package com.drxin.framework.web.service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import javax.servlet.http.HttpServletRequest;

import com.alibaba.fastjson2.JSONObject;
import com.drxin.common.core.domain.model.TokenPair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.drxin.common.constant.CacheConstants;
import com.drxin.common.constant.Constants;
import com.drxin.common.core.domain.model.LoginUser;
import com.drxin.common.core.redis.RedisCache;
import com.drxin.common.utils.ServletUtils;
import com.drxin.common.utils.StringUtils;
import com.drxin.common.utils.ip.AddressUtils;
import com.drxin.common.utils.ip.IpUtils;
import com.drxin.common.utils.uuid.IdUtils;
import eu.bitwalker.useragentutils.UserAgent;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

/**
 * 双token验证处理
 *
 * <AUTHOR>
 */
@Component
public class TokenService {
    private static final Logger log = LoggerFactory.getLogger(TokenService.class);

    // 令牌自定义标识
    @Value("${token.header}")
    private String header;

    // 刷新令牌自定义标识
    @Value("${token.refreshHeader:refresh-token}")
    private String refreshHeader;

    // 令牌秘钥
    @Value("${token.secret}")
    private String secret;

    // 访问令牌有效期（默认30分钟）
    @Value("${token.expireTime}")
    private int expireTime;

    // 刷新令牌有效期（默认7天）
    @Value("${token.refreshExpireTime:7}")
    private int refreshExpireTime;

    protected static final long MILLIS_SECOND = 1000;
    protected static final long MILLIS_MINUTE = 60 * MILLIS_SECOND;
    private static final Long MILLIS_MINUTE_FIVE = 20 * 60 * 1000L; // 5分钟内自动刷新

    @Autowired
    private RedisCache redisCache;
    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser(HttpServletRequest request) {
        // 获取请求携带的令牌
        String token = getToken(request);
        if (StringUtils.isNotEmpty(token)) {
            try {
                Claims claims = parseToken(token);
                // 解析对应的权限以及用户信息
                String uuid = (String) claims.get(Constants.LOGIN_USER_KEY);
                String userKey = getTokenKey(uuid);
                LoginUser loginUser = redisCache.getCacheObject(userKey);

                if (loginUser != null) {
                    // 检查是否需要自动刷新token
                    checkAndRefreshToken(loginUser, request);
                }

                return loginUser;
            } catch (Exception e) {
                log.error("获取用户信息异常'{}'", e.getMessage());
            }
        }
        return null;
    }

    /**
     * 设置用户身份信息
     */
    public void setLoginUser(LoginUser loginUser) {
        if (StringUtils.isNotNull(loginUser) && StringUtils.isNotEmpty(loginUser.getToken())) {
            refreshToken(loginUser);
        }
    }

    private void deleteOldToken(String username) {
        String userTokenKey = getUserTokenKey(username);
        JSONObject oldTokenMap = redisCache.getCacheObject(userTokenKey);
        if (oldTokenMap != null) {
            String oldAccessToken = oldTokenMap.getString("access");
            String oldRefreshToken = oldTokenMap.getString("refresh");
            if (StringUtils.isNotBlank(oldAccessToken)) {
                redisCache.deleteObject(getTokenKey(oldAccessToken));
            }
            if (StringUtils.isNotBlank(oldRefreshToken)) {
                redisCache.deleteObject(getRefreshTokenKey(oldRefreshToken));
            }
        }

    }

    /**
     * 删除用户身份信息
     */
    public void delLoginUser(String token) {
        if (StringUtils.isNotEmpty(token)) {
            String userKey = getTokenKey(token);
            LoginUser loginUser = redisCache.getCacheObject(userKey);
            if (loginUser != null && StringUtils.isNotEmpty(loginUser.getRefreshToken())) {
                // 删除刷新token
                String refreshKey = getRefreshTokenKey(loginUser.getRefreshToken());
                redisCache.deleteObject(refreshKey);
                redisCache.deleteObject(getUserTokenKey(loginUser.getUsername()));
            }
            // 删除访问token
            redisCache.deleteObject(userKey);
        }
    }

    /**
     * 创建双令牌
     *
     * @param loginUser 用户信息
     * @return TokenPair 包含访问token和刷新token
     */
    public TokenPair createTokenPair(LoginUser loginUser) {
        // 删除旧的token
        deleteOldToken(loginUser.getUsername());

        String accessToken = IdUtils.fastUUID();
        String refreshToken = IdUtils.fastUUID();

        loginUser.setToken(accessToken);
        loginUser.setRefreshToken(refreshToken);
        setUserAgent(loginUser);

        // 设置访问token过期时间
        refreshToken(loginUser);

        // 缓存刷新token（有效期更长）
        String refreshKey = getRefreshTokenKey(refreshToken);
        redisCache.setCacheObject(refreshKey, loginUser, refreshExpireTime, TimeUnit.DAYS);
        // 缓存 username -> token 对应关系
        String userTokenKey = getUserTokenKey(loginUser.getUsername());
        JSONObject newTokenMap = new JSONObject();
        newTokenMap.put("access", accessToken);
        newTokenMap.put("refresh", refreshToken);
        redisCache.setCacheObject(userTokenKey, newTokenMap, refreshExpireTime, TimeUnit.DAYS);
        // 创建JWT访问token
        Map<String, Object> claims = new HashMap<>();
        claims.put(Constants.LOGIN_USER_KEY, accessToken);
        claims.put(Constants.JWT_USERNAME, loginUser.getUsername());
        String jwtAccessToken = createToken(claims);

        // 创建JWT刷新token
        Map<String, Object> refreshClaims = new HashMap<>();
        refreshClaims.put(Constants.LOGIN_USER_KEY, refreshToken);
        refreshClaims.put(Constants.JWT_USERNAME, loginUser.getUsername());
        refreshClaims.put("type", "refresh");
        String jwtRefreshToken = createToken(refreshClaims);

        return new TokenPair(jwtAccessToken, jwtRefreshToken);
    }

    /**
     * 使用刷新token获取新的访问token
     *
     * @param refreshToken 刷新token
     * @return TokenPair 新的token对
     */
    public TokenPair refreshAccessToken(String refreshToken) {
        try {
            Claims claims = parseToken(refreshToken);
            String tokenId = (String) claims.get(Constants.LOGIN_USER_KEY);
            String type = (String) claims.get("type");

            if (!"refresh".equals(type)) {
                log.error("无效的刷新token类型");
                return null;
            }

            // 验证刷新token是否存在且有效
            String refreshKey = getRefreshTokenKey(tokenId);
            LoginUser cachedLoginUser = redisCache.getCacheObject(refreshKey);

            if (cachedLoginUser == null) {
                log.error("刷新token已过期或无效");
                return null;
            }
            // 删除旧的刷新token
            redisCache.deleteObject(refreshKey);
            // 创建新的token对
            return createTokenPair(cachedLoginUser);

        } catch (Exception e) {
            log.error("刷新token异常: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 检查并自动刷新token
     */
    private void checkAndRefreshToken(LoginUser loginUser, HttpServletRequest request) {
        long expireTime = loginUser.getExpireTime();
        long currentTime = System.currentTimeMillis();

        // 如果访问token在5分钟内过期，自动刷新
        if (expireTime - currentTime <= MILLIS_MINUTE_FIVE) {
            String refreshTokenHeader = request.getHeader(refreshHeader);
            if (StringUtils.isNotEmpty(refreshTokenHeader)) {
                TokenPair newTokenPair = refreshAccessToken(refreshTokenHeader);
                if (newTokenPair != null) {
                    // 在响应头中返回新的token
                    ServletUtils.getResponse().setHeader(header, Constants.TOKEN_PREFIX + newTokenPair.getAccessToken());
                    ServletUtils.getResponse().setHeader(refreshHeader, Constants.TOKEN_PREFIX + newTokenPair.getRefreshToken());
                    log.info("自动刷新token成功，用户: {}", loginUser.getUsername());
                }
            }
        }
    }

    /**
     * 验证令牌有效期，相差不足5分钟，自动刷新缓存
     *
     * @param loginUser 登录信息
     */
    public void verifyToken(LoginUser loginUser) {
        long expireTime = loginUser.getExpireTime();
        long currentTime = System.currentTimeMillis();
        if (expireTime - currentTime <= MILLIS_MINUTE_FIVE) {
            refreshToken(loginUser);
        }
    }

    /**
     * 刷新令牌有效期
     *
     * @param loginUser 登录信息
     */
    public void refreshToken(LoginUser loginUser) {
        loginUser.setLoginTime(System.currentTimeMillis());
        loginUser.setExpireTime(loginUser.getLoginTime() + expireTime * MILLIS_MINUTE);
        // 根据uuid将loginUser缓存
        String userKey = getTokenKey(loginUser.getToken());
        redisCache.setCacheObject(userKey, loginUser, expireTime, TimeUnit.MINUTES);
    }

    /**
     * 设置用户代理信息
     *
     * @param loginUser 登录信息
     */
    public void setUserAgent(LoginUser loginUser) {
        UserAgent userAgent = UserAgent.parseUserAgentString(ServletUtils.getRequest().getHeader("User-Agent"));
        String ip = IpUtils.getIpAddr();
        loginUser.setIpaddr(ip);
        loginUser.setLoginLocation(AddressUtils.getRealAddressByIP(ip));
        loginUser.setBrowser(userAgent.getBrowser().getName());
        loginUser.setOs(userAgent.getOperatingSystem().getName());
    }

    /**
     * 创建令牌
     *
     * @param loginUser 用户信息
     * @return 令牌
     */
    public String createToken(LoginUser loginUser) {
        String token = IdUtils.fastUUID();
        loginUser.setToken(token);
        setUserAgent(loginUser);
        refreshToken(loginUser);

        Map<String, Object> claims = new HashMap<>();
        claims.put(Constants.LOGIN_USER_KEY, token);
        claims.put(Constants.JWT_USERNAME, loginUser.getUsername());
        return createToken(claims);
    }



    /**
     * 从数据声明生成令牌
     *
     * @param claims 数据声明
     * @return 令牌
     */
    private String createToken(Map<String, Object> claims) {
        return Jwts.builder()
                .setClaims(claims)
                .signWith(SignatureAlgorithm.HS512, secret)
                .compact();
    }

    /**
     * 从令牌中获取数据声明
     *
     * @param token 令牌
     * @return 数据声明
     */
    private Claims parseToken(String token) {
        return Jwts.parser()
                .setSigningKey(secret)
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * 从令牌中获取用户名
     *
     * @param token 令牌
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        Claims claims = parseToken(token);
        return claims.getSubject();
    }

    /**
     * 获取请求token
     *
     * @param request
     * @return token
     */
    private String getToken(HttpServletRequest request) {
        String token = request.getHeader(header);
        if (StringUtils.isNotEmpty(token) && token.startsWith(Constants.TOKEN_PREFIX)) {
            token = token.replace(Constants.TOKEN_PREFIX, "");
        }
        return token;
    }

    /**
     * 获取刷新token
     */
    private String getRefreshToken(HttpServletRequest request) {
        String token = request.getHeader(refreshHeader);
        if (StringUtils.isNotEmpty(token) && token.startsWith(Constants.TOKEN_PREFIX)) {
            token = token.replace(Constants.TOKEN_PREFIX, "");
        }
        return token;
    }

    private String getTokenKey(String uuid) {
        return CacheConstants.LOGIN_TOKEN_KEY + uuid;
    }

    private String getRefreshTokenKey(String uuid) {
        return CacheConstants.REFRESH_TOKEN_KEY + uuid;
    }

    private String getUserTokenKey(String username) {
        return CacheConstants.USER_TOKEN_KEY + username;
    }

}