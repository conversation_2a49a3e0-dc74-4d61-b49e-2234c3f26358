package com.drxin.framework.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.Map;

@Getter
public class ContributionActionEvent extends ApplicationEvent {
    private final Long userId;
    private final String actionCode;
    private final Map<String, Object> context;

    public ContributionActionEvent(Object source, Long userId, String actionCode, Map<String, Object> context) {
        super(source);
        this.userId = userId;
        this.actionCode = actionCode;
        this.context = context;
    }
}
