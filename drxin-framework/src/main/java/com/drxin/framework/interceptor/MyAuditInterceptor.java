package com.drxin.framework.interceptor;

import com.drxin.common.core.domain.model.LoginUser;
import com.drxin.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;

import java.lang.reflect.Field;
import java.util.Date;
import java.util.Map;
import java.util.Properties;

@Slf4j
@Intercepts(value = {
        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
})
public class MyAuditInterceptor implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        Object parameter = invocation.getArgs()[1];

        // 获取SQL操作类型
        SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();
        String currentUser = getCurrentUserOrDefault();
        if (parameter !=null && hasRequiredFields(parameter)) {
            // 根据SQL操作类型判断是插入还是更新
            if (SqlCommandType.INSERT.equals(sqlCommandType)) {
                // 插入操作，自动填充创建人和创建时间
                fillField(parameter, "createBy", currentUser);
                fillField(parameter, "createTime", new Date());
            } else if (SqlCommandType.UPDATE.equals(sqlCommandType)) {
                // 更新操作，自动填充更新人和更新时间
                fillField(parameter, "updateBy", currentUser);
                fillField(parameter, "updateTime", new Date());
            }
        }
        return invocation.proceed();
    }
    // 反射方式填充字段值
    private void fillField(Object parameter, String fieldName, Object value) {
        if (parameter instanceof Map) {
            Map<?, ?> paramMap = (Map<?, ?>) parameter;
            paramMap.forEach((k, v) -> fillField(v, fieldName, value));
        } else {
            try {
                Field field = getFieldRecursive(parameter.getClass(), fieldName);
                if (field != null) {
                    field.setAccessible(true);
                    field.set(parameter, value);
                }
            } catch (IllegalAccessException ignored) {
            }
        }
    }

    private Field getFieldRecursive(Class<?> clazz, String fieldName) {
        while (clazz != null) {
            try {
                return clazz.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass(); // 继续往父类查找
            }
        }
        return null; // 没有找到
    }
    private String getCurrentUser() {
        // 获取当前用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        return loginUser.getUsername();
    }

    private boolean hasRequiredFields(Object entity) {
        return hasField(entity.getClass(), "createBy")
                && hasField(entity.getClass(), "createTime")
                && hasField(entity.getClass(), "updateBy")
                && hasField(entity.getClass(), "updateTime");
    }

    private boolean hasField(Class<?> clazz, String fieldName) {
        while (clazz != null) {
            try {
                clazz.getDeclaredField(fieldName);
                return true; // 找到了就返回 true
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass(); // 继续向父类查找
            }
        }
        return false; // 一直没找到
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
    }

    private String getCurrentUserOrDefault() {
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser(); // 尝试获取当前登录用户
            return loginUser != null ? loginUser.getUsername() : null;  // 如果无法获取用户，返回默认值
        } catch (Exception e) {
            // 如果发生异常，返回默认值
            log.warn("获取用户信息失败，使用空值");
            return null;
        }
    }
}
