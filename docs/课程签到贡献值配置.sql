-- 课程签到贡献值配置初始化SQL
-- 插入课程签到贡献值配置

INSERT INTO contribution_action_config (
    code, 
    name, 
    score, 
    average_flag, 
    enabled, 
    remark, 
    create_by, 
    create_time
) VALUES (
    'COURSE_CHECKIN', 
    '课程签到', 
    10.00, 
    'N', 
    'Y', 
    '用户参与课程签到获得的贡献值', 
    'admin', 
    NOW()
) ON DUPLICATE KEY UPDATE 
    name = VALUES(name),
    score = VALUES(score),
    average_flag = VALUES(average_flag),
    enabled = VALUES(enabled),
    remark = VALUES(remark),
    update_by = 'admin',
    update_time = NOW();
