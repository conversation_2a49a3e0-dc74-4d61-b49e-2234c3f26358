-- 急救案例通过贡献值配置初始化SQL

-- 急救案例通过贡献值配置
INSERT INTO contribution_action_config (
    code, 
    name, 
    score, 
    average_flag, 
    enabled, 
    remark, 
    create_by, 
    create_time
) VALUES (
    'RESCUE_CASE_APPROVED', 
    '急救案例通过', 
    30.00, 
    'Y',  -- 需要平均分配，多个急救人员共享贡献值
    'Y', 
    '急救案例审核通过，参与急救的人员获得的贡献值', 
    'admin', 
    NOW()
) ON DUPLICATE KEY UPDATE 
    name = VALUES(name),
    score = VALUES(score),
    average_flag = VALUES(average_flag),
    enabled = VALUES(enabled),
    remark = VALUES(remark),
    update_by = 'admin',
    update_time = NOW();

-- 说明：
-- 1. 急救案例通过设置为平均分配模式（average_flag = 'Y'）
-- 2. 总分值30分，如果有3个急救人员参与，每人获得10分
-- 3. 如果只有1个急救人员，则获得全部30分
-- 4. 这样设计鼓励团队协作，同时保证单人急救也有足够的激励
