# 贡献值事件日志系统说明

## 功能概述

为了防止贡献值策略处理失败后无记录可查，新增了事件日志系统，记录所有贡献值事件的处理状态。

## 核心特性

### 1. 全程记录
- **事件接收**：监听器接收事件时立即记录
- **处理状态**：实时更新处理成功/失败状态
- **错误信息**：记录详细的失败原因

### 2. 数据完整性
- **上下文保存**：完整保存事件的context数据
- **来源追踪**：记录事件发布的来源类

### 3. 监控能力
- **状态查询**：可查询待处理、成功、失败的事件
- **时间追踪**：记录创建时间和处理时间

## 数据库表结构

### contribution_event_log 表
```sql
CREATE TABLE `contribution_event_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `action_code` varchar(50) NOT NULL COMMENT '行为编码',
  `event_source` varchar(100) DEFAULT NULL COMMENT '事件来源类',
  `context_data` text COMMENT '事件上下文数据(JSON格式)',
  `process_status` char(1) NOT NULL DEFAULT '0' COMMENT '处理状态',
  `process_message` varchar(500) DEFAULT NULL COMMENT '处理结果消息',
  `process_time` datetime DEFAULT NULL COMMENT '处理时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action_code` (`action_code`),
  KEY `idx_process_status` (`process_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 处理状态说明
- **0**: 待处理 - 事件已记录，等待策略处理
- **1**: 处理成功 - 策略处理成功，贡献值已计算和记录
- **2**: 处理失败 - 策略处理失败，需要人工干预或重试

## 处理流程

### 1. 事件接收阶段
```java
@EventListener
public void handle(ContributionActionEvent event) {
    // 1. 立即记录事件日志（状态：待处理）
    Long eventLogId = contributionEventLogService.recordEventLog(event);
    
    try {
        // 2. 获取策略并处理
        ContributionStrategy strategy = strategyFactory.getStrategy(actionCode);
        strategy.process(event);
        
        // 3. 更新状态为成功
        contributionEventLogService.updateEventSuccess(eventLogId, "策略处理成功");
        
    } catch (Exception e) {
        // 4. 更新状态为失败
        contributionEventLogService.updateEventFailed(eventLogId, e.getMessage());
    }
}
```

### 2. 数据记录示例
```json
{
  "id": 1,
  "user_id": 123,
  "action_code": "COURSE_CHECKIN",
  "event_source": "WxCourseAttendanceServiceImpl",
  "context_data": "{\"courseId\":1001,\"attendanceId\":5001,\"attendTime\":\"2025-07-10 14:30:00\"}",
  "process_status": "1",
  "process_message": "策略处理成功",
  "process_time": "2025-07-10 14:30:05",
  "create_time": "2025-07-10 14:30:01",
  "update_time": "2025-07-10 14:30:05"
}
```

### 3. 批量处理示例
```json
{
  "id": 2,
  "user_id": 101,
  "action_code": "ACTIVITY_ORGANIZER",
  "event_source": "ActivityServiceImpl",
  "context_data": "{\"activityId\":2001,\"attendeeCount\":80,\"activityType\":\"TRAINING\",\"userIds\":[101,102,103]}",
  "process_status": "1",
  "process_message": "策略处理成功",
  "process_time": "2025-07-10 15:00:10",
  "create_time": "2025-07-10 15:00:01",
  "update_time": "2025-07-10 15:00:10"
}
```

## 监控和查询

### 1. 查询待处理事件
```java
List<ContributionEventLog> pendingEvents = 
    contributionEventLogService.selectPendingEventLogs(100);
```

### 2. 查询失败事件
```java
List<ContributionEventLog> failedEvents =
    contributionEventLogService.selectFailedEventLogs(50); // 查询50条失败事件
```

### 3. SQL查询示例
```sql
-- 查询今天的事件处理统计
SELECT 
    action_code,
    process_status,
    COUNT(*) as count
FROM contribution_event_log 
WHERE DATE(create_time) = CURDATE()
GROUP BY action_code, process_status;

-- 查询处理失败的事件
SELECT * FROM contribution_event_log 
WHERE process_status = '2' 
ORDER BY create_time DESC;

-- 查询某个用户的贡献值事件记录
SELECT * FROM contribution_event_log 
WHERE user_id = 123 
ORDER BY create_time DESC;
```



## 运维监控

### 1. 关键指标
- **事件总量**：每日处理的事件数量
- **成功率**：处理成功的事件比例
- **失败率**：处理失败的事件比例
- **平均处理时间**：从接收到处理完成的时间

### 2. 告警规则
- 处理失败率超过5%时告警
- 待处理事件积压超过100条时告警
- 失败事件数量过多时告警

### 3. 清理策略
```sql
-- 清理30天前的成功事件记录
DELETE FROM contribution_event_log
WHERE process_status = '1'
AND create_time < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 保留失败事件记录更长时间（90天）用于问题排查
DELETE FROM contribution_event_log
WHERE process_status = '2'
AND create_time < DATE_SUB(NOW(), INTERVAL 90 DAY);
```

## 优势总结

1. **数据完整性**：确保所有事件都有记录，不会丢失
2. **问题追踪**：失败事件有详细的错误信息，便于排查
3. **监控能力**：提供完整的事件处理监控数据
4. **运维友好**：支持查询、统计、清理等运维操作
5. **简洁高效**：去除复杂的重试机制，专注于记录和监控

通过这套事件日志系统，可以确保贡献值处理的可靠性和可追溯性，大大提升系统的稳定性和可维护性。
