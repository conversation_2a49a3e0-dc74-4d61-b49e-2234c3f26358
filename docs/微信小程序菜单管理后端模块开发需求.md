# 微信小程序菜单管理后端模块开发需求

## 项目背景

开发一个忻道中医生命急救三分钟管理平台的后端菜单管理模块，支持基于用户角色的动态菜单权限控制。

## 核心功能需求

### 1. 用户角色管理

#### 角色类型
- `general`: 普通用户
- `aider`: 急救员  
- `mentor`: 急救导师
- `disciple`: 弟子

#### 角色特性
- **支持多角色**：用户可以同时拥有多个角色（逗号分隔存储）
- **角色权限**：不同角色可访问不同的菜单项
- **角色升级**：普通用户可申请成为急救员，急救员可申请成为导师

### 2. 菜单配置管理

系统支持两类菜单：

#### 个人设置菜单
通过右上角设置按钮进入的个人配置菜单：

```json
[
  {
    "id": 1,
    "title": "账户设置",
    "icon": "setting-fill",
    "path": "/pageA/profile",
    "showFor": ["all"],
    "description": "用户账户基本设置"
  },
  {
    "id": 2,
    "title": "个人资料",
    "icon": "account-fill", 
    "path": "/pageA/personal",
    "showFor": ["aider", "mentor", "disciple"],
    "description": "完善个人详细信息"
  },
  {
    "id": 3,
    "title": "地址管理",
    "icon": "map-fill",
    "path": "/pageA/user_address/list",
    "showFor": ["aider", "mentor", "disciple"],
    "description": "管理收货地址信息"
  },
  {
    "id": 4,
    "title": "我的邀请码",
    "icon": "share-fill",
    "path": "/pageA/invite_code",
    "showFor": ["mentor", "disciple"],
    "description": "查看和分享邀请码"
  }
]
```

#### 功能菜单
直接在主页面显示的核心功能菜单：

```json
[
  {
    "id": 4,
    "title": "证件申请",
    "icon": "order-fill",
    "path": "/pageA/apply/list",
    "showFor": ["aider", "mentor", "disciple"],
    "description": "申请相关资质证件"
  },
  {
    "id": 7,
    "title": "活动上报",
    "icon": "calendar-fill",
    "path": "/pageA/activity_review/list",
    "showFor": ["aider", "mentor", "disciple"],
    "description": "上报和管理活动信息"
  },
  {
    "id": 8,
    "title": "急救案例",
    "icon": "heart-fill",
    "path": "/pageA/rescue_case/list",
    "showFor": ["aider", "mentor", "disciple"],
    "description": "记录和分享急救案例"
  },
  {
    "id": 1001,
    "title": "急救员申请",
    "icon": "plus-circle-fill",
    "path": "/pageA/user_apply/index?userType=aider",
    "showFor": ["general"],
    "description": "普通用户申请成为急救员"
  },
  {
    "id": 1002,
    "title": "导师申请",
    "icon": "star-fill",
    "path": "/pageA/user_apply/index?userType=mentor",
    "showFor": ["general", "aider"],
    "description": "申请成为急救导师"
  }
]
```

### 3. API接口需求

#### 基础菜单接口
```
GET /wx/menu/personal
- 描述: 获取个人设置菜单
- 权限: 需要登录
- 返回: 根据用户角色过滤的个人设置菜单列表

GET /wx/menu/function
- 描述: 获取功能菜单
- 权限: 需要登录
- 返回: 根据用户角色过滤的功能菜单列表

GET /wx/menu/all
- 描述: 获取用户所有可访问菜单
- 权限: 需要登录
- 返回: 包含个人设置和功能菜单的完整列表
```

#### 管理员接口
```
POST /bizz/wx_menu
- 描述: 创建菜单项
- 权限: 管理员
- 参数: title, icon, path, menu_type, show_for, sort_order

PUT /bizz/wx_menu/{id}
- 描述: 更新菜单项
- 权限: 管理员
- 参数: 同创建接口

DELETE /bizz/wx_menu/{id}
- 描述: 删除菜单项
- 权限: 管理员

GET /bizz/wx_menu/manage
- 描述: 获取所有菜单（管理后台用）
- 权限: 管理员
```

#### 权限验证接口
```
GET /api/menu/check/{menuId}
- 描述: 检查用户是否有权限访问指定菜单
- 权限: 需要登录
- 返回: boolean

GET /api/user/roles
- 描述: 获取当前用户角色信息
- 权限: 需要登录
- 返回: 用户角色列表和权限信息
```

### 4. 数据库设计

#### 菜单表 (menus)
```sql
CREATE TABLE wx_menus (
  id INT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(50) NOT NULL COMMENT '菜单名称',
  icon VARCHAR(50) NOT NULL COMMENT '图标名称',
  path VARCHAR(200) NOT NULL COMMENT '页面路径',
  menu_type VARCHAR(200) NOT NULL COMMENT '菜单类型，如 personal/function',
  show_for VARCHAR(200) COMMENT '显示给哪些角色，如 general,aider',
  sort_order INT DEFAULT 0 COMMENT '排序权重',
  status TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用',
  description VARCHAR(200) COMMENT '菜单描述',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_menu_type (menu_type),
  INDEX idx_status (status),
  INDEX idx_sort_order (sort_order)
);
```

#### 用户角色表（使用现有表 `user_role`）

- 用户角色信息来源于已有的 `user_role` 表；

- 每条记录代表用户与某个角色的关联关系；

- 用户可以拥有多个角色（多条记录）；

- 表结构示例（仅做说明）：

  ```
  create table sys_user_role
  (
      user_id bigint not null comment '用户ID',
      role_id bigint not null comment '角色ID',
      primary key (user_id, role_id)
  )
      comment '用户和角色关联表';
  
  
  ```

  

#### 菜单访问日志表 (menu_access_logs)
```sql
CREATE TABLE wx_menu_access_logs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  menu_id INT NOT NULL,
  access_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  user_agent VARCHAR(500),
  ip_address VARCHAR(45),
  
  INDEX idx_user_id (user_id),
  INDEX idx_menu_id (menu_id),
  INDEX idx_access_time (access_time)
);
```

### 5. 业务逻辑要求

#### 权限过滤逻辑（基于现有 `user_role` 表）

1. **获取用户角色**：从 `user_role` 表中查询指定 `user_id` 的所有角色记录；
2. **角色集合构建**：将查询结果中的所有 `role_code` 转换为角色列表；
3. **菜单过滤**：菜单项的 `show_for` 字段为英文逗号分隔的角色列表（如 `"aider,mentor"`）；
4. **权限匹配**：
   - 若用户的任一角色存在于菜单的 `show_for` 中，即可展示该菜单；
   - 如果菜单 `show_for` 包含 `"all"`，则对所有登录用户展示；
5. **返回前端菜单**：只返回当前用户具备访问权限的菜单项；
6. **缓存优化建议**：用户角色列表与菜单配置应分别缓存，减少频繁数据库查询。

#### 响应格式标准
```json
{
  "code": 200,
  "message": "success", 
  "data":
    [
      {
        "id": 4,
        "title": "证件申请", 
        "icon": "order-fill",
        "path": "/pageA/apply/list",
        "description": "申请相关资质证件"
      }
    ]
}
```

#### 错误处理
```json
{
  "code": 401,
  "message": "未授权访问",
  "data": null,
  "timestamp": 1642147200000
}
```

### 6. 技术要求

#### 安全要求
- **JWT Token验证**：所有接口需要验证用户身份
- **权限控制**：严格按照角色权限过滤菜单
- **SQL注入防护**：使用参数化查询
- **XSS防护**：对输入进行转义处理

#### 性能要求  
- **缓存机制**：使用Redis缓存菜单配置和用户角色
- **缓存策略**：菜单配置缓存1小时，用户角色缓存30分钟
- **数据库优化**：添加必要索引，优化查询性能
- **接口响应时间**：< 200ms

#### 可维护性
- **配置热更新**：支持菜单配置动态更新，无需重启服务
