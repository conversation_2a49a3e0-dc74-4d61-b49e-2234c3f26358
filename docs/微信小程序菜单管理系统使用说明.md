# 微信小程序菜单管理系统使用说明

## 概述

微信小程序菜单管理系统是一个基于角色权限的动态菜单管理模块，支持根据用户角色动态显示不同的菜单项。

## 功能特性

- **角色权限控制**：基于用户角色动态过滤菜单
- **菜单类型分类**：支持个人设置菜单和功能菜单

- **管理后台**：提供完整的菜单管理功能


## 数据库表结构

### 菜单表 (wx_menus)
```sql
CREATE TABLE wx_menus (
  id INT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(50) NOT NULL COMMENT '菜单名称',
  icon VARCHAR(50) NOT NULL COMMENT '图标名称',
  path VARCHAR(200) NOT NULL COMMENT '页面路径',
  menu_type VARCHAR(20) NOT NULL COMMENT '菜单类型：personal/function',
  show_for VARCHAR(200) COMMENT '显示给哪些角色，如 general,aider',
  sort_order INT DEFAULT 0 COMMENT '排序权重',
  status TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用',
  description VARCHAR(200) COMMENT '菜单描述',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```



## API接口

### 微信端接口 (/wx/menu)

#### 1. 根据类型获取菜单列表
```
GET /wx/menu/list?menuType=personal
GET /wx/menu/list?menuType=function
```
参数说明：
- `menuType`: 菜单类型，支持 `personal`（个人设置菜单）和 `function`（功能菜单）

#### 2. 检查菜单权限
```
GET /wx/menu/check/{menuId}
```
检查当前用户是否有权限访问指定菜单。

#### 3. 获取用户角色信息
```
GET /wx/menu/user/roles
```
获取当前用户的角色信息。

### 管理端接口 (/bizz/wx_menu)

#### 1. 查询菜单列表
```
GET /bizz/wx_menu/list
```

#### 2. 获取菜单详情
```
GET /bizz/wx_menu/{id}
```

#### 3. 新增菜单
```
POST /bizz/wx_menu
```

#### 4. 修改菜单
```
PUT /bizz/wx_menu/{id}
```

#### 5. 删除菜单
```
DELETE /bizz/wx_menu/{ids}
```

## 权限配置

### 角色类型
- `general`: 普通用户
- `aider`: 急救员
- `mentor`: 急救导师
- `disciple`: 弟子

### 菜单权限配置
在菜单的 `show_for` 字段中配置角色权限：
- `"all"`: 所有登录用户可访问
- `"general"`: 仅普通用户可访问
- `"aider,mentor"`: 急救员和导师可访问
- `"mentor,disciple"`: 导师和弟子可访问

## 使用示例

### 1. 前端调用示例
```javascript
// 获取个人设置菜单
wx.request({
  url: '/wx/menu/list?menuType=personal',
  method: 'GET',
  header: {
    'Authorization': 'Bearer ' + token
  },
  success: function(res) {
    console.log('个人设置菜单:', res.data.data);
  }
});

// 获取功能菜单
wx.request({
  url: '/wx/menu/list?menuType=function',
  method: 'GET',
  header: {
    'Authorization': 'Bearer ' + token
  },
  success: function(res) {
    console.log('功能菜单:', res.data.data);
  }
});

// 检查菜单权限
wx.request({
  url: '/wx/menu/check/1',
  method: 'GET',
  header: {
    'Authorization': 'Bearer ' + token
  },
  success: function(res) {
    if (res.data.data) {
      console.log('有权限访问该菜单');
    } else {
      console.log('无权限访问该菜单');
    }
  }
});
```

### 2. 后端服务调用示例
```java
@Autowired
private IWxMenuService wxMenuService; // system模块的服务

// 获取用户个人设置菜单
public List<WxMenuVo> getUserPersonalMenus(Long userId) {
    List<SysRole> roles = roleService.selectRolesByUserId(userId);
    return wxMenuService.getMenusByType("personal", roles);
}

// 获取用户功能菜单
public List<WxMenuVo> getUserFunctionMenus(Long userId) {
    List<SysRole> roles = roleService.selectRolesByUserId(userId);
    return wxMenuService.getMenusByType("function", roles);
}

// 检查菜单权限
public boolean checkUserMenuPermission(Long userId, Integer menuId) {
    List<SysRole> roles = roleService.selectRolesByUserId(userId);
    return wxMenuService.checkMenuPermission(menuId, roles);
}
```

## 部署说明

1. **执行数据库脚本**
   ```bash
   mysql -u username -p database_name < sql/wx_menu_tables.sql
   ```

2. **配置权限**
   在系统权限管理中添加相应的权限配置。

3. **重启服务**
   重启应用服务使配置生效。

## 注意事项

1. **权限控制**：所有菜单接口都需要用户登录，系统会自动根据用户角色过滤菜单。

2. **缓存策略**：建议在生产环境中对菜单配置和用户角色信息进行缓存优化。




