# 统一事件贡献值系统使用示例

## 设计理念

使用统一的 `ContributionActionEvent`，通过 `context` 传递所有必要数据，包括批量用户信息。

## 核心特性

- **统一事件**：只使用 `ContributionActionEvent`
- **context驱动**：所有数据通过context传递
- **策略智能**：自动识别单个/批量处理
- **平均分配**：支持配置和强制控制

## 使用方法

### 1. 单个用户贡献值

```java
// 课程签到示例
@Service
public class CourseService {
    
    @Resource
    private ApplicationEventPublisher eventPublisher;
    
    public void recordAttendance(Long userId, Long courseId) {
        // 保存签到记录
        WxCourseAttendance attendance = saveAttendance(userId, courseId);
        
        // 发布贡献值事件
        Map<String, Object> context = new HashMap<>();
        context.put("attendanceId", attendance.getId());
        context.put("attendTime", attendance.getAttendTime());
        context.put("courseId", courseId);
        
        ContributionActionEvent event = new ContributionActionEvent(
            this, userId, "COURSE_CHECKIN", context);
        eventPublisher.publishEvent(event);
    }
}
```

### 2. 批量用户贡献值

```java
// 活动主办人示例
@Service
public class ActivityService {
    
    @Resource
    private ApplicationEventPublisher eventPublisher;
    
    public void assignOrganizerContribution(Long activityId, List<Long> organizerIds) {
        // 准备上下文数据
        Map<String, Object> context = new HashMap<>();
        context.put("activityId", activityId);
        context.put("attendeeCount", 80);
        context.put("activityType", "TRAINING");
        context.put("durationHours", 6);
        context.put("activityRating", 4.6);
        
        // 关键：批量用户数据
        context.put("userIds", organizerIds);  // 批量用户ID列表
        context.put("averageDistribution", true);  // 强制平均分配
        
        // 发布统一事件（使用第一个用户ID作为代表）
        ContributionActionEvent event = new ContributionActionEvent(
            this, organizerIds.get(0), "ACTIVITY_ORGANIZER", context);
        eventPublisher.publishEvent(event);
    }
}
```

### 3. 救援案例批量分配

```java
// 救援人员贡献值分配
@Service
public class RescueService {
    
    public void assignRescueContribution(Long rescueCaseId, List<Long> rescuerIds) {
        Map<String, Object> context = new HashMap<>();
        context.put("rescueCaseId", rescueCaseId);
        context.put("difficulty", "HIGH");
        context.put("successful", true);
        context.put("rescueHours", 12);
        
        // 批量数据
        context.put("userIds", rescuerIds);
        context.put("averageDistribution", true);  // 平均分配
        
        ContributionActionEvent event = new ContributionActionEvent(
            this, rescuerIds.get(0), "RESCUE_CASE", context);
        eventPublisher.publishEvent(event);
    }
}
```

## 策略实现

### 活动主办策略完整示例

```java
@Slf4j
@Component
public class ActivityOrganizerContributionStrategy implements ContributionStrategy {

    @Resource
    private IContributionActionConfigService contributionActionConfigService;
    
    @Resource
    private IContributionLogService contributionLogService;
    
    @Resource
    private ISysUserService userService;

    @Override
    public String getSupportAction() {
        return "ACTIVITY_ORGANIZER";
    }

    @Override
    public void process(ContributionActionEvent event) {
        try {
            // 检查是否为批量处理
            @SuppressWarnings("unchecked")
            List<Long> userIds = (List<Long>) event.getContext().get("userIds");
            
            if (userIds != null && !userIds.isEmpty()) {
                // 批量处理
                processBatchUsers(event, userIds);
            } else {
                // 单个用户处理
                processSingleUser(event);
            }
            
        } catch (Exception e) {
            log.error("处理活动主办贡献值失败: userId={}", event.getUserId(), e);
        }
    }

    private void processBatchUsers(ContributionActionEvent event, List<Long> userIds) {
        log.info("开始处理批量活动主办贡献值: userCount={}", userIds.size());

        // 1. 获取配置
        ContributionActionConfig config = getActionConfig();
        if (config == null) {
            log.warn("未找到活动主办贡献值配置或配置已禁用");
            return;
        }

        // 2. 计算总贡献值
        double totalScore = calculateScore(event);
        if (totalScore <= 0) {
            log.info("总贡献值为0或负数，忽略处理: totalScore={}", totalScore);
            return;
        }

        // 3. 根据配置决定分配方式
        Boolean forceAverage = (Boolean) event.getContext().get("averageDistribution");
        boolean shouldAverage = "Y".equals(config.getAverageFlag()) || Boolean.TRUE.equals(forceAverage);
        double scorePerUser = shouldAverage && userIds.size() > 1 ? 
            totalScore / userIds.size() : totalScore;

        // 4. 为每个用户记录贡献值日志
        for (Long userId : userIds) {
            recordContributionLog(userId, event, scorePerUser);
        }

        log.info("批量活动主办贡献值处理完成: userCount={}, totalScore={}, scorePerUser={}", 
            userIds.size(), totalScore, scorePerUser);
    }

    private void processSingleUser(ContributionActionEvent event) {
        log.info("开始处理单个活动主办贡献值: userId={}", event.getUserId());

        double score = calculateScore(event);
        if (score <= 0) {
            log.info("贡献值为0或负数，不记录日志: userId={}, score={}", event.getUserId(), score);
            return;
        }

        recordContributionLog(event.getUserId(), event, score);
        log.info("单个活动主办贡献值处理完成: userId={}, score={}", event.getUserId(), score);
    }

    // 其他辅助方法...
}
```

## Context数据规范

### 必需字段
- `userIds`: `List<Long>` - 批量用户ID列表（批量处理时必需）
- `averageDistribution`: `Boolean` - 是否强制平均分配

### 业务字段
- `activityId`: `Long` - 活动ID
- `attendeeCount`: `Integer` - 参与人数
- `activityType`: `String` - 活动类型
- `durationHours`: `Integer` - 持续时间
- `activityRating`: `Double` - 活动评分

## 配置管理

```sql
-- 支持平均分配的行为配置
INSERT INTO contribution_action_config VALUES (
    'ACTIVITY_ORGANIZER', '活动主办', 50.00, 'Y', 'Y', 
    '活动主办人获得的贡献值，支持多人平均分配'
);

-- 不支持平均分配的行为配置
INSERT INTO contribution_action_config VALUES (
    'COURSE_CHECKIN', '课程签到', 10.00, 'N', 'Y', 
    '课程签到获得的贡献值'
);
```

## 实际效果

### 场景：3人主办80人培训活动

```
输入数据：
- organizerIds: [1, 2, 3]
- activityType: "TRAINING"
- attendeeCount: 80
- averageDistribution: true

处理流程：
1. 监听器接收事件，检测到userIds批量数据
2. 调用ActivityOrganizerContributionStrategy.process()
3. 策略检测到批量处理，调用processBatchUsers()
4. 计算总分：50 × 1.5 × 1.3 = 97.5分
5. 平均分配：97.5 ÷ 3 = 32.5分/人
6. 插入3条日志记录

数据库结果：
contribution_log表：
- 用户1：32.5分，action_ref_id=activityId
- 用户2：32.5分，action_ref_id=activityId  
- 用户3：32.5分，action_ref_id=activityId
```

## 优势总结

1. **统一简洁**：只需要一个事件类型
2. **灵活扩展**：通过context传递任意数据
3. **智能识别**：策略自动判断单个/批量
4. **配置驱动**：支持数据库配置和代码强制控制
5. **易于使用**：发布事件即可，无需关心内部逻辑

这种设计完美符合您的需求：简单、统一、灵活！
