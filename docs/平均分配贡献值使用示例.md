# 平均分配贡献值使用示例

## 1. 场景说明

当一个活动有多个主办人时，需要将基础贡献值按人数平均分配，并且为每个主办人单独记录一条贡献值日志。

## 2. 配置设置

### 2.1 数据库配置
```sql
-- 设置活动主办行为支持平均分配
INSERT INTO contribution_action_config (
    code, name, score, average_flag, enabled, remark
) VALUES (
    'ACTIVITY_ORGANIZER', '活动主办', 50.00, 'Y', 'Y', 
    '活动主办人获得的贡献值，支持多人平均分配'
);
```

**关键字段说明：**
- `score`: 50.00 - 基础分值
- `average_flag`: 'Y' - 启用平均分配
- `enabled`: 'Y' - 启用该行为

## 3. 使用方法

### 3.1 发布批量贡献值事件

```java
@Service
public class ActivityService {
    
    @Resource
    private ApplicationEventPublisher eventPublisher;
    
    /**
     * 活动结束后为主办人分配贡献值
     */
    public void assignOrganizerContribution(Long activityId, List<Long> organizerIds) {
        // 准备上下文信息
        Map<String, Object> context = new HashMap<>();
        context.put("activityId", activityId);
        context.put("attendeeCount", 80);  // 参与人数
        context.put("activityType", "TRAINING");  // 活动类型
        context.put("durationHours", 6);  // 持续时间
        context.put("activityRating", 4.6);  // 活动评分
        
        // 发布批量贡献值事件
        BatchContributionActionEvent event = new BatchContributionActionEvent(
            this, 
            organizerIds,  // 多个主办人ID
            "ACTIVITY_ORGANIZER", 
            context,
            true  // 启用平均分配
        );
        
        eventPublisher.publishEvent(event);
    }
}
```

### 3.2 处理流程

```
1. 系统接收批量事件
2. 验证配置（ACTIVITY_ORGANIZER 已启用，average_flag = 'Y'）
3. 获取策略计算总分值
   - 基础分值：50.00
   - 参与人数奖励：80人 × 1.5 = 75.00
   - 活动类型奖励：培训 × 1.3 = 97.50
   - 持续时间奖励：6小时 × 1.2 = 117.00
   - 评分奖励：4.6分 × 1.3 = 152.10
4. 平均分配：152.10 ÷ 3人 = 50.70 每人
5. 为每个主办人记录一条日志
```

## 4. 实际效果

### 4.1 假设场景
- 活动ID：1001
- 主办人：[用户A, 用户B, 用户C]
- 参与人数：80人
- 活动类型：培训
- 持续时间：6小时
- 活动评分：4.6分

### 4.2 计算过程
```
基础分值：50.00
× 参与人数奖励(1.5) = 75.00
× 活动类型奖励(1.3) = 97.50  
× 持续时间奖励(1.2) = 117.00
× 评分奖励(1.3) = 152.10

平均分配：152.10 ÷ 3 = 50.70 每人
```

### 4.3 数据库记录
```sql
-- contribution_log 表会插入3条记录
INSERT INTO contribution_log VALUES 
(1, 用户A_ID, '用户A', 'ACTIVITY_ORGANIZER', 50.70, '行为：活动主办', 'SYSTEM', '1', 1001, NOW()),
(2, 用户B_ID, '用户B', 'ACTIVITY_ORGANIZER', 50.70, '行为：活动主办', 'SYSTEM', '1', 1001, NOW()),
(3, 用户C_ID, '用户C', 'ACTIVITY_ORGANIZER', 50.70, '行为：活动主办', 'SYSTEM', '1', 1001, NOW());
```

## 5. 高级用法

### 5.1 不同分配策略

```java
// 1. 强制平均分配（忽略配置）
BatchContributionActionEvent event1 = new BatchContributionActionEvent(
    this, organizerIds, "ACTIVITY_ORGANIZER", context, true);

// 2. 不平均分配（每人都得全额）
BatchContributionActionEvent event2 = new BatchContributionActionEvent(
    this, organizerIds, "ACTIVITY_ORGANIZER", context, false);

// 3. 根据配置决定（推荐）
BatchContributionActionEvent event3 = new BatchContributionActionEvent(
    this, organizerIds, "ACTIVITY_ORGANIZER", context);
```

### 5.2 复杂场景处理

```java
/**
 * 救援案例多人参与平均分配
 */
public void assignRescueContribution(Long rescueCaseId, List<Long> rescuerIds) {
    Map<String, Object> context = new HashMap<>();
    context.put("rescueCaseId", rescueCaseId);
    context.put("difficulty", "HIGH");  // 救援难度
    context.put("successful", true);    // 救援结果
    context.put("rescueCount", rescuerIds.size());  // 参与人数
    
    BatchContributionActionEvent event = new BatchContributionActionEvent(
        this, rescuerIds, "RESCUE_CASE", context);
    eventPublisher.publishEvent(event);
}
```

## 6. 配置管理

### 6.1 动态调整分配方式
```sql
-- 启用平均分配
UPDATE contribution_action_config 
SET average_flag = 'Y' 
WHERE code = 'ACTIVITY_ORGANIZER';

-- 禁用平均分配（每人都得全额）
UPDATE contribution_action_config 
SET average_flag = 'N' 
WHERE code = 'ACTIVITY_ORGANIZER';
```

### 6.2 调整基础分值
```sql
-- 提高活动主办基础分值
UPDATE contribution_action_config 
SET score = 80.00 
WHERE code = 'ACTIVITY_ORGANIZER';
```

## 7. 监控和查询

### 7.1 查看分配结果
```sql
-- 查看某个活动的主办人贡献值分配
SELECT user_id, user_name, score, create_time
FROM contribution_log 
WHERE action_code = 'ACTIVITY_ORGANIZER' 
  AND action_ref_id = 1001  -- 活动ID
ORDER BY create_time;
```

### 7.2 统计分析
```sql
-- 统计活动主办贡献值分配情况
SELECT 
    action_ref_id as activity_id,
    COUNT(*) as organizer_count,
    SUM(score) as total_score,
    AVG(score) as avg_score_per_person
FROM contribution_log 
WHERE action_code = 'ACTIVITY_ORGANIZER'
GROUP BY action_ref_id
ORDER BY total_score DESC;
```

## 8. 注意事项

### 8.1 数据一致性
- 批量事件确保所有用户要么都成功，要么都失败
- 单个用户记录失败不影响其他用户

### 8.2 性能考虑
- 大批量用户时建议分批处理
- 异步处理不阻塞主业务流程

### 8.3 业务规则
- 平均分配时小数点保留2位
- 支持配置驱动的分配策略
- 每个用户都有独立的日志记录

## 总结

通过批量贡献值事件和平均分配机制，系统能够灵活处理多人共享贡献值的场景，既保证了公平性，又维护了数据的完整性和可追溯性。
