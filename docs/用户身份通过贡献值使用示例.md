# 用户身份通过贡献值使用示例

## 功能概述

当用户身份审核通过后，系统自动为用户分配贡献值，以鼓励用户完善个人信息和身份认证。

## 核心组件

### 1. 贡献值策略类
- **类名**: `UserIdentityApprovedContributionStrategy`
- **行为编码**: `USER_IDENTITY_APPROVED`
- **处理方式**: 单个用户处理，不支持批量

### 2. 事件发布
- **触发时机**: 用户身份审核通过后
- **发布位置**: `UserApplyServiceImpl.updateUserTypeAndInfo()` 方法
- **事件类型**: `ContributionActionEvent`

### 3. 数据库配置
```sql
INSERT INTO contribution_action_config (
    code, name, score, average_flag, enabled, remark
) VALUES (
    'USER_IDENTITY_APPROVED', '用户身份通过', 20.00, 'N', 'Y', 
    '用户身份审核通过获得的贡献值'
);
```

## 使用流程

### 1. 自动触发（推荐）
用户身份审核通过时，系统自动发布贡献值事件：

```java
// 在 UserApplyServiceImpl.updateUserTypeAndInfo() 中自动调用
private void publishUserIdentityApprovedEvent(UserApply userApply) {
    Map<String, Object> context = new HashMap<>();
    context.put("applyId", userApply.getId());
    context.put("userType", userApply.getUserType());
    context.put("realName", userApply.getRealName());
    context.put("idCard", userApply.getIdCard());
    context.put("phoneNumber", userApply.getPhoneNumber());

    ContributionActionEvent event = new ContributionActionEvent(
        this, userApply.getUserId(), "USER_IDENTITY_APPROVED", context);
    
    eventPublisher.publishEvent(event);
}
```

### 2. 手动触发
在需要的地方手动发布事件：

```java
@Service
public class SomeService {
    
    @Resource
    private ApplicationEventPublisher eventPublisher;
    
    public void grantIdentityApprovedContribution(Long userId, Long applyId) {
        Map<String, Object> context = new HashMap<>();
        context.put("applyId", applyId);
        context.put("userType", "DOCTOR");
        context.put("realName", "张医生");
        
        ContributionActionEvent event = new ContributionActionEvent(
            this, userId, "USER_IDENTITY_APPROVED", context);
        
        eventPublisher.publishEvent(event);
    }
}
```

## 上下文参数

### 必需参数
- **userId**: 用户ID（通过事件构造函数传入）
- **actionCode**: 行为编码（固定为 "USER_IDENTITY_APPROVED"）

### 可选参数
- **applyId**: 申请ID，用于关联业务记录
- **userType**: 用户类型（DOCTOR、NURSE、VOLUNTEER等）
- **realName**: 真实姓名
- **idCard**: 身份证号
- **phoneNumber**: 手机号码

## 贡献值计算

### 基础分值
- 从数据库配置表 `contribution_action_config` 读取
- 默认配置：20.00分
- 可通过管理界面动态调整

### 计算逻辑
```java
private double calculateScore(ContributionActionConfig config) {
    if (config == null) {
        return 0.0;
    }
    
    BigDecimal baseScore = config.getScore();
    if (baseScore == null) {
        return 0.0;
    }
    
    // 目前使用基础分值，后续可扩展复杂计算逻辑
    return baseScore.doubleValue();
}
```

## 日志记录

### 贡献值日志
- **表名**: `contribution_log`
- **用户ID**: 获得贡献值的用户
- **行为编码**: USER_IDENTITY_APPROVED
- **分值**: 计算得到的贡献值
- **描述**: 从配置表的remark字段获取
- **关联ID**: 申请记录ID

### 事件日志
- **表名**: `contribution_event_log`
- **记录**: 事件接收、处理状态、结果消息
- **用途**: 问题排查和监控

## 配置管理

### 启用/禁用
```sql
-- 禁用用户身份通过贡献值
UPDATE contribution_action_config 
SET enabled = 'N' 
WHERE code = 'USER_IDENTITY_APPROVED';

-- 启用用户身份通过贡献值
UPDATE contribution_action_config 
SET enabled = 'Y' 
WHERE code = 'USER_IDENTITY_APPROVED';
```

### 调整分值
```sql
-- 调整用户身份通过基础分值为30分
UPDATE contribution_action_config 
SET score = 30.00 
WHERE code = 'USER_IDENTITY_APPROVED';
```

## 监控查询

### 查看用户贡献值记录
```sql
SELECT * FROM contribution_log 
WHERE action_code = 'USER_IDENTITY_APPROVED' 
  AND user_id = 1 
ORDER BY create_time DESC;
```

### 统计身份通过贡献值
```sql
SELECT 
    COUNT(*) as total_count,
    SUM(score) as total_score,
    AVG(score) as avg_score
FROM contribution_log 
WHERE action_code = 'USER_IDENTITY_APPROVED'
  AND DATE(create_time) = CURDATE();
```

### 查看事件处理状态
```sql
SELECT * FROM contribution_event_log 
WHERE action_code = 'USER_IDENTITY_APPROVED' 
  AND process_status = '2'  -- 处理失败的记录
ORDER BY create_time DESC;
```

## 扩展说明

### 1. 复杂计算逻辑
可在 `calculateScore` 方法中添加更复杂的计算逻辑：

```java
private double calculateScore(ContributionActionConfig config, ContributionActionEvent event) {
    double baseScore = config.getScore().doubleValue();
    
    // 根据用户类型调整分值
    String userType = (String) event.getContext().get("userType");
    if ("DOCTOR".equals(userType)) {
        baseScore *= 1.5;  // 医生身份认证额外奖励50%
    } else if ("NURSE".equals(userType)) {
        baseScore *= 1.2;  // 护士身份认证额外奖励20%
    }
    
    return baseScore;
}
```

### 2. 新增相关策略
可以参考此实现，创建其他身份相关的贡献值策略：
- 证书认证通过：`CERTIFICATE_APPROVED`
- 资质审核通过：`QUALIFICATION_APPROVED`
- 信息完善奖励：`PROFILE_COMPLETED`

## 测试验证

### 运行测试用例
```bash
# 运行用户身份通过贡献值策略测试
mvn test -Dtest=UserIdentityApprovedContributionTest
```

### 手动测试
1. 创建用户身份申请
2. 管理员审核通过
3. 查看贡献值日志表
4. 验证分值是否正确记录

## 总结

用户身份通过贡献值功能已完整实现：

1. **策略类**: 完整的计算和日志记录逻辑
2. **事件发布**: 审核通过时自动触发
3. **配置管理**: 支持动态调整分值和启用状态
4. **监控日志**: 完整的事件和贡献值记录
5. **测试覆盖**: 全面的单元测试用例

该功能遵循现有贡献值系统的设计模式，具有良好的可维护性和扩展性。
