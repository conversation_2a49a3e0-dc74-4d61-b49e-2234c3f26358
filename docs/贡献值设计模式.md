这是一个非常优秀的设计方向，主人！

在使用 Spring 事件驱动的基础上，引入 **策略模式（或多态）** 来处理“不同贡献行为”的计算逻辑，会让结构更清晰、逻辑更解耦，也方便扩展和测试。

---

## ✅ 总体设计目标

* 使用 **Spring 的事件驱动**来异步触发；
* 使用 **策略模式（多态）** 来根据 `actionCode` 找到对应的“计算策略”；
* 策略类只负责逻辑，最终由 Service 统一落地（加明细、更新总值）；
* 新增行为时，只需新增策略类+数据库配置，无需修改主逻辑代码。

---

## 🧱 一、核心类结构图

```
ContributionActionEvent (Spring事件)
     ↓
ContributionEventListener (统一监听)
     ↓
ContributionStrategyFactory (策略工厂)
     ↓
├── CourseSigninStrategy
├── RescueCaseStrategy
├── RecommendStrategy
└── ...
```

---

## 🧩 二、代码实现示例

---

### 1. 定义事件类

```java
public class ContributionActionEvent extends ApplicationEvent {
    private final Long userId;
    private final String actionCode;
    private final Map<String, Object> context;

    public ContributionActionEvent(Object source, Long userId, String actionCode, Map<String, Object> context) {
        super(source);
        this.userId = userId;
        this.actionCode = actionCode;
        this.context = context;
    }

    // Getters...
}
```

---

### 2. 定义策略接口

```java
public interface ContributionStrategy {
    /**
     * 每个行为对应一个唯一 code，如 "COURSE_SIGNIN"
     */
    String getSupportAction();

    /**
     * 计算实际的贡献值
     */
    double calculate(ContributionActionEvent event);
}
```

---

### 3. 示例策略实现：急救案例

```java
@Component
public class RescueCaseStrategy implements ContributionStrategy {

    @Override
    public String getSupportAction() {
        return "RESCUE_CASE";
    }

    @Override
    public double calculate(ContributionActionEvent event) {
        int people = (int) event.getContext().getOrDefault("rescueCount", 1);
        return 200.0 / people;
    }
}
```

再来一个签到策略：

```java
@Component
public class CourseSigninStrategy implements ContributionStrategy {

    @Override
    public String getSupportAction() {
        return "COURSE_SIGNIN";
    }

    @Override
    public double calculate(ContributionActionEvent event) {
        return 1.0; // 固定分值
    }
}
```

---

### 4. 策略工厂（自动注入）

```java
@Component
public class ContributionStrategyFactory {

    private final Map<String, ContributionStrategy> strategyMap = new HashMap<>();

    @Autowired
    public ContributionStrategyFactory(List<ContributionStrategy> strategies) {
        for (ContributionStrategy strategy : strategies) {
            strategyMap.put(strategy.getSupportAction(), strategy);
        }
    }

    public ContributionStrategy getStrategy(String actionCode) {
        return strategyMap.get(actionCode);
    }
}
```

---

### 5. 事件监听器（统一入口）

```java
@Slf4j
@Component
public class ContributionEventListener {

    @Autowired
    private ContributionStrategyFactory strategyFactory;

    @Autowired
    private ContributionActionConfigMapper configMapper;

    @Autowired
    private ContributionService contributionService;

    @Async
    @EventListener
    public void handle(ContributionActionEvent event) {
        String actionCode = event.getActionCode();

        ContributionActionConfig config = configMapper.selectByCode(actionCode);
        if (config == null || !Boolean.TRUE.equals(config.getEnabled())) {
            log.warn("贡献行为未启用: {}", actionCode);
            return;
        }

        ContributionStrategy strategy = strategyFactory.getStrategy(actionCode);
        if (strategy == null) {
            log.warn("未找到策略处理器: {}", actionCode);
            return;
        }

        double actualScore = strategy.calculate(event);
        if (actualScore <= 0) {
            log.info("贡献值为 0，忽略处理: {}", actionCode);
            return;
        }

        // 最终持久化处理
        contributionService.addContribution(
            event.getUserId(),
            actualScore,
            actionCode,
            "行为：" + config.getName()
        );
    }
}
```

---

## ✅ 三、优势总结

| 设计点       | 优势                            |
| --------- | ----------------------------- |
| **事件驱动**  | 逻辑异步执行，不影响主业务流                |
| **策略模式**  | 每种行为逻辑独立，开放封闭原则               |
| **工厂注册**  | 扫描自动注入，支持扩展                   |
| **配置化行为** | 后台管理行为表，前端只传 `actionCode` 和参数 |
| **易测试**   | 每个策略类可以单元测试验证积分计算正确性          |

---

## 🔧 四、可拓展建议

* 策略类中也可以注入 `行为基础分值`（从数据库中查），让策略只关注**算法**；
* 配置行为时加入 `是否允许重复积分` 字段，防止刷分；
* 监听器可增加异常监控和操作日志审计。

---

是否需要我为你整理一个“初始化脚手架项目结构”+策略注册模板？我可以一键生成全套 Java 类代码给你直接集成使用。
