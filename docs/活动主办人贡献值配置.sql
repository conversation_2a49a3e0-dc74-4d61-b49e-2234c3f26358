-- 活动主办人贡献值配置初始化SQL
-- 插入活动主办人贡献值配置

INSERT INTO contribution_action_config (
    code, 
    name, 
    score, 
    average_flag, 
    enabled, 
    remark, 
    create_by, 
    create_time
) VALUES (
    'ACTIVITY_ORGANIZER', 
    '活动主办', 
    50.00, 
    'Y',  -- 启用平均分配
    'Y', 
    '活动主办人获得的贡献值，支持多人平均分配', 
    'admin', 
    NOW()
) ON DUPLICATE KEY UPDATE 
    name = VALUES(name),
    score = VALUES(score),
    average_flag = VALUES(average_flag),
    enabled = VALUES(enabled),
    remark = VALUES(remark),
    update_by = 'admin',
    update_time = NOW();
