# 简化版贡献值系统使用指南

## 架构设计

### 核心原则
- **策略实现类**：负责完整的处理逻辑（计算 + 记录日志）
- **事件监听器**：只负责调用策略实现类
- **支持单个和批量**：统一的接口设计

### 组件职责

```
事件发布 → 监听器接收 → 调用策略 → 策略处理（计算+记录）
```

## 接口定义

```java
public interface ContributionStrategy {
    String getSupportAction();
    void process(ContributionActionEvent event); // 统一处理方法，通过context区分单个/批量
}
```

## 使用场景

### 1. 单个用户贡献值（如课程签到）

```java
// 发布事件
Map<String, Object> context = new HashMap<>();
context.put("attendanceId", 123L);
context.put("attendTime", new Date());

ContributionActionEvent event = new ContributionActionEvent(
    this, userId, "COURSE_CHECKIN", context);
eventPublisher.publishEvent(event);

// 系统自动处理：监听器 → 策略 → 计算 → 记录日志
```

### 2. 批量用户贡献值（如活动主办）

```java
// 发布批量事件（通过context传递批量数据）
List<Long> organizerIds = Arrays.asList(1L, 2L, 3L);
Map<String, Object> context = new HashMap<>();
context.put("activityId", 1001L);
context.put("attendeeCount", 80);
context.put("activityType", "TRAINING");
context.put("userIds", organizerIds);  // 批量用户ID列表
context.put("averageDistribution", true);  // 是否强制平均分配

ContributionActionEvent event = new ContributionActionEvent(
    this, organizerIds.get(0), "ACTIVITY_ORGANIZER", context);
eventPublisher.publishEvent(event);

// 系统自动处理：监听器 → 策略 → 检测批量 → 计算总分 → 平均分配 → 批量记录日志
```

## 策略实现示例

### 课程签到策略（单个用户）

```java
@Component
public class CourseCheckinContributionStrategy implements ContributionStrategy {

    @Override
    public void process(ContributionActionEvent event) {
        // 课程签到只支持单个用户，不检查批量

        // 1. 计算贡献值
        double score = calculateScore(event);
        if (score <= 0) return;

        // 2. 记录日志
        recordContributionLog(event, score);
    }
}
```

### 活动主办策略（支持批量）

```java
@Component
public class ActivityOrganizerContributionStrategy implements ContributionStrategy {

    @Override
    public void process(ContributionActionEvent event) {
        // 检查是否为批量处理
        @SuppressWarnings("unchecked")
        List<Long> userIds = (List<Long>) event.getContext().get("userIds");

        if (userIds != null && !userIds.isEmpty()) {
            // 批量处理
            processBatchUsers(event, userIds);
        } else {
            // 单个用户处理
            processSingleUser(event);
        }
    }

    private void processBatchUsers(ContributionActionEvent event, List<Long> userIds) {
        // 1. 计算总贡献值
        double totalScore = calculateScore(event);

        // 2. 根据配置决定分配方式
        boolean shouldAverage = checkAverageFlag(event);
        double scorePerUser = shouldAverage ?
            totalScore / userIds.size() : totalScore;

        // 3. 为每个用户记录日志
        for (Long userId : userIds) {
            recordContributionLog(userId, event, scorePerUser);
        }
    }
}
```

## 配置管理

### 数据库配置

```sql
-- 单个用户行为（如课程签到）
INSERT INTO contribution_action_config VALUES (
    'COURSE_CHECKIN', '课程签到', 10.00, 'N', 'Y', '课程签到贡献值'
);

-- 批量用户行为（如活动主办，支持平均分配）
INSERT INTO contribution_action_config VALUES (
    'ACTIVITY_ORGANIZER', '活动主办', 50.00, 'Y', 'Y', '活动主办贡献值，支持平均分配'
);
```

**字段说明：**
- `average_flag = 'Y'`：启用平均分配
- `average_flag = 'N'`：不平均分配（每人全额）

## 实际效果

### 场景：3人主办活动，基础50分

```
配置：average_flag = 'Y'（启用平均分配）

计算过程：
1. 策略计算总分：50 × 各种奖励倍率 = 120分
2. 平均分配：120 ÷ 3 = 40分/人
3. 数据库记录：3条日志，每条40分

结果：
contribution_log表插入3条记录：
- 用户A：40分
- 用户B：40分  
- 用户C：40分
```

## 监听器简化

```java
@Component
public class ContributionActionEventListener {

    @Async
    @EventListener
    public void handle(ContributionActionEvent event) {
        log.info("接收到贡献值事件: userId={}, actionCode={}",
            event.getUserId(), event.getActionCode());

        // 找到策略并调用，不关心单个还是批量
        ContributionStrategy strategy = strategyFactory.getStrategy(event.getActionCode());
        if (strategy != null) {
            strategy.process(event);  // 策略负责完整处理
        }

        log.info("贡献值事件处理完成: userId={}, actionCode={}",
            event.getUserId(), event.getActionCode());
    }
}
```

## 优势

### 1. 架构简单
- 监听器职责单一：只负责调用策略
- 策略职责完整：计算 + 记录一体化
- 减少组件间依赖

### 2. 易于扩展
- 新增策略只需实现接口
- 支持复杂的计算和分配逻辑
- 配置驱动的行为控制

### 3. 使用便捷
- 发布事件即可触发处理
- 支持单个和批量统一接口
- 异步处理不阻塞主流程

## 总结

简化后的架构更符合您的需求：
- **策略实现类**承担完整职责（计算+记录）
- **监听器**只负责简单的策略调用
- **支持平均分配**的批量处理能力
- **配置驱动**的灵活控制机制

这样的设计既保持了功能的完整性，又简化了架构的复杂度，更易于理解和维护。
