-- 用户身份通过贡献值配置初始化SQL
-- 根据用户类型分别配置贡献值

-- 急救员身份通过贡献值配置
INSERT INTO contribution_action_config (
    code,
    name,
    score,
    average_flag,
    enabled,
    remark,
    create_by,
    create_time
) VALUES (
    'IDENTITY_AIDER',
    '急救员身份通过',
    15.00,
    'N',  -- 不需要平均分配，单个用户行为
    'Y',
    '急救员身份审核通过获得的贡献值',
    'admin',
    NOW()
) ON DUPLICATE KEY UPDATE
    name = VALUES(name),
    score = VALUES(score),
    average_flag = VALUES(average_flag),
    enabled = VALUES(enabled),
    remark = VALUES(remark),
    update_by = 'admin',
    update_time = NOW();

-- 急救导师身份通过贡献值配置
INSERT INTO contribution_action_config (
    code,
    name,
    score,
    average_flag,
    enabled,
    remark,
    create_by,
    create_time
) VALUES (
    'IDENTITY_MENTOR',
    '急救导师身份通过',
    25.00,
    'N',  -- 不需要平均分配，单个用户行为
    'Y',
    '急救导师身份审核通过获得的贡献值',
    'admin',
    NOW()
) ON DUPLICATE KEY UPDATE
    name = VALUES(name),
    score = VALUES(score),
    average_flag = VALUES(average_flag),
    enabled = VALUES(enabled),
    remark = VALUES(remark),
    update_by = 'admin',
    update_time = NOW();
