# 忻道小程序 - 文档目录

本文件夹包含了忻道小程序项目的相关技术文档和配置文件。

## 📁 文件列表

### 📋 技术文档
- **贡献值系统架构分析文档.md** - 贡献值系统的整体架构分析，包括核心模块、策略模式和事件驱动设计
- **课程签到积分功能实现说明.md** - 课程签到积分功能的详细实现说明，包括技术架构和使用方式

### 🗄️ 配置文件
- **课程签到贡献值配置.sql** - 课程签到贡献值的数据库初始化配置脚本

## 🏗️ 系统架构概览

### 贡献值系统
- **策略模式**: 支持多种贡献值计算策略
- **事件驱动**: 基于Spring事件机制的异步处理
- **配置驱动**: 数据库配置，支持动态调整

### 已实现功能
- ✅ 课程签到积分功能
- ✅ 异步事件处理
- ✅ 策略工厂模式
- ✅ 贡献值日志记录

## 🚀 快速开始

1. **初始化配置**
   ```sql
   -- 执行配置脚本
   source docs/课程签到贡献值配置.sql
   ```

2. **查看架构文档**
   - 阅读 `贡献值系统架构分析文档.md` 了解整体设计
   - 阅读 `课程签到积分功能实现说明.md` 了解具体实现

3. **功能测试**
   - 运行单元测试验证功能
   - 在微信小程序中进行课程签到测试

## 📝 更新日志

- **2025-07-10**: 完成课程签到积分功能实现
- **2025-07-10**: 创建贡献值系统架构文档

## 🔗 相关链接

- 项目根目录: `../`
- 业务模块: `../drxin-bizz/`
- 微信模块: `../drxin-wechat/`
- 测试用例: `../drxin-bizz/src/test/`

---
*最后更新: 2025-07-10*
