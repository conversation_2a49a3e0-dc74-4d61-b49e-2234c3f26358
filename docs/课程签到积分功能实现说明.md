# 课程签到积分功能实现说明

## 功能概述
实现了用户在微信小程序中进行课程签到后，系统自动异步计算并记录贡献值的功能。

## 实现架构

### 1. 签到处理（wechat模块）
**文件**: `WxCourseAttendanceServiceImpl.java`

**修改内容**:
- 添加 `ContributionStrategyFactory` 依赖注入
- 在签到成功后异步调用贡献值策略
- 添加异常处理，确保贡献值处理失败不影响主流程

**关键代码**:
```java
// 签到成功后异步处理贡献值
if (saveResult) {
    processContributionAsync(attendance);
}

@Async
private void processContributionAsync(WxCourseAttendance attendance) {
    ContributionStrategy strategy = strategyFactory.getStrategy("COURSE_CHECKIN");
    if (strategy != null) {
        ContributionActionEvent event = new ContributionActionEvent(
            this, attendance.getUserId(), "COURSE_CHECKIN", context);
        strategy.process(event);
    }
}
```

### 2. 贡献值策略实现（bizz模块）
**文件**: `CourseCheckinContributionStrategy.java`

**功能**:
- 实现 `ContributionStrategy` 接口
- 支持行为编码: `COURSE_CHECKIN`
- 完整处理贡献值逻辑：计算 + 记录日志

**核心方法**:
```java
public interface ContributionStrategy {
    String getSupportAction();
    void process(ContributionActionEvent event);  // 处理完整逻辑
}
```

**特性**:
- 配置驱动：从 `contribution_action_config` 表读取分值配置
- 异常安全：处理失败时记录日志，不影响系统稳定性
- 智能调整：根据签到时间自动调整积分奖励
  - 早上7-9点签到：额外20%奖励
  - 晚上18-20点签到：额外10%奖励
- 自动记录：计算完成后自动记录贡献值日志
- 可扩展：支持根据课程类型、重要性等因素进一步调整积分

**处理流程**:
1. 计算贡献值 → 2. 获取用户信息 → 3. 记录贡献值日志

### 3. 配置管理
**文件**: `课程签到贡献值配置.sql`

**配置项**:
- `code`: COURSE_CHECKIN
- `name`: 课程签到
- `score`: 10.00（基础分值）
- `enabled`: Y（启用状态）

## 技术特点

### 1. 异步处理
- 使用Spring的 `@Async` 注解实现异步处理
- 签到操作不会因为积分计算而阻塞
- 提升用户体验和系统性能

### 2. 策略模式
- 支持多种贡献值计算策略
- 新增策略只需实现 `ContributionStrategy` 接口
- 策略工厂自动注册和管理
- 策略内部完成计算和日志记录的完整流程

### 3. 配置驱动
- 贡献值分值可通过数据库配置动态调整
- 支持启用/禁用控制
- 无需重启应用即可生效

### 4. 异常安全
- 完善的异常处理机制
- 积分计算失败不影响主业务流程
- 详细的日志记录便于问题排查

## 使用方式

### 1. 数据库配置
执行 `课程签到贡献值配置.sql` 初始化配置数据

### 2. 签到流程
用户在微信小程序中进行课程签到，系统自动：
1. 验证签到权限和重复性
2. 记录签到数据
3. 异步调用贡献值策略
4. 计算并记录贡献值

### 3. 查看结果
- 贡献值日志：`contribution_log` 表
- 管理界面：贡献值日志管理页面

## 扩展说明

### 1. 新增贡献值策略
```java
@Component
public class NewActionStrategy implements ContributionStrategy {
    @Override
    public String getSupportAction() {
        return "NEW_ACTION_CODE";
    }

    @Override
    public void process(ContributionActionEvent event) {
        // 实现完整处理逻辑：计算 + 记录日志
        double score = calculateScore(event);
        recordContributionLog(event, score);
    }
}
```

### 2. 调整积分规则
- 修改数据库配置表 `contribution_action_config`
- 或在策略实现中添加复杂计算逻辑

### 3. 直接调用策略处理
```java
// 在需要的地方直接调用策略
ContributionStrategy strategy = strategyFactory.getStrategy("ACTION_CODE");
if (strategy != null) {
    strategy.process(event);
}
```

## 测试验证
提供了完整的单元测试用例 `CourseCheckinContributionTest.java`，验证：
- 策略注册正确性
- 基础贡献值计算功能
- 早签到奖励机制（20%奖励）
- 晚签到奖励机制（10%奖励）
- 工厂模式正确性

## 总结
该实现采用了简化的策略模式设计，每个策略负责完整的贡献值处理流程（计算+记录），通过异步调用确保性能，实现了高效、可扩展的签到积分功能。相比事件监听器模式，这种设计更加直接和简洁，减少了系统复杂度。
