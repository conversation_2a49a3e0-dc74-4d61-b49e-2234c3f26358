package com.drxin.common.utils;

import com.drxin.common.exception.ServiceException;

/**
 * 证件号码校验工具类
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
public class IdCardValidationUtils {

    /**
     * 证件号码格式校验
     *
     * @param idCard 证件号码
     * @param cardType 证件类型
     */
    public static void validateIdCard(String idCard, String cardType) {
        if (cardType == null || cardType.trim().isEmpty()) {
            throw new ServiceException("证件类型不能为空");
        }

        switch (cardType.trim()) {
            case "IDCARD":
                validateMainlandIdCard(idCard);
                break;
            case "HKM_PASS":
                validateHKMacauPass(idCard);
                break;
            case "TW_PASS":
                validateTaiwanPass(idCard);
                break;
            case "PASSPORT":
                validatePassport(idCard);
                break;
            default:
                throw new ServiceException("不支持的证件类型");
        }
    }

    /**
     * 大陆身份证校验
     *
     * @param idCard 身份证号
     */
    public static void validateMainlandIdCard(String idCard) {
        // 大陆身份证号长度校验
        if (idCard.length() != 18) {
            throw new ServiceException("大陆身份证号长度必须为18位");
        }

        // 大陆身份证号格式校验
        String reg = "^[1-9]\\d{5}(19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]$";
        if (!idCard.matches(reg)) {
            throw new ServiceException("大陆身份证号格式不正确");
        }

        // 校验码验证
        int[] factor = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
        char[] parity = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};
        int sum = 0;

        for (int i = 0; i < 17; i++) {
            int ai = Character.getNumericValue(idCard.charAt(i));
            int wi = factor[i];
            sum += ai * wi;
        }

        char last = parity[sum % 11];
        if (last != idCard.charAt(17) && last != Character.toLowerCase(idCard.charAt(17))) {
            throw new ServiceException("大陆身份证号校验码错误");
        }
    }

    /**
     * 港澳居民来往内地通行证校验
     *
     * @param idCard 港澳通行证号
     */
    public static void validateHKMacauPass(String idCard) {
        // 港澳通行证格式：H/M + 8位或10位数字
        String hkMacauReg = "^[HMhm]{1}([0-9]{10}|[0-9]{8})$";
        if (!idCard.matches(hkMacauReg)) {
            throw new ServiceException("港澳居民来往内地通行证格式不正确");
        }
    }

    /**
     * 台湾居民来往大陆通行证校验
     *
     * @param idCard 台胞证号
     */
    public static void validateTaiwanPass(String idCard) {
        // 台胞证格式：8位或10位数字
        String taiwanReg = "^[0-9]{8,10}$";
        if (!idCard.matches(taiwanReg)) {
            throw new ServiceException("台湾居民来往大陆通行证格式不正确");
        }
    }

    /**
     * 护照校验
     *
     * @param idCard 护照号
     */
    public static void validatePassport(String idCard) {
        // 护照格式：字母+数字组合，长度6-20位
        String passportReg = "^[A-Za-z0-9]{6,20}$";
        if (!idCard.matches(passportReg)) {
            throw new ServiceException("护照号码格式不正确");
        }
    }

    /**
     * 手机号码格式校验
     *
     * @param phoneNumber 手机号码
     */
    public static void validatePhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            throw new ServiceException("手机号码不能为空");
        }
        
        String phone = phoneNumber.trim();
        if (!phone.matches("^1[3-9]\\d{9}$")) {
            throw new ServiceException("手机号码格式不正确");
        }
    }
}
