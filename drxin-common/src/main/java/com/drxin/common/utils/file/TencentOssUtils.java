package com.drxin.common.utils.file;

import com.drxin.common.config.TencentCosConfig;
import com.drxin.common.exception.ServiceException;
import com.drxin.common.utils.uuid.UUID;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.model.COSObject;
import com.qcloud.cos.model.GetObjectRequest;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.region.Region;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 腾讯云COS文件上传工具类
 * 优化版本，支持连接池管理和更好的异常处理
 */
@Slf4j
@Component
public class TencentOssUtils {

    // 获取配置文件环境
    @Value("${spring.profiles.active}")
    private String activeProfile;

    private static String activeProfileCache;


    private static final int MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
    private static final String DATE_PATTERN = "yyyy/MM/dd";
    private static final ConcurrentHashMap<String, COSClient> clientPool = new ConcurrentHashMap<>();
    private static final ReentrantLock clientLock = new ReentrantLock();

    private static TencentCosConfig tencentCosConfig;

    @Autowired
    public TencentOssUtils(TencentCosConfig tencentCosConfig) {
        TencentOssUtils.tencentCosConfig = tencentCosConfig;
    }

    public static void deleteFile(String oldCode) {
        String key = oldCode.replace(tencentCosConfig.getBaseUrl() + "/","");
        COSClient cosClient = getCosClient();
        try {
            // 删除COS中的文件
            cosClient.deleteObject(tencentCosConfig.getBucketName(), key);
            log.info("文件删除成功 - COS路径: {}", key);
        } catch (CosServiceException e) {
            log.error("COS服务异常 - 删除文件失败, COS路径: {}, 错误码: {}, 错误信息: {}",
                    key, e.getErrorCode(), e.getErrorMessage(), e);
            throw new ServiceException("文件删除失败：" + e.getErrorMessage());
        } catch (CosClientException e) {
            log.error("COS客户端异常 - 删除文件失败, COS路径: {}", key, e);
            throw new ServiceException("文件删除失败：网络连接异常");
        } catch (Exception e) {
            log.error("删除文件失败 - COS路径: {}", key, e);
            throw new ServiceException("文件删除失败：" + e.getMessage());
        }
    }

    @PostConstruct
    public void init() {
        try {
            activeProfileCache = this.activeProfile;
            log.info("当前环境配置: {}", activeProfileCache);

            // 验证配置信息完整性
            validateConfig();

            // 预创建一个客户端连接，验证连接可用性
            COSClient testClient = createCosClient();

            // 测试连接是否正常（检查bucket是否存在且可访问）
            testConnection(testClient);

            // 将测试客户端加入连接池
            String key = generateClientKey();
            clientPool.put(key, testClient);

            log.info("TencentOssUploadUtils 初始化完成，COS连接测试成功");
        } catch (Exception e) {
            log.error("TencentOssUploadUtils 初始化失败", e);
            throw new RuntimeException("COS服务初始化失败，请检查配置信息", e);
        }
    }

    /**
     * 验证配置信息
     */
    private void validateConfig() {
        if (tencentCosConfig == null) {
            throw new IllegalStateException("TencentCosConfig 配置为空");
        }

        if (!StringUtils.hasText(tencentCosConfig.getSecretId())) {
            throw new IllegalStateException("SecretId 不能为空");
        }

        if (!StringUtils.hasText(tencentCosConfig.getSecretKey())) {
            throw new IllegalStateException("SecretKey 不能为空");
        }

        if (!StringUtils.hasText(tencentCosConfig.getRegion())) {
            throw new IllegalStateException("Region 不能为空");
        }

        if (!StringUtils.hasText(tencentCosConfig.getBucketName())) {
            throw new IllegalStateException("BucketName 不能为空");
        }

        if (!StringUtils.hasText(tencentCosConfig.getBaseUrl())) {
            throw new IllegalStateException("BaseUrl 不能为空");
        }

        log.info("COS配置验证通过 - Region: {}, Bucket: {}",
                tencentCosConfig.getRegion(), tencentCosConfig.getBucketName());
    }

    /**
     * 测试COS连接
     */
    private void testConnection(COSClient cosClient) {
        try {
            // 检查bucket是否存在
            boolean bucketExists = cosClient.doesBucketExist(tencentCosConfig.getBucketName());
            if (!bucketExists) {
                throw new IllegalStateException("指定的存储桶不存在: " + tencentCosConfig.getBucketName());
            }

            // 可以添加更多连接测试，比如权限检查等
            log.info("COS连接测试成功，存储桶可正常访问");
        } catch (Exception e) {
            log.error("COS连接测试失败", e);
            throw new RuntimeException("COS服务连接失败", e);
        }
    }

    @PreDestroy
    public void destroy() {
        // 关闭所有客户端连接
        clientPool.values().forEach(client -> {
            try {
                client.shutdown();
            } catch (Exception e) {
                log.warn("关闭COS客户端失败", e);
            }
        });
        clientPool.clear();
        log.info("TencentOssUploadUtils 销毁完成");
    }

    /**
     * 上传文件（MultipartFile版本）
     * @param file 待上传的文件
     * @return 文件访问URL
     * @throws ServiceException 业务异常
     */
    public static String uploadFile(MultipartFile file) {
        validateFile(file);

        String filename = file.getOriginalFilename();
        String filePath = generateFilePath(filename);

        COSClient cosClient = getCosClient();

        try (InputStream inputStream = file.getInputStream()) {
            return doUpload(cosClient, inputStream, filePath, file.getSize());
        } catch (IOException e) {
            log.error("获取文件输入流失败, 文件名: {}", filename, e);
            throw new ServiceException("文件读取失败");
        } catch (Exception e) {
            log.error("文件上传失败, 文件名: {}, 路径: {}", filename, filePath, e);
            throw new ServiceException("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传文件（InputStream版本）
     * @param inputStream 文件输入流
     * @param fileName 文件名
     * @return 文件访问URL
     * @throws ServiceException 业务异常
     */
    public static String uploadFile(InputStream inputStream, String fileName) {
        validateInputStream(inputStream, fileName);

        String filePath = generateFilePath(fileName);
        COSClient cosClient = getCosClient();

        try {
            long contentLength = inputStream.available();
            return doUpload(cosClient, inputStream, filePath, contentLength);
        } catch (IOException e) {
            log.error("获取输入流大小失败, 文件名: {}", fileName, e);
            throw new ServiceException("文件流读取失败");
        } catch (Exception e) {
            log.error("文件上传失败, 文件名: {}, 路径: {}", fileName, filePath, e);
            throw new ServiceException("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 执行实际的上传操作
     */
    private static String doUpload(COSClient cosClient, InputStream inputStream,
                                   String filePath, long contentLength) {
        try {
            ObjectMetadata metadata = createObjectMetadata(contentLength);
            PutObjectRequest request = new PutObjectRequest(
                    tencentCosConfig.getBucketName(), filePath, inputStream, metadata);

            // 上传文件
            cosClient.putObject(request);

            // 设置公共读权限（仅在需要时设置）
            setBucketPublicReadIfNeeded(cosClient);

            String fileUrl = tencentCosConfig.getBaseUrl() + "/" + filePath;
            log.info("文件上传成功, 路径: {}, URL: {}", filePath, fileUrl);
            return fileUrl;
        } catch (Exception e) {
            log.error("COS上传操作失败, 路径: {}", filePath, e);
            throw new ServiceException("文件上传到云存储服务失败");
        }
    }

    /**
     * 创建对象元数据
     */
    private static ObjectMetadata createObjectMetadata(long contentLength) {
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(contentLength);
        // 可以根据需要添加更多元数据
        // metadata.setContentType(contentType);
        // metadata.setCacheControl("max-age=3600");
        return metadata;
    }

    /**
     * 设置存储桶公共读权限（谨慎使用）
     */
    private static void setBucketPublicReadIfNeeded(COSClient cosClient) {
        try {
            // 注意：每次上传都设置bucket权限是不必要的，建议在初始化时设置一次
            // 这里保留原有逻辑，但建议根据实际需求调整
            // cosClient.setBucketAcl(tencentCosConfig.getBucketName(), CannedAccessControlList.PublicRead);
            log.info("IP域名限制已设置，无需修改公共读权限。");
        } catch (Exception e) {
            log.warn("设置存储桶公共读权限失败", e);
            // 不抛出异常，因为文件已经上传成功
        }
    }

    /**
     * 校验MultipartFile
     */
    private static void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new ServiceException("文件不能为空");
        }

        if (!StringUtils.hasText(file.getOriginalFilename())) {
            throw new ServiceException("文件名不能为空");
        }

        if (file.getSize() > MAX_FILE_SIZE) {
            throw new ServiceException("文件大小不能超过100MB");
        }

        // 可以添加更多校验逻辑
        validateFileType(file.getOriginalFilename());
    }

    /**
     * 校验InputStream和文件名
     */
    private static void validateInputStream(InputStream inputStream, String fileName) {
        if (inputStream == null) {
            throw new ServiceException("文件流不能为空");
        }

        if (!StringUtils.hasText(fileName)) {
            throw new ServiceException("文件名不能为空");
        }

        validateFileType(fileName);
    }

    /**
     * 校验文件类型
     */
    private static void validateFileType(String fileName) {
        if (!fileName.contains(".")) {
            throw new ServiceException("文件必须包含扩展名");
        }

        String extension = fileName.substring(fileName.lastIndexOf(".")).toLowerCase();
        // 可以根据需要添加文件类型白名单或黑名单校验
        if (extension.length() > 10) {
            throw new ServiceException("文件扩展名过长");
        }
    }

    /**
     * 生成文件存储路径
     * 格式：drxin-project/{category}/{date}/{uuid}.{extension}
     */
    private static String generateFilePath(String fileName) {
        String fileExtension = getFileExtension(fileName);
        String category = determineFileCategory(fileName);
        String dateDir = LocalDate.now().format(DateTimeFormatter.ofPattern(DATE_PATTERN));
        String uuid = UUID.randomUUID().toString();
        String profile = activeProfileCache != null ? activeProfileCache : "dev";
        return String.format("drxin-project/%s/%s/%s/%s%s",profile, category, dateDir, uuid, fileExtension);
    }

    /**
     * 获取文件扩展名
     */
    private static String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf(".");
        if (lastDotIndex == -1) {
            return "";
        }
        return fileName.substring(lastDotIndex).toLowerCase();
    }

    /**
     * 确定文件分类
     */
    private static String determineFileCategory(String fileName) {
        if (FileUtils.isVideo(fileName)) {
            return "video";
        } else if (FileUtils.isImage(fileName)) {
            return "image";
        } else {
            return "other";
        }
    }

    /**
     * 获取COS客户端（使用连接池）
     */
    private static COSClient getCosClient() {
        String key = generateClientKey();

        return clientPool.computeIfAbsent(key, k -> {
            clientLock.lock();
            try {
                return createCosClient();
            } finally {
                clientLock.unlock();
            }
        });
    }

    /**
     * 生成客户端缓存key
     */
    private static String generateClientKey() {
        return String.format("%s_%s_%s",
                tencentCosConfig.getSecretId(),
                tencentCosConfig.getRegion(),
                tencentCosConfig.getBucketName());
    }

    /**
     * 创建COS客户端
     */
    private static COSClient createCosClient() {
        try {
            BasicCOSCredentials credentials = new BasicCOSCredentials(
                    tencentCosConfig.getSecretId(),
                    tencentCosConfig.getSecretKey());

            Region region = new Region(tencentCosConfig.getRegion());
            ClientConfig clientConfig = new ClientConfig(region);

            // 可以根据需要配置更多客户端参数
            // clientConfig.setConnectionTimeout(30000);
            // clientConfig.setSocketTimeout(30000);

            COSClient cosClient = new COSClient(credentials, clientConfig);
            log.info("COS客户端创建成功, 区域: {}", tencentCosConfig.getRegion());
            return cosClient;

        } catch (Exception e) {
            log.error("创建COS客户端失败", e);
            throw new ServiceException("初始化云存储客户端失败");
        }
    }
    /**
     * 下载文件到指定本地路径
     * @param cosFilePath COS中的文件路径（对象键）
     * @param localFilePath 本地文件保存路径
     * @return 是否下载成功
     * @throws ServiceException 业务异常
     */
    public static boolean downloadFile(String cosFilePath, String localFilePath) {
        validateDownloadParams(cosFilePath, localFilePath);

        File localFile = new File(localFilePath);
        return downloadFile(cosFilePath, localFile);
    }

    /**
     * 下载文件到指定File对象
     * @param cosFilePath COS中的文件路径（对象键）
     * @param localFile 本地文件对象
     * @return 是否下载成功
     * @throws ServiceException 业务异常
     */
    public static boolean downloadFile(String cosFilePath, File localFile) {
        validateDownloadParams(cosFilePath, localFile);

        COSClient cosClient = getCosClient();

        try {
            // 创建本地文件的父目录（如果不存在）
            createParentDirectories(localFile);

            // 创建下载请求
            GetObjectRequest getObjectRequest = new GetObjectRequest(
                    tencentCosConfig.getBucketName(), cosFilePath);

            // 执行下载
            ObjectMetadata objectMetadata = cosClient.getObject(getObjectRequest, localFile);

            // 验证下载是否成功
            if (localFile.exists() && localFile.length() > 0) {
                log.info("文件下载成功 - COS路径: {}, 本地路径: {}, 文件大小: {} bytes",
                        cosFilePath, localFile.getAbsolutePath(), localFile.length());
                return true;
            } else {
                log.error("文件下载失败，本地文件不存在或为空 - COS路径: {}", cosFilePath);
                throw new ServiceException("文件下载失败，本地文件创建异常");
            }

        } catch (CosServiceException e) {
            log.error("COS服务异常 - 文件路径: {}, 错误码: {}, 错误信息: {}",
                    cosFilePath, e.getErrorCode(), e.getErrorMessage(), e);

            // 根据不同的错误码提供更友好的错误信息
            String errorMessage = getDownloadErrorMessage(e.getErrorCode());
            throw new ServiceException(errorMessage);

        } catch (CosClientException e) {
            log.error("COS客户端异常 - 文件路径: {}", cosFilePath, e);
            throw new ServiceException("文件下载失败：网络连接异常");

        } catch (Exception e) {
            log.error("文件下载失败 - COS路径: {}, 本地路径: {}",
                    cosFilePath, localFile.getAbsolutePath(), e);
            throw new ServiceException("文件下载失败：" + e.getMessage());
        }
    }

    /**
     * 下载文件并返回字节数组
     * @param cosFilePath COS中的文件路径（对象键）
     * @return 文件字节数组
     * @throws ServiceException 业务异常
     */
    public static byte[] downloadFileAsBytes(String cosFilePath) {
        validateCosFilePath(cosFilePath);

        COSClient cosClient = getCosClient();

        try {
            GetObjectRequest getObjectRequest = new GetObjectRequest(
                    tencentCosConfig.getBucketName(), cosFilePath);

            COSObject cosObject = cosClient.getObject(getObjectRequest);

            try (InputStream inputStream = cosObject.getObjectContent();
                 ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }

                byte[] fileBytes = outputStream.toByteArray();
                log.info("文件下载成功（字节数组） - COS路径: {}, 文件大小: {} bytes",
                        cosFilePath, fileBytes.length);
                return fileBytes;

            }

        } catch (CosServiceException e) {
            log.error("COS服务异常 - 文件路径: {}, 错误码: {}, 错误信息: {}",
                    cosFilePath, e.getErrorCode(), e.getErrorMessage(), e);
            String errorMessage = getDownloadErrorMessage(e.getErrorCode());
            throw new ServiceException(errorMessage);

        } catch (CosClientException e) {
            log.error("COS客户端异常 - 文件路径: {}", cosFilePath, e);
            throw new ServiceException("文件下载失败：网络连接异常");

        } catch (IOException e) {
            log.error("IO异常 - 文件路径: {}", cosFilePath, e);
            throw new ServiceException("文件下载失败：数据读取异常");

        } catch (Exception e) {
            log.error("文件下载失败 - COS路径: {}", cosFilePath, e);
            throw new ServiceException("文件下载失败：" + e.getMessage());
        }
    }

    /**
     * 检查COS中的文件是否存在
     * @param cosFilePath COS中的文件路径（对象键）
     * @return 文件是否存在
     */
    public static boolean doesFileExist(String cosFilePath) {
        validateCosFilePath(cosFilePath);

        COSClient cosClient = getCosClient();

        try {
            return cosClient.doesObjectExist(tencentCosConfig.getBucketName(), cosFilePath);
        } catch (Exception e) {
            log.error("检查文件是否存在失败 - COS路径: {}", cosFilePath, e);
            return false;
        }
    }

    /**
     * 获取文件元数据信息
     * @param cosFilePath COS中的文件路径（对象键）
     * @return 文件元数据
     * @throws ServiceException 业务异常
     */
    public static ObjectMetadata getFileMetadata(String cosFilePath) {
        validateCosFilePath(cosFilePath);

        COSClient cosClient = getCosClient();

        try {
            ObjectMetadata metadata = cosClient.getObjectMetadata(
                    tencentCosConfig.getBucketName(), cosFilePath);
            log.info("获取文件元数据成功 - COS路径: {}, 文件大小: {} bytes, 最后修改时间: {}",
                    cosFilePath, metadata.getContentLength(), metadata.getLastModified());
            return metadata;

        } catch (CosServiceException e) {
            log.error("获取文件元数据失败 - COS路径: {}, 错误码: {}", cosFilePath, e.getErrorCode(), e);
            String errorMessage = getDownloadErrorMessage(e.getErrorCode());
            throw new ServiceException(errorMessage);

        } catch (Exception e) {
            log.error("获取文件元数据失败 - COS路径: {}", cosFilePath, e);
            throw new ServiceException("获取文件信息失败：" + e.getMessage());
        }
    }

// ==================== 私有辅助方法 ====================

    /**
     * 校验下载参数（字符串路径版本）
     */
    private static void validateDownloadParams(String cosFilePath, String localFilePath) {
        validateCosFilePath(cosFilePath);

        if (!StringUtils.hasText(localFilePath)) {
            throw new ServiceException("本地文件路径不能为空");
        }
    }

    /**
     * 校验下载参数（File对象版本）
     */
    private static void validateDownloadParams(String cosFilePath, File localFile) {
        validateCosFilePath(cosFilePath);

        if (localFile == null) {
            throw new ServiceException("本地文件对象不能为空");
        }

        // 检查父目录是否可写
        File parentDir = localFile.getParentFile();
        if (parentDir != null && parentDir.exists() && !parentDir.canWrite()) {
            throw new ServiceException("本地文件目录无写入权限：" + parentDir.getAbsolutePath());
        }
    }

    /**
     * 校验COS文件路径
     */
    private static void validateCosFilePath(String cosFilePath) {
        if (!StringUtils.hasText(cosFilePath)) {
            throw new ServiceException("COS文件路径不能为空");
        }

        // 移除开头的斜杠（如果有）
        if (cosFilePath.startsWith("/")) {
            cosFilePath = cosFilePath.substring(1);
        }
    }

    /**
     * 创建本地文件的父目录
     */
    private static void createParentDirectories(File localFile) {
        File parentDir = localFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            boolean created = parentDir.mkdirs();
            if (!created) {
                throw new ServiceException("创建本地目录失败：" + parentDir.getAbsolutePath());
            }
            log.info("创建本地目录：{}", parentDir.getAbsolutePath());
        }
    }

    /**
     * 根据错误码获取友好的错误信息
     */
    private static String getDownloadErrorMessage(String errorCode) {
        if (errorCode == null) {
            return "文件下载失败：未知错误";
        }

        switch (errorCode) {
            case "NoSuchKey":
                return "文件不存在，请检查文件路径是否正确";
            case "AccessDenied":
                return "访问被拒绝，请检查访问权限";
            case "InvalidBucketName":
                return "存储桶名称无效";
            case "NoSuchBucket":
                return "存储桶不存在";
            case "RequestTimeout":
                return "请求超时，请稍后重试";
            case "InternalError":
                return "服务器内部错误，请稍后重试";
            default:
                return "文件下载失败：" + errorCode;
        }
    }

}