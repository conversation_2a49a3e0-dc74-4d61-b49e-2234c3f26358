package com.drxin.common.utils.http;

import java.io.*;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.security.cert.X509Certificate;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.drxin.common.utils.Base64Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.drxin.common.constant.Constants;
import com.drxin.common.utils.StringUtils;
import org.springframework.http.MediaType;

/**
 * 通用http发送方法
 *
 * <AUTHOR>
 */
public class HttpUtils {
    private static final Logger log = LoggerFactory.getLogger(HttpUtils.class);

    /**
     * 向指定 URL 发送GET方法的请求
     *
     * @param url 发送请求的 URL
     * @return 所代表远程资源的响应结果
     */
    public static String sendGet(String url) {
        return sendGet(url, StringUtils.EMPTY);
    }

    /**
     * 向指定 URL 发送GET方法的请求
     *
     * @param url   发送请求的 URL
     * @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @return 所代表远程资源的响应结果
     */
    public static String sendGet(String url, String param) {
        return sendGet(url, param, Constants.UTF8);
    }

    /**
     * 向指定 URL 发送GET方法的请求
     *
     * @param url         发送请求的 URL
     * @param param       请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @param contentType 编码类型
     * @return 所代表远程资源的响应结果
     */
    public static String sendGet(String url, String param, String contentType) {
        StringBuilder result = new StringBuilder();
        BufferedReader in = null;
        try {
            String urlNameString = StringUtils.isNotBlank(param) ? url + "?" + param : url;
            log.info("sendGet - {}", urlNameString);
            URL realUrl = new URL(urlNameString);
            URLConnection connection = realUrl.openConnection();
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64)");
            connection.connect();
            in = new BufferedReader(new InputStreamReader(connection.getInputStream(), contentType));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            log.info("recv - {}", result);
        } catch (ConnectException e) {
            log.error("调用HttpUtils.sendGet ConnectException, url={},param={}", url, param, e);
        } catch (SocketTimeoutException e) {
            log.error("调用HttpUtils.sendGet SocketTimeoutException, url={},param={}", url, param, e);
        } catch (IOException e) {
            log.error("调用HttpUtils.sendGet IOException, url={},param={}", url, param, e);
        } catch (Exception e) {
            log.error("调用HttpsUtil.sendGet Exception, url={},param={}", url, param, e);
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception ex) {
                log.error("调用in.close Exception, url=" + url + ",param=" + param, ex);
            }
        }
        return result.toString();
    }

    /**
     * 向指定 URL 发送POST方法的请求
     *
     * @param url   发送请求的 URL
     * @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @return 所代表远程资源的响应结果
     */
    public static String sendPost(String url, String param) {
        return sendPost(url, param, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
    }

    /**
     * 向指定 URL 发送POST方法的请求
     *
     * @param url         发送请求的 URL
     * @param param       请求参数
     * @param contentType 内容类型
     * @return 所代表远程资源的响应结果
     */
    public static String sendPost(String url, String param, String contentType) {
        PrintWriter out = null;
        BufferedReader in = null;
        StringBuilder result = new StringBuilder();
        try {
            log.info("sendPost - {}", url);
            URLConnection conn = getUrlConnection(url, contentType);
            out = new PrintWriter(conn.getOutputStream());
            out.print(param);
            out.flush();
            in = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            log.info("recv - {}", result);
        } catch (ConnectException e) {
            log.error("调用HttpUtils.sendPost ConnectException, url={},param={}", url, param, e);
        } catch (SocketTimeoutException e) {
            log.error("调用HttpUtils.sendPost SocketTimeoutException, url={},param={}", url, param, e);
        } catch (IOException e) {
            log.error("调用HttpUtils.sendPost IOException, url={},param={}", url, param, e);
        } catch (Exception e) {
            log.error("调用HttpsUtil.sendPost Exception, url={},param={}", url, param, e);
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                log.error("调用in.close Exception, url={},param={}", url, param, ex);
            }
        }
        return result.toString();
    }

    /**
     * 发送post请求（上传文件）
     * @param url 地址
     * @param paramMap 参数
     * @return 返回结果
     */
    public static String sendPostMultipart(String url, Map<String, Object> paramMap) {
        try {
            HttpRequest request = HttpRequest.post(url); // 避免二次编码
            for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                if (value instanceof File) {
                    request.form(key, (File) value);
                } else if (value instanceof List<?>) {
                    List<?> list = (List<?>) value;
                    if (CollUtil.isNotEmpty(list)) {
                        for (Object obj : list) {
                            if (obj instanceof File) {
                                request.form(key, (File) obj);
                            } else {
                                request.form(key, Objects.toString(obj, ""));
                            }
                        }
                    }
                } else {
                    request.form(key, Objects.toString(value, ""));
                }
            }

            try (HttpResponse response = request.execute()) {
                return response.body();
            }
        } catch (Exception e) {
            log.error("sendPostMultipart 调用异常, url={}, paramMap={}", url, paramMap, e);
            return null;
        }
    }


    private static URLConnection getUrlConnection(String url, String contentType) throws IOException {
        URL realUrl = new URL(url);
        URLConnection conn = realUrl.openConnection();
        conn.setRequestProperty("accept", "*/*");
        conn.setRequestProperty("connection", "Keep-Alive");
        conn.setRequestProperty("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64)");
        conn.setRequestProperty("Accept-Charset", "utf-8");
        conn.setRequestProperty("Content-Type", contentType);
        conn.setDoOutput(true);
        conn.setDoInput(true);
        return conn;
    }

    public static String sendSSLPost(String url, String param) {
        return sendSSLPost(url, param, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
    }

    public static String sendSSLPost(String url, String param, String contentType) {
        StringBuilder result = new StringBuilder();
        String urlNameString = url + "?" + param;
        try {
            log.info("sendSSLPost - {}", urlNameString);
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, new TrustManager[]{new TrustAnyTrustManager()}, new java.security.SecureRandom());
            URL console = new URL(urlNameString);
            HttpsURLConnection conn = (HttpsURLConnection) console.openConnection();
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64)");
            conn.setRequestProperty("Accept-Charset", "utf-8");
            conn.setRequestProperty("Content-Type", contentType);
            conn.setDoOutput(true);
            conn.setDoInput(true);

            conn.setSSLSocketFactory(sc.getSocketFactory());
            conn.setHostnameVerifier(new TrustAnyHostnameVerifier());
            conn.connect();
            InputStream is = conn.getInputStream();
            BufferedReader br = new BufferedReader(new InputStreamReader(is));
            String ret = "";
            while ((ret = br.readLine()) != null) {
                if (!ret.trim().isEmpty()) {
                    result.append(new String(ret.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8));
                }
            }
            log.info("recv-result - {}", result);
            conn.disconnect();
            br.close();
        } catch (ConnectException e) {
            log.error("调用HttpUtils.sendSSLPost ConnectException, url={},param={}", url, param, e);
        } catch (SocketTimeoutException e) {
            log.error("调用HttpUtils.sendSSLPost SocketTimeoutException, url={},param={}", url, param, e);
        } catch (IOException e) {
            log.error("调用HttpUtils.sendSSLPost IOException, url={},param={}", url, param, e);
        } catch (Exception e) {
            log.error("调用HttpsUtil.sendSSLPost Exception, url={},param={}", url, param, e);
        }
        return result.toString();
    }

    private static class TrustAnyTrustManager implements X509TrustManager {
        @Override
        public void checkClientTrusted(X509Certificate[] chain, String authType) {
        }

        @Override
        public void checkServerTrusted(X509Certificate[] chain, String authType) {
        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[]{};
        }
    }

    private static class TrustAnyHostnameVerifier implements HostnameVerifier {
        @Override
        public boolean verify(String hostname, SSLSession session) {
            return true;
        }
    }


    /**
     * 发送 JSON 请求并返回字符串响应
     */
    public static String sendPostJsonReturnString(String url, String jsonBody) {
       try (HttpResponse response = HttpUtil.createPost(url).body(jsonBody)
               .contentType(MediaType.APPLICATION_JSON_VALUE).execute()){
           return response.body();
       }catch (Exception e){
            log.error("sendPostJsonReturnString 调用异常, url={}, body={}", url, jsonBody, e);
       }
       return null;
    }

    /**
     * 发送 JSON 请求并返回 base64 编码的响应体（用于图片、PDF等二进制响应）
     */
    public static String sendPostJsonReturnBase64(String url, String jsonBody) {
        try( HttpResponse response = HttpUtil.createPost(url).body(jsonBody)
                .contentType(MediaType.APPLICATION_JSON_VALUE).execute()) {
            return Base64Util.bytesToBase64(response.bodyBytes());
        } catch (Exception e) {
            log.error("sendPostJsonReturnBase64 调用异常, url={}, body={}", url, jsonBody, e);
        }
        return null;
    }
}
