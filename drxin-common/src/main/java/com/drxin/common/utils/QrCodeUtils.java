package com.drxin.common.utils;

import com.alibaba.fastjson2.JSON;
import com.drxin.common.utils.file.TencentOssUtils;
import com.drxin.common.utils.spring.SpringUtils;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

public class QrCodeUtils {

    public static String generateQrCodeWithBufferedLogo(String title, String content, BufferedImage logo)
            throws WriterException, IOException {
        int width = 300;
        int height = 300;
        int titleHeight = 40; // 额外高度留给标题

        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");

        BitMatrix bitMatrix = new MultiFormatWriter().encode(content, BarcodeFormat.QR_CODE, width, height, hints);

        // 创建包含标题区域的新图片
        BufferedImage qrImage = new BufferedImage(width, height + titleHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = qrImage.createGraphics();
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, width, height + titleHeight); // 背景白色

        // 画二维码本体
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                qrImage.setRGB(x, y, bitMatrix.get(x, y) ? Color.BLACK.getRGB() : Color.WHITE.getRGB());
            }
        }

        // 添加 logo 到二维码中间
        if (logo != null) {
            int logoWidth = width / 4;
            int logoHeight = height / 4;

            BufferedImage scaledLogo = new BufferedImage(logoWidth, logoHeight, BufferedImage.TYPE_INT_ARGB);
            Graphics2D g = scaledLogo.createGraphics();
            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            g.drawImage(logo, 0, 0, logoWidth, logoHeight, null);
            g.dispose();

            int x = (width - logoWidth) / 2;
            int y = (height - logoHeight) / 2;
            g2d.drawImage(scaledLogo, x, y, null);
        }

        // 添加标题
        if (title != null && !title.isEmpty()) {
            g2d.setColor(Color.BLACK);
            g2d.setFont(new Font("Microsoft YaHei", Font.PLAIN, 20));
            FontMetrics fm = g2d.getFontMetrics();
            int titleWidth = fm.stringWidth(title);
            int titleX = (width - titleWidth) / 2;
            int titleY = height + (titleHeight + fm.getAscent() - fm.getDescent()) / 2 - 4;
            g2d.drawString(title, titleX, titleY);
        }

        g2d.dispose();

        // 输出为 base64
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(qrImage, "png", baos);
        return "data:image/png;base64," + Base64.getEncoder().encodeToString(baos.toByteArray());
    }

    public static String generateAndUploadWxQrCode(String page, String scene, String fileName) {
        Map<String, Object> params = new HashMap<>();
        params.put("page", page);
        params.put("scene", scene);
        params.put("check_path", false);
        params.put("env_version", "release"); // 可根据需要改为入参 正式版为 "release"，体验版为 "trial"，开发版为 "develop"。默认是正式版。

        String paramJsonStr = JSON.toJSONString(params);

        // 获取小程序码 base64 字符串
        String base64Str = SpringUtils.getBean(WeChatMiniProgramUtil.class).getWxCode(paramJsonStr);
        if (base64Str == null || base64Str.isEmpty()) {
            throw new RuntimeException("小程序码生成失败");
        }

        // 转为输入流
        ByteArrayInputStream inputStream = Base64Util.base64ToInputStream(base64Str);

        // 上传到 OSS，返回 URL
        return TencentOssUtils.uploadFile(inputStream, fileName);
    }



}
