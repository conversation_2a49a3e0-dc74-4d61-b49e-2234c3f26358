package com.drxin.common.utils.file;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;

import javax.imageio.ImageIO;

import org.apache.poi.util.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.drxin.common.config.DrxinConfig;
import com.drxin.common.constant.Constants;
import com.drxin.common.utils.StringUtils;

/**
 * 图片处理工具类
 *
 * <AUTHOR>
 */
public class ImageUtils {
    private static final Logger log = LoggerFactory.getLogger(ImageUtils.class);

    // 图片压缩相关常量
    private static final int TARGET_WIDTH = 1280;
    private static final int TARGET_HEIGHT = 720;
    private static final long SIZE_THRESHOLD = 500 * 1024; // 500KB

    public static byte[] getImage(String imagePath) {
        InputStream is = getFile(imagePath);
        try {
            assert is != null;
            return IOUtils.toByteArray(is);
        } catch (Exception e) {
            log.error("图片加载异常", e);
            return null;
        } finally {
            IOUtils.closeQuietly(is);
        }
    }

    public static InputStream getFile(String imagePath) {
        try {
            byte[] result = readFile(imagePath);
            assert result != null;
            result = Arrays.copyOf(result, result.length);
            return new ByteArrayInputStream(result);
        } catch (Exception e) {
            log.error("获取图片异常", e);
        }
        return null;
    }

    /**
     * 读取文件为字节数据
     *
     * @param url 地址
     * @return 字节数据
     */
    public static byte[] readFile(String url) {
        InputStream in = null;
        try {
            if (url.startsWith("http")) {
                // 网络地址
                URL urlObj = new URL(url);
                URLConnection urlConnection = urlObj.openConnection();
                urlConnection.setConnectTimeout(30 * 1000);
                urlConnection.setReadTimeout(60 * 1000);
                urlConnection.setDoInput(true);
                in = urlConnection.getInputStream();
            } else {
                // 本机地址
                String localPath = DrxinConfig.getProfile();
                String downloadPath = localPath + StringUtils.substringAfter(url, Constants.RESOURCE_PREFIX);
                in = Files.newInputStream(Paths.get(downloadPath));
            }
            return IOUtils.toByteArray(in);
        } catch (Exception e) {
            log.error("获取文件路径异常", e);
            return null;
        } finally {
            IOUtils.closeQuietly(in);
        }
    }

    /**
     * 压缩图片
     * 当图片大小超过500KB或尺寸超过1280x720时进行压缩
     *
     * @param inputFile  输入文件
     * @param outputFile 输出文件
     * @throws IOException IO异常
     */
    public static void compressImage(File inputFile, File outputFile) throws IOException {
        // 检查文件大小是否小于阈值
        if (inputFile.length() < SIZE_THRESHOLD) {
            log.info("图片小于500KB，不进行压缩");
            return;
        }

        BufferedImage originalImage = ImageIO.read(inputFile);
        if (originalImage == null) {
            throw new IOException("无法读取图片文件: " + inputFile.getName());
        }

        int originalWidth = originalImage.getWidth();
        int originalHeight = originalImage.getHeight();

        // 检查图片尺寸是否小于目标尺寸
        if (originalWidth <= TARGET_WIDTH && originalHeight <= TARGET_HEIGHT) {
            log.info("图片尺寸小于{}x{}，不进行压缩", TARGET_WIDTH, TARGET_HEIGHT);
            return;
        }

        // 计算新尺寸，保持原始宽高比
        int newWidth, newHeight;
        double originalRatio = (double) originalWidth / originalHeight;
        double targetRatio = (double) TARGET_WIDTH / TARGET_HEIGHT;

        if (originalRatio > targetRatio) {
            // 原始图片更宽，以宽度为基准
            newWidth = TARGET_WIDTH;
            newHeight = (int) (TARGET_WIDTH / originalRatio);
        } else {
            // 原始图片更高或以相同比例，以高度为基准
            newHeight = TARGET_HEIGHT;
            newWidth = (int) (TARGET_HEIGHT * originalRatio);
        }

        log.info("压缩图片从 {}x{} 到 {}x{}", originalWidth, originalHeight, newWidth, newHeight);

        // 创建缩放后的图片
        BufferedImage resizedImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = resizedImage.createGraphics();
        g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g.drawImage(originalImage, 0, 0, newWidth, newHeight, null);
        g.dispose();

        // 获取文件扩展名
        String formatName = getFileExtension(inputFile.getName());

        // 写入输出文件
        ImageIO.write(resizedImage, formatName, outputFile);
    }

    /**
     * 获取文件扩展名
     *
     * @param fileName 文件名
     * @return 扩展名（不含点号）
     */
    private static String getFileExtension(String fileName) {
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex > 0) {
            return fileName.substring(dotIndex + 1).toLowerCase();
        }
        return "jpg"; // 默认使用jpg格式
    }
}
