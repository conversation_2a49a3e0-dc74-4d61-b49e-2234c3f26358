package com.drxin.common.utils;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.drxin.common.config.WxMiniConfig;
import com.drxin.common.constant.WxMiniProgramConstants;
import com.drxin.common.core.redis.RedisCache;
import com.drxin.common.exception.ServiceException;
import com.drxin.common.utils.http.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class WeChatMiniProgramUtil {

    @Resource
    private WxMiniConfig wxMiniConfig;

    @Resource
    private RedisCache redisCache;

    /**
     * 根据 code 获取 openid
     *
     * @param code 微信 code
     * @return openid
     */
    public String getOpenId(String code) {
        String url = UriComponentsBuilder
                .fromHttpUrl(WxMiniProgramConstants.JS_CODE_TO_SESSION_URL)
                .queryParam("appid", wxMiniConfig.getAppId())
                .queryParam("secret", wxMiniConfig.getAppSecret())
                .queryParam("js_code", code)
                .queryParam("grant_type", "authorization_code")
                .build()
                .toUriString();

        String response = HttpUtils.sendGet(url);
        JSONObject json = JSON.parseObject(response);
        return json.getString("openid");
    }

    /**
     * 获取assess_token
     */
    public String getAccessToken() {
        String accessToken = redisCache.getCacheObject(WxMiniProgramConstants.WXMINIPROGRAM_REDIS_KEY);
        if (StringUtils.isNotEmpty(accessToken)) {
            return accessToken;
        }
        String url = UriComponentsBuilder
                .fromHttpUrl(WxMiniProgramConstants.ACCESS_TOKEN_URL)
                .queryParam("appid", wxMiniConfig.getAppId())
                .queryParam("secret", wxMiniConfig.getAppSecret())
                .queryParam("grant_type", "client_credential")
                .build()
                .toUriString();

        String response = HttpUtils.sendGet(url);
        JSONObject json = JSON.parseObject(response);
        accessToken = json.getString("access_token");
        redisCache.setCacheObject(WxMiniProgramConstants.WXMINIPROGRAM_REDIS_KEY, accessToken, 5, TimeUnit.MINUTES);
        return accessToken;
    }

    /**
     * 获取小程序码 不受限制的
     */
    public String getWxCode(String scene) {
        String accessToken = getAccessToken();
        String url = UriComponentsBuilder.fromHttpUrl(WxMiniProgramConstants.GET_WXACODE_URL)
                .queryParam("access_token", accessToken).toUriString();
        return HttpUtils.sendPostJsonReturnBase64(url, scene);
    }

    /**
     * 身份证ID_CARD ocr识别 by imgUrl
     */
    public JSONObject getIDCardInfo(String url){
        String accessToken = getAccessToken();
        String url1 = UriComponentsBuilder.fromHttpUrl("https://api.weixin.qq.com/cv/ocr/idcard")
                .queryParam("access_token", accessToken)
                .toUriString();
        Map<String,Object> jsonBody = new HashMap<>();
        jsonBody.put("img_url",url);
        String body = HttpUtils.sendPostJsonReturnString(url1, JSON.toJSONString(jsonBody));
        JSONObject resultJson = JSON.parseObject(body);
        if (resultJson != null && resultJson.getIntValue("errcode") == 0) {
            return resultJson;
        }
        throw new ServiceException("身份证识别失败！！！");
    }

    /**
     * 身份证ID_CARD ocr识别 by file
     * @param file 文件
     * @return 结果
     */
    public JSONObject getIDCardInfo(File file){
        String accessToken = getAccessToken();
        String url1 = UriComponentsBuilder.fromHttpUrl("https://api.weixin.qq.com/cv/ocr/idcard")
                .queryParam("access_token", accessToken)
                .toUriString();
        Map<String, Object> img = MapUtil.of("img", file);
        String body = HttpUtils.sendPostMultipart(url1, img);
        JSONObject resultJson = JSON.parseObject(body);
        log.info("身份证识别结果: {}", resultJson);
        if (resultJson != null && resultJson.getIntValue("errcode") == 0) {
            return resultJson;
        }
        throw new ServiceException("身份证识别失败！！！");
    }
}
