package com.drxin.common.utils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;

public class Base64Util {
    public static String inputStreamToBase64(InputStream inputStream) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len;
        while ((len = inputStream.read(buffer)) != -1) {
            outputStream.write(buffer, 0, len);
        }
        return Base64.getEncoder().encodeToString(outputStream.toByteArray());
    }


    public static String bytesToBase64(byte[] bytes) {
        return Base64.getEncoder().encodeToString(bytes);
    }

    public static byte[] base64ToBytes(String base64Str) {
        if (base64Str.contains(",")) {
            base64Str = base64Str.split(",")[1];
        }
        return Base64.getDecoder().decode(base64Str);
    }

    public static boolean isBase64(String str) {
        try {
            Base64.getDecoder().decode(str);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    public static ByteArrayInputStream base64ToInputStream(String base64Str) {
        // 如果包含 data:image/png;base64, 这样的前缀，先去掉
        if (base64Str.contains(",")) {
            base64Str = base64Str.split(",")[1];
        }

        byte[] decodedBytes = Base64.getDecoder().decode(base64Str);
        return new ByteArrayInputStream(decodedBytes);
    }
}
