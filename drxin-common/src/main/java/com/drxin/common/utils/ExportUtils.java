package com.drxin.common.utils;

import cn.idev.excel.FastExcel;
import com.alibaba.fastjson2.JSONObject;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.common.utils.file.FileUtils;
import com.drxin.common.utils.file.TencentOssUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.zip.Deflater;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 文件导出工具类
 */
public class ExportUtils {

    private static final Logger logger = LoggerFactory.getLogger(ExportUtils.class);
    private static final int BUFFER_SIZE = 8192;
    private static final int MAX_DOWNLOAD_THREADS = 10;
    private static final long DOWNLOAD_TIMEOUT_MINUTES = 30;

    private static final ExecutorService downloadExecutor = Executors.newFixedThreadPool(MAX_DOWNLOAD_THREADS);

    /**
     * 构建导出文件名
     */
    public static String buildExportFileName(String beginTime, String endTime, String prefix) {
        String timestamp = DateUtils.parseDateToStr("yyyyMMddHHmmss", new Date());
        return String.format("%s_%s_%s_%s", prefix, beginTime, endTime, timestamp);
    }

    /**
     * 生成Excel文件
     */
    public static <T> File generateExcelFile(File tempDir, String fileName, List<T> data, Class<T> clazz, String sheetName) throws IOException {
        File excelFile = new File(tempDir, fileName + ".xlsx");

        try {
            FastExcel.write(excelFile, clazz)
                    .sheet(sheetName)
                    .doWrite(data);
            logger.debug("Excel文件生成完成: {}, 记录数: {}", excelFile.getName(), data.size());
            return excelFile;

        } catch (Exception e) {
            throw new IOException("生成Excel文件失败", e);
        }
    }

    /**
     * 并行下载文件
     *
     * @param downloadTasks 下载任务列表
     */
    public static void downloadFilesParallel(List<DownloadTask> downloadTasks) {
        if (downloadTasks.isEmpty()) {
            logger.debug("没有需要下载的文件");
            return;
        }

        logger.info("开始并行下载文件，总数: {}", downloadTasks.size());

        // 创建下载任务
        List<CompletableFuture<Void>> futures = downloadTasks.stream()
                .map(task -> CompletableFuture.runAsync(() -> downloadSingleFile(task), downloadExecutor))
                .collect(Collectors.toList());

        try {
            // 等待所有下载任务完成
            CompletableFuture<Void> allTasks = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0]));

            allTasks.get(DOWNLOAD_TIMEOUT_MINUTES, TimeUnit.MINUTES);
            logger.info("所有文件下载完成");

        } catch (Exception e) {
            logger.error("文件下载过程中出现异常", e);
            // 不抛出异常，允许继续导出
        }
    }

    /**
     * 下载任务类
     */
    public static class DownloadTask {
        private String fileUrl;
        private String fileName;
        private File targetDir;
        private String description;

        public DownloadTask(String fileUrl, String fileName, File targetDir, String description) {
            this.fileUrl = fileUrl;
            this.fileName = fileName;
            this.targetDir = targetDir;
            this.description = description;
        }

        // getters
        public String getFileUrl() {
            return fileUrl;
        }

        public String getFileName() {
            return fileName;
        }

        public File getTargetDir() {
            return targetDir;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 创建ZIP压缩包
     */
    public static File createZipFile(File tempDir, String fileName) throws IOException {
        File zipFile = new File(tempDir.getParent(), fileName + ".zip");

        try (FileOutputStream fos = new FileOutputStream(zipFile);
             BufferedOutputStream bos = new BufferedOutputStream(fos);
             ZipOutputStream zos = new ZipOutputStream(bos)) {

            zos.setLevel(Deflater.DEFAULT_COMPRESSION);

            File[] filesToZip = tempDir.listFiles();
            if (filesToZip == null || filesToZip.length == 0) {
                throw new IOException("临时目录为空，无法创建ZIP文件");
            }

            for (File file : filesToZip) {
                if (file.isFile()) {
                    addFileToZip(zos, file);
                }
            }

            logger.debug("ZIP文件创建完成: {}, 大小: {} KB",
                    zipFile.getName(), zipFile.length() / 1024);
            return zipFile;
        }
    }

    /**
     * 将ZIP文件写入响应流
     */
    public static void writeZipToResponse(File zipFile, HttpServletResponse response) throws IOException {
        response.setContentType("application/zip");
        response.setContentLengthLong(zipFile.length());
        response.setHeader("Content-Disposition",
                "attachment; filename=" + URLEncoder.encode(zipFile.getName(), StandardCharsets.UTF_8.name()));
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        try (ServletOutputStream out = response.getOutputStream();
             FileInputStream fis = new FileInputStream(zipFile);
             BufferedInputStream bis = new BufferedInputStream(fis)) {

            byte[] buffer = new byte[BUFFER_SIZE];
            int len;
            while ((len = bis.read(buffer)) != -1) {
                out.write(buffer, 0, len);
            }
            out.flush();
        }
    }

    /**
     * 清理资源
     */
    public static void cleanupResources(File tempDir, File zipFile) {
        try {
            if (tempDir != null && tempDir.exists()) {
                FileUtils.deleteDirectory(tempDir);
                logger.debug("临时目录清理完成: {}", tempDir.getAbsolutePath());
            }

            if (zipFile != null && zipFile.exists()) {
                boolean deleted = zipFile.delete();
                logger.debug("ZIP文件清理{}: {}", deleted ? "成功" : "失败", zipFile.getName());
            }
        } catch (Exception e) {
            logger.warn("清理资源时出现异常", e);
        }
    }

    /**
     * 处理导出异常
     */
    public static void handleExportError(HttpServletResponse response, Exception e) {
        try {
            response.reset();
            response.setContentType("application/json;charset=UTF-8");
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);

            String errorMessage = "导出失败: " + e.getMessage();
            response.getWriter().write(JSONObject.toJSONString(AjaxResult.error(errorMessage)));

        } catch (IOException ioException) {
            logger.error("写入错误响应失败", ioException);
        }
    }

    // ============ 私有方法 ============

    private static void downloadSingleFile(DownloadTask task) {
        try {
            File targetFile = new File(task.getTargetDir(), task.getFileName());
            String cosFileKey = getCosFileKey(task.getFileUrl());
            TencentOssUtils.downloadFile(cosFileKey, targetFile);

            logger.debug("文件下载成功: {}", task.getFileName());

        } catch (Exception e) {
            logger.warn("文件下载失败: {} - {}, 原因: {}",
                    task.getDescription(), task.getFileName(), e.getMessage());
        }
    }

    /**
     * 清理文件名中的非法字符
     */
    public static String sanitizeFileName(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return "unknown";
        }
        return fileName.replaceAll("[\\\\/:*?\"<>|]", "_");
    }

    private static void addFileToZip(ZipOutputStream zos, File file) throws IOException {
        try (FileInputStream fis = new FileInputStream(file);
             BufferedInputStream bis = new BufferedInputStream(fis)) {

            ZipEntry zipEntry = new ZipEntry(file.getName());
            zipEntry.setTime(file.lastModified());
            zos.putNextEntry(zipEntry);

            byte[] buffer = new byte[BUFFER_SIZE];
            int len;
            while ((len = bis.read(buffer)) != -1) {
                zos.write(buffer, 0, len);
            }

            zos.closeEntry();
        }
    }

    private static String getCosFileKey(String photoUrl) {
        // 保持原有逻辑
        return photoUrl;
    }

    /**
     * 关闭线程池
     */
    public static void shutdown() {
        downloadExecutor.shutdown();
        try {
            if (!downloadExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                downloadExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            downloadExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}