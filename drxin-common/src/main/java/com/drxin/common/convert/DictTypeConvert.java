package com.drxin.common.convert;

import cn.idev.excel.converters.Converter;
import cn.idev.excel.metadata.GlobalConfiguration;
import cn.idev.excel.metadata.data.WriteCellData;
import cn.idev.excel.metadata.property.ExcelContentProperty;
import com.drxin.common.annotation.DictTypeProperty;
import com.drxin.common.utils.DictUtils;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class DictTypeConvert implements Converter<String> {

    @Override
    public Class<String> supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        DictTypeProperty dictAnnotation = contentProperty.getField().getDeclaredAnnotation(DictTypeProperty.class);
        if (dictAnnotation == null) {
            throw new IllegalArgumentException("字段缺少@DictTypeProperty注解");
        }

        // 空值快速返回
        if (value == null || value.isEmpty()) {
            return new WriteCellData<>("");
        }

        // 统一处理逻辑（无需分隔符判断）
        String dictType = dictAnnotation.value();
        String[] values = value.split(DictUtils.SEPARATOR);  // 保留空值
        String label = Arrays.stream(values)
                .map(v -> DictUtils.getDictLabel(dictType, v))
                .collect(Collectors.joining(DictUtils.SEPARATOR));
        return new WriteCellData<>(label);
    }

}
