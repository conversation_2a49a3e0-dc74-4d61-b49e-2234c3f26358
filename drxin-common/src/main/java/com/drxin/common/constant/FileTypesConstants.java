package com.drxin.common.constant;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class FileTypesConstants {

    // 图片文件后缀集合
    public static final Set<String> IMAGE_EXTENSIONS = new HashSet<>(Arrays.asList(
            ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg", ".tiff", ".ico", ".heic"
    ));

    // 视频文件后缀集合
    public static final Set<String> VIDEO_EXTENSIONS = new HashSet<>(Arrays.asList(
            ".mp4", ".avi", ".mov", ".wmv", ".flv", ".mkv", ".webm", ".mpeg", ".mpg", ".3gp",
            ".m4v", ".ts", ".rm", ".rmvb"
    ));
}
