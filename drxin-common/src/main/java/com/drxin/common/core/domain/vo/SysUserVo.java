package com.drxin.common.core.domain.vo;

import com.drxin.common.annotation.Excel;
import com.drxin.common.annotation.Excels;
import com.drxin.common.core.domain.entity.SysDept;
import com.drxin.common.core.domain.entity.SysRole;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class SysUserVo implements Serializable {

    private static final long serialVersionUID = 7409530886940388419L;
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 用户账号
     */
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    private String userType;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 手机号码
     */
    private String phonenumber;

    /**
     * 用户性别
     */
    private String sex;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 最后登录时间
     */
    private Date loginDate;

    private SysDept dept;

    /**
     * 角色对象
     */
    private List<SysRole> roles;

    /**
     * 微信openid
     */
    private String openid;

    private boolean guest = false;

    private String inviterCode;

    private String realName;

    private String idCard;

    private String cardType;

    private String contribution;
}
