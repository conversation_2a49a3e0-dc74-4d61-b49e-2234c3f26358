-- 活动评审相关表结构

-- 1. 活动评审主表
CREATE TABLE `activity_review` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `activity_title` varchar(200) NOT NULL COMMENT '活动主题',
  `start_date` datetime NOT NULL COMMENT '开始时间',
  `end_date` datetime NOT NULL COMMENT '结束时间',
  `lecturer_name` varchar(100) NOT NULL COMMENT '讲师姓名',
  `lecturer_phone` varchar(20) DEFAULT NULL COMMENT '讲师电话',
  `lecturer` varchar(100) DEFAULT NULL COMMENT '讲师',
  `attendee_count` int(11) NOT NULL COMMENT '参与人数',
  `activity_description` text COMMENT '活动描述',
  `review_status` char(1) NOT NULL DEFAULT '1' COMMENT '审核状态（1-新建 2-待审核 3-审核通过 4-审核拒绝）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_create_by` (`create_by`),
  KEY `idx_review_status` (`review_status`),
  KEY `idx_start_date` (`start_date`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动评审表';

-- 2. 活动评审附件表
CREATE TABLE `activity_review_attachments` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `activity_id` bigint(20) NOT NULL COMMENT '关联活动ID',
  `attachment_id` varchar(100) DEFAULT NULL COMMENT '附件编号',
  `attachment_url` varchar(500) NOT NULL COMMENT '附件URL',
  `attachment_type` varchar(50) DEFAULT NULL COMMENT '附件类型',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_activity_id` (`activity_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动评审附件表';

-- 3. 活动评审人员关联表
CREATE TABLE `activity_review_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `activity_id` bigint(20) NOT NULL COMMENT '关联活动ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `role_type` tinyint(1) NOT NULL COMMENT '角色类型（0-主办人 1-协办人）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_activity_id` (`activity_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role_type` (`role_type`),
  UNIQUE KEY `uk_activity_user_role` (`activity_id`, `user_id`, `role_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动评审人员关联表';

-- 添加外键约束（可选）
-- ALTER TABLE `activity_review_attachments` ADD CONSTRAINT `fk_attachment_activity` FOREIGN KEY (`activity_id`) REFERENCES `activity_review` (`id`) ON DELETE CASCADE;
-- ALTER TABLE `activity_review_user` ADD CONSTRAINT `fk_user_activity` FOREIGN KEY (`activity_id`) REFERENCES `activity_review` (`id`) ON DELETE CASCADE;
