-- 微信小程序菜单管理相关表结构

-- 菜单表
CREATE TABLE wx_menus (
  id INT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(50) NOT NULL COMMENT '菜单名称',
  icon VARCHAR(50) NOT NULL COMMENT '图标名称',
  path VARCHAR(200) NOT NULL COMMENT '页面路径',
  menu_type VARCHAR(20) NOT NULL COMMENT '菜单类型：personal/function',
  show_for VARCHAR(200) COMMENT '显示给哪些角色，如 general,aider',
  sort_order INT DEFAULT 0 COMMENT '排序权重',
  status TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用',
  description VARCHAR(200) COMMENT '菜单描述',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_menu_type (menu_type),
  INDEX idx_status (status),
  INDEX idx_sort_order (sort_order)
) COMMENT='微信小程序菜单配置表';



-- 插入初始菜单数据
INSERT INTO wx_menus (id, title, icon, path, menu_type, show_for, sort_order, description) VALUES
-- 个人设置菜单
(1, '账户设置', 'setting-fill', '/pageA/profile', 'personal', 'all', 1, '用户账户基本设置'),
(2, '个人资料', 'account-fill', '/pageA/personal', 'personal', 'aider,mentor,disciple', 2, '完善个人详细信息'),
(3, '地址管理', 'map-fill', '/pageA/user_address/list', 'personal', 'aider,mentor,disciple', 3, '管理收货地址信息'),
(4, '我的邀请码', 'share-fill', '/pageA/invite_code', 'personal', 'mentor,disciple', 4, '查看和分享邀请码'),

-- 功能菜单
(5, '证件申请', 'order-fill', '/pageA/apply/list', 'function', 'aider,mentor,disciple', 1, '申请相关资质证件'),
(6, '活动上报', 'calendar-fill', '/pageA/activity_review/list', 'function', 'aider,mentor,disciple', 2, '上报和管理活动信息'),
(7, '急救案例', 'heart-fill', '/pageA/rescue_case/list', 'function', 'aider,mentor,disciple', 3, '记录和分享急救案例'),
(8, '急救员申请', 'plus-circle-fill', '/pageA/user_apply/index?userType=aider', 'function', 'general', 4, '普通用户申请成为急救员'),
(9, '导师申请', 'star-fill', '/pageA/user_apply/index?userType=mentor', 'function', 'general,aider', 5, '申请成为急救导师');
