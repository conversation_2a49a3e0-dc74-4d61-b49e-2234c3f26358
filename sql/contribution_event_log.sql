-- 贡献值事件日志表
-- 用于记录所有贡献值事件，防止策略处理失败后无记录可查

CREATE TABLE `contribution_event_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `action_code` varchar(50) NOT NULL COMMENT '行为编码',
  `event_source` varchar(100) DEFAULT NULL COMMENT '事件来源类',
  `context_data` text COMMENT '事件上下文数据(JSON格式)',
  `process_status` char(1) NOT NULL DEFAULT '0' COMMENT '处理状态(0-待处理 1-处理成功 2-处理失败)',
  `process_message` varchar(500) DEFAULT NULL COMMENT '处理结果消息',
  `process_time` datetime DEFAULT NULL COMMENT '处理时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action_code` (`action_code`),
  KEY `idx_process_status` (`process_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='贡献值事件日志表';

-- 处理状态说明
-- 0: 待处理 - 事件已记录，等待策略处理
-- 1: 处理成功 - 策略处理成功，贡献值已计算和记录
-- 2: 处理失败 - 策略处理失败，需要人工干预或重试
