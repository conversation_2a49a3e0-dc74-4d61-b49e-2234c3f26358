package com.drxin.quartz.task;

import com.drxin.bizz.service.ICourseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component("drxinTask")
@Slf4j
public class DrxinTask {
    @Resource
    private ICourseService courseService;
    // 定时清理课程状态
    public void cleanCourseStatus() {
        log.info("定时任务：清理课程状态");
        // 这里可以添加具体的业务逻辑代码
        courseService.cleanCourseStatus();
    }
}
