package com.drxin.bizz.constants;

import lombok.Getter;

@Getter
public enum UserTypeRoleMapConstants {

    // 普通用户
    GENERAL("general", 2, "普通用户"),
    // 急救员
    AIDER("aider", 100, "急救员"),
    // 急救导师
    MENTOR("mentor", 101, "急救导师"),
    // 弟子
    DISCIPLE("disciple", 102, "弟子"),
    // 创始人
    FOUNDER("founder", 103, "创始人"),
    // 联合创始人
    UNION_FOUNDER("union_founder", 104, "联合创始人");

    private final String userType;
    private final int roleId;

    UserTypeRoleMapConstants(String userType, int roleId, String userTypeLabel) {
        this.userType = userType;
        this.roleId = roleId;
    }


}
