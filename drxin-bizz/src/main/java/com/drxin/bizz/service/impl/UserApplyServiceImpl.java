package com.drxin.bizz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.drxin.bizz.constants.UserTypeRoleMapConstants;
import com.drxin.bizz.domain.AuditResult;
import com.drxin.bizz.domain.UserApply;
import com.drxin.bizz.mapper.UserApplyMapper;
import com.drxin.bizz.service.IUserApplyService;
import com.drxin.common.core.domain.entity.SysUser;
import com.drxin.common.exception.ServiceException;
import com.drxin.common.utils.DateUtils;
import com.drxin.common.utils.SecurityUtils;
import com.drxin.framework.event.ContributionActionEvent;
import com.drxin.system.domain.SysUserRole;
import com.drxin.system.mapper.SysUserMapper;
import com.drxin.system.mapper.SysUserRoleMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户身份申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Slf4j
@Service
public class UserApplyServiceImpl extends ServiceImpl<UserApplyMapper, UserApply>  implements IUserApplyService {
    @Resource
    private UserApplyMapper userApplyMapper;

    @Resource
    private SysUserMapper userMapper;

    @Resource
    private SysUserRoleMapper userRoleMapper;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Resource
    private PlatformTransactionManager transactionManager;


    /**
     * 查询用户身份申请
     * 
     * @param id 用户身份申请主键
     * @return 用户身份申请
     */
    @Override
    public UserApply selectUserApplyById(Long id) {
        return userApplyMapper.selectUserApplyById(id);
    }

    /**
     * 查询用户身份申请列表
     * 
     * @param userApply 用户身份申请
     * @return 用户身份申请
     */
    @Override
    public List<UserApply> selectUserApplyList(UserApply userApply) {
        return userApplyMapper.selectUserApplyList(userApply);
    }

    /**
     * 新增用户身份申请
     *
     * @param userApply 用户身份申请
     * @return 结果
     */
    @Override
    public int insertUserApply(UserApply userApply) {
        // 检查用户是否已有审核中的申请
        if (userApply.getUserId() != null) {
            int pendingCount = userApplyMapper.countPendingApplyByUserId(userApply.getUserId());
            if (pendingCount > 0) {
                throw new ServiceException("您已有申请正在审核中，请勿重复申请");
            }
        }

        // 如果用户没有申请过，则判断用户身份证是否已存在用户表中且user_id不是当前的userId
        // 先查询用户是否有任何申请记录
        UserApply queryApply = new UserApply();
        queryApply.setUserId(userApply.getUserId());
        List<UserApply> existingApplies = userApplyMapper.selectUserApplyList(queryApply);

        if (existingApplies.isEmpty() && userApply.getIdCard() != null && !userApply.getIdCard().trim().isEmpty()) {
            SysUser existingUser = userMapper.checkIdCardUnique(userApply.getIdCard().trim());
            if (existingUser != null && !existingUser.getUserId().equals(userApply.getUserId())) {
                throw new ServiceException("该身份证号已被其他用户使用，请检查后重新填写");
            }
        }

        userApply.setCreateTime(DateUtils.getNowDate());
        return userApplyMapper.insertUserApply(userApply);
    }

    /**
     * 修改用户身份申请
     * 
     * @param userApply 用户身份申请
     * @return 结果
     */
    @Override
    public int updateUserApply(UserApply userApply) {
        userApply.setUpdateTime(DateUtils.getNowDate());
        return userApplyMapper.updateUserApply(userApply);
    }

    /**
     * 批量删除用户身份申请
     * 
     * @param ids 需要删除的用户身份申请主键
     * @return 结果
     */
    @Override
    public int deleteUserApplyByIds(Long[] ids) {
        return userApplyMapper.deleteUserApplyByIds(ids);
    }

    /**
     * 删除用户身份申请信息
     *
     * @param id 用户身份申请主键
     * @return 结果
     */
    @Override
    public int deleteUserApplyById(Long id) {
        return userApplyMapper.deleteUserApplyById(id);
    }

    /**
     * 审批用户身份申请（支持批量）
     * 使用手动事务管理，支持部分成功场景
     * 每个申请在独立事务中处理，失败的不影响成功的
     *
     * @param ids 申请ID数组
     * @param type 审批类型 (pass-通过, reject-拒绝)
     * @param rejectReason 驳回原因（当type为reject时使用）
     * @return 审核结果对象，包含成功/失败统计和详细信息
     */
    @Override
    public AuditResult auditUserApply(Long[] ids, String type, String rejectReason) {
        if (ids == null || ids.length == 0) {
            throw new RuntimeException("申请ID列表不能为空");
        }
        if (type == null || type.trim().isEmpty()) {
            throw new RuntimeException("审批类型不能为空");
        }

        // 验证审批类型并获取状态码
        String status = validateAndGetStatus(type);

        // 初始化统计变量
        int successCount = 0;
        int failCount = 0;
        StringBuilder errorMessages = new StringBuilder();
        List<Long> successIds = new ArrayList<>();
        List<Long> failIds = new ArrayList<>();
        Map<String, List<Long>> successIdsByUserType = new HashMap<>();

        // 批量查询所有申请记录，避免在循环中逐个查询
        List<Long> idList = Arrays.asList(ids);
        List<UserApply> userApplies = userApplyMapper.selectBatchIds(idList);
        Set<Long> remainingIds = new HashSet<>(idList);

        // 处理查询到的申请记录
        for (UserApply userApply : userApplies) {
            Long id = userApply.getId();
            remainingIds.remove(id);

            log.debug("开始处理申请ID: {}", id);

            // 预检查申请状态
            if (!"2".equals(userApply.getApplyStatus())) {
                String errorMsg = "申请ID[" + id + "]已处理，无法重复审批，当前状态: " + userApply.getApplyStatus();
                failCount++;
                failIds.add(id);
                errorMessages.append(errorMsg).append("; ");
                log.warn(errorMsg);
                continue;
            }

            // 在独立事务中处理申请
            if (processApplyInTransaction(userApply, status, type, rejectReason)) {
                successCount++;
                successIds.add(id);
                // 按用户类型分组成功的申请ID
                String userType = userApply.getUserType();
                if (userType != null) {
                    successIdsByUserType.computeIfAbsent(userType, k -> new ArrayList<>()).add(id);
                }
                log.info("申请ID[{}]处理成功", id);
            } else {
                String errorMsg = "申请ID[" + id + "]事务处理失败";
                failCount++;
                failIds.add(id);
                errorMessages.append(errorMsg).append("; ");
            }
        }

        // 处理不存在的申请ID
        for (Long id : remainingIds) {
            String errorMsg = "申请ID[" + id + "]不存在";
            failCount++;
            failIds.add(id);
            errorMessages.append(errorMsg).append("; ");
            log.warn(errorMsg);
        }

        // 记录批量处理结果
        log.info("批量审批完成: 总数={}, 成功数={}, 失败数={}", ids.length, successCount, failCount);

        if (successCount > 0) {
            log.info("成功处理的申请ID: {}", successIds);
        }
        if (failCount > 0) {
            log.warn("失败处理的申请ID: {}, 错误信息: {}", failIds, errorMessages);
        }

        // 构建审核结果并发布事件
        AuditResult result = new AuditResult(successCount, failCount, successIds, failIds, errorMessages.toString());
        publishEventsIfNeeded(type, result, successIdsByUserType, successIds);

        return result;
    }



    /**
     * 更新用户身份类型和个人资料
     */
    private void updateUserTypeAndInfo(UserApply userApply) {
        try {
            Long userId = userApply.getUserId();
            String userType = userApply.getUserType();
            // 获取用户类型对应的角色ID
            UserTypeRoleMapConstants userTypeRoleMapConstants = UserTypeRoleMapConstants.valueOf(userType.toUpperCase());
            int roleId = userTypeRoleMapConstants.getRoleId();

            // 删除用户原有的角色
            userRoleMapper.deleteUserRoleByUserId(userId);

            UpdateWrapper<SysUser> updateWrapper = getSysUserUpdateWrapper(userApply, userId, userType);

            int update = userMapper.update(null, updateWrapper);
            if (update <= 0) {
                throw new ServiceException("更新用户身份和资料失败，可能是用户身份证号在用户表中已存在");
            }

            // 添加新的角色
            List<SysUserRole> sysUserRoleList = new ArrayList<>();
            // 添加新的身份角色
            if (!UserTypeRoleMapConstants.GENERAL.getUserType().equals(userType)) {
                sysUserRoleList.add(new SysUserRole(userId, roleId));
            }
            userRoleMapper.batchUserRole(sysUserRoleList);

        } catch (IllegalArgumentException e) {
            throw new ServiceException("不支持的用户类型: " + userApply.getUserType());
        } catch (Exception e) {
            throw new RuntimeException("更新用户身份和资料失败: " + e.getMessage());
        }
    }

    private UpdateWrapper<SysUser> getSysUserUpdateWrapper(UserApply userApply, Long userId, String userType) {
        UpdateWrapper<SysUser> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("user_id", userId);
        updateWrapper.set("user_type", userType);

        // 更新个人资料
        if (userApply.getRealName() != null && !userApply.getRealName().trim().isEmpty()) {
            updateWrapper.set("real_name", userApply.getRealName());
        }
        if (userApply.getIdCard() != null && !userApply.getIdCard().trim().isEmpty()) {
            updateWrapper.set("id_card", userApply.getIdCard());
        }
        if (userApply.getCardType() != null && !userApply.getCardType().trim().isEmpty()) {
            updateWrapper.set("card_type", userApply.getCardType());
        }
        if (userApply.getSex() != null && !userApply.getSex().trim().isEmpty()) {
            updateWrapper.set("sex", userApply.getSex());
        }
        if (userApply.getPhoneNumber() != null && !userApply.getPhoneNumber().trim().isEmpty()) {
            updateWrapper.set("phonenumber", userApply.getPhoneNumber());
        }

        if (userApply.getInviterId() != null) {
            // 判断邀请人身份是否是导师、弟子、创始人、联合创始人
            if (userApply.getInviterId().equals(userId)) {
                throw new ServiceException("邀请人ID不能是申请人自己");
            }
            LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysUser::getUserId, userApply.getInviterId())
                        .in(SysUser::getUserType, UserTypeRoleMapConstants.MENTOR.getUserType(),
                            UserTypeRoleMapConstants.DISCIPLE.getUserType(),
                            UserTypeRoleMapConstants.FOUNDER.getUserType(),
                            UserTypeRoleMapConstants.UNION_FOUNDER.getUserType());
            SysUser inviter = userMapper.selectOne(queryWrapper);
            if (inviter == null) {
                updateWrapper.set("inviter_id", null);
                updateWrapper.set("deal_inviter_id", null);
            }else {
                // 设置成交人ID和邀请人ID
                updateWrapper.set("deal_inviter_id", userApply.getInviterId());
                updateWrapper.set("inviter_id", userApply.getInviterId());
            }
        }else {
            // 如果邀请人ID为空，则清空成交人ID和邀请人ID
            updateWrapper.setSql("deal_inviter_id = inviter_id ");
        }
        // 设置更新时间
        updateWrapper.set("upgraded_time", DateUtils.getNowDate());
        return updateWrapper;
    }

    /**
     * 批量发布用户身份通过贡献值事件
     *
     * @param successIdsByUserType 按用户类型分组的成功申请ID
     */
    private void publishBatchUserIdentityApprovedEvent(Map<String, List<Long>> successIdsByUserType) {
        try {
            int totalCount = successIdsByUserType.values().stream().mapToInt(List::size).sum();
            log.info("开始发布批量用户身份通过贡献值事件: 成功申请数量={}", totalCount);

            // 为每种用户类型发布对应的事件
            for (Map.Entry<String, List<Long>> entry : successIdsByUserType.entrySet()) {
                String userType = entry.getKey();
                List<Long> typeApplyIds = entry.getValue();

                // 根据用户类型确定事件代码
                String eventCode = getEventCodeByUserType(userType);
                if (eventCode == null) {
                    log.warn("未知的用户类型，跳过事件发布: userType={}, applyIds={}", userType, typeApplyIds);
                    continue;
                }

                // 构建事件上下文
                Map<String, Object> context = new HashMap<>();
                context.put("successApplyIds", typeApplyIds);
                context.put("batchSize", typeApplyIds.size());
                context.put("userType", userType);

                // 获取当前操作用户ID作为事件的用户ID
                Long currentUserId = SecurityUtils.getUserId();

                // 创建并发布贡献值事件
                ContributionActionEvent event = new ContributionActionEvent(
                    this, currentUserId, eventCode, context);
                eventPublisher.publishEvent(event);

                log.info("发布用户类型[{}]贡献值事件: eventCode={}, 申请数量={}",
                    userType, eventCode, typeApplyIds.size());
            }

            log.info("批量用户身份通过贡献值事件发布完成: 总申请数量={}", totalCount);

        } catch (Exception e) {
            // 记录日志但不影响主流程
            log.error("发布批量用户身份通过贡献值事件失败: successIdsByUserType={}", successIdsByUserType, e);
        }
    }

    /**
     * 根据用户类型获取对应的事件代码
     *
     * @param userType 用户类型
     * @return 事件代码
     */
    private String getEventCodeByUserType(String userType) {
        if (userType == null) {
            return null;
        }

        switch (userType.toUpperCase()) {
            case "AIDER":
                return "IDENTITY_AIDER";
            case "MENTOR":
                return "IDENTITY_MENTOR";
            default:
                log.warn("未支持的用户类型: {}", userType);
                return null;
        }
    }

    /**
     * 批量发布推荐成功贡献值事件
     *
     * @param successApplyIds 成功的申请ID列表
     */
    private void publishBatchRecommendSuccessEvent(List<Long> successApplyIds) {
        try {
            log.info("开始发布批量推荐成功贡献值事件: 成功申请数量={}", successApplyIds.size());

            if (successApplyIds.isEmpty()) {
                log.warn("成功申请ID列表为空，跳过推荐成功事件发布");
                return;
            }

            // 批量查询申请记录，收集推荐人和成交人信息
            List<UserApply> userApplies = userApplyMapper.selectBatchIds(successApplyIds);
            List<Long> userIds = userApplies.stream()
                    .map(UserApply::getUserId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (userIds.isEmpty()) {
                log.warn("未找到有效的用户ID，跳过推荐成功事件发布");
                return;
            }

            // 批量查询用户信息，获取成交人信息
            List<SysUser> users = userMapper.selectBatchIds(userIds);
            Map<Long, SysUser> userMap = users.stream()
                    .collect(Collectors.toMap(SysUser::getUserId, Function.identity()));

            // 收集推荐人和成交人信息，并按用户类型分组
            Map<String, List<Map<String, Object>>> recommendInfoByUserType = new HashMap<>();
            for (UserApply userApply : userApplies) {
                if (userApply.getUserId() == null) continue;

                SysUser user = userMap.get(userApply.getUserId());
                if (user == null) continue;

                Map<String, Object> recommendInfo = new HashMap<>();
                recommendInfo.put("applyId", userApply.getId());
                recommendInfo.put("userId", userApply.getUserId());
                recommendInfo.put("userType", userApply.getUserType());
                recommendInfo.put("inviterId", userApply.getInviterId()); // 推荐人ID
                recommendInfo.put("dealInviterId", user.getDealInviterId()); // 成交人ID
                recommendInfo.put("realName", userApply.getRealName());

                // 按用户类型分组
                String userType = userApply.getUserType();
                if (userType != null) {
                    recommendInfoByUserType.computeIfAbsent(userType, k -> new ArrayList<>()).add(recommendInfo);
                }
            }

            if (recommendInfoByUserType.isEmpty()) {
                log.warn("未收集到推荐信息，跳过推荐成功事件发布");
                return;
            }

            // 获取当前操作用户ID作为事件的用户ID
            Long currentUserId = SecurityUtils.getUserId();

            // 根据用户类型分别发布不同的推荐成功事件
            for (Map.Entry<String, List<Map<String, Object>>> entry : recommendInfoByUserType.entrySet()) {
                String userType = entry.getKey();
                List<Map<String, Object>> typeRecommendInfoList = entry.getValue();

                // 根据用户类型确定事件代码
                String eventCode = getRecommendEventCodeByUserType(userType);
                if (eventCode == null) {
                    log.warn("未知的用户类型，跳过推荐成功事件发布: userType={}, 推荐信息数量={}",
                        userType, typeRecommendInfoList.size());
                    continue;
                }

                // 构建事件上下文
                Map<String, Object> context = new HashMap<>();
                context.put("successApplyIds", typeRecommendInfoList.stream()
                    .map(info -> (Long) info.get("applyId"))
                    .collect(Collectors.toList()));
                context.put("recommendInfoList", typeRecommendInfoList);
                context.put("batchSize", typeRecommendInfoList.size());
                context.put("userType", userType);

                // 创建并发布推荐成功贡献值事件
                ContributionActionEvent event = new ContributionActionEvent(
                    this, currentUserId, eventCode, context);
                eventPublisher.publishEvent(event);

                log.info("推荐成功贡献值事件发布完成: userType={}, eventCode={}, 推荐信息数量={}",
                    userType, eventCode, typeRecommendInfoList.size());
            }

            int totalCount = recommendInfoByUserType.values().stream().mapToInt(List::size).sum();
            log.info("批量推荐成功贡献值事件发布完成: 总申请数量={}, 总推荐信息数量={}",
                successApplyIds.size(), totalCount);

        } catch (Exception e) {
            // 记录日志但不影响主流程
            log.error("发布批量推荐成功贡献值事件失败: successApplyIds={}", successApplyIds, e);
        }
    }

    /**
     * 根据用户类型获取推荐成功事件代码
     *
     * @param userType 用户类型
     * @return 事件代码
     */
    private String getRecommendEventCodeByUserType(String userType) {
        if (userType == null) {
            return null;
        }

        switch (userType.toUpperCase()) {
            case "AIDER":
                return "RECOMMEND_SUCCESS_AIDER";
            case "MENTOR":
                return "RECOMMEND_SUCCESS_MENTOR";
            default:
                log.warn("未支持的推荐成功用户类型: {}", userType);
                return null;
        }
    }

    /**
     * 验证审批类型并返回对应的状态码
     *
     * @param type 审批类型
     * @return 状态码
     */
    private String validateAndGetStatus(String type) {
        switch (type.toLowerCase()) {
            case "pass":
                return "3"; // 通过
            case "reject":
                return "4"; // 拒绝
            default:
                throw new RuntimeException("无效的审批类型，只支持 pass 或 reject");
        }
    }



    /**
     * 在独立事务中处理申请
     * 保持完整的事务管理机制，包括创建、提交、回滚
     *
     * @param userApply 申请对象
     * @param status 目标状态
     * @param type 审批类型
     * @param rejectReason 驳回原因
     * @return 处理结果，true表示成功，false表示失败
     */
    private boolean processApplyInTransaction(UserApply userApply, String status, String type, String rejectReason) {
        Long id = userApply.getId();
        log.debug("申请ID[{}]预检查通过，开启独立事务进行数据修改", id);

        // 创建独立事务定义
        DefaultTransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
        transactionDefinition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        transactionDefinition.setIsolationLevel(TransactionDefinition.ISOLATION_DEFAULT);
        transactionDefinition.setTimeout(30); // 30秒超时

        TransactionStatus transactionStatus = transactionManager.getTransaction(transactionDefinition);

        try {
            // 事务内操作：更新申请状态
            UserApply updateApply = new UserApply();
            updateApply.setId(id);
            updateApply.setApplyStatus(status);
            updateApply.setUpdateTime(DateUtils.getNowDate());

            // 如果是拒绝操作且提供了驳回原因，则设置驳回原因
            if ("reject".equalsIgnoreCase(type) && rejectReason != null && !rejectReason.trim().isEmpty()) {
                updateApply.setRejectReason(rejectReason.trim());
            }

            int result = userApplyMapper.updateUserApply(updateApply);
            if (result <= 0) {
                throw new RuntimeException("更新申请状态失败");
            }

            // 事务内操作：如果是审核通过，更新用户身份和资料
            if ("pass".equalsIgnoreCase(type) && userApply.getUserId() != null) {
                updateUserTypeAndInfo(userApply);
            }

            // 提交当前事务
            transactionManager.commit(transactionStatus);
            log.info("申请ID[{}]处理成功，事务已提交", id);
            return true;

        } catch (Exception e) {
            // 发生异常，回滚当前事务
            try {
                transactionManager.rollback(transactionStatus);
                log.error("申请ID[{}]数据修改失败，事务已回滚", id, e);
            } catch (Exception rollbackException) {
                log.error("申请ID[{}]事务回滚失败", id, rollbackException);
            }
            return false;
        }
    }

    /**
     * 根据处理结果发布相应的事件
     *
     * @param type 审批类型
     * @param result 审核结果
     * @param successIdsByUserType 按用户类型分组的成功ID
     * @param successIds 成功ID列表
     */
    private void publishEventsIfNeeded(String type, AuditResult result,
                                      Map<String, List<Long>> successIdsByUserType, List<Long> successIds) {
        if (result.isAllFailed()) {
            log.error("批量审批全部失败: 总数={}, 失败原因: {}",
                result.getSuccessCount() + result.getFailCount(), result.getErrorMessage());
        } else if (result.hasPartialSuccess()) {
            log.warn("批量审批部分成功: 成功{}条，失败{}条。失败原因: {}",
                result.getSuccessCount(), result.getFailCount(), result.getErrorMessage());
            publishApprovalEvents(type, successIdsByUserType, successIds);
        } else {
            log.info("批量审批全部成功: 成功{}条", result.getSuccessCount());
            publishApprovalEvents(type, successIdsByUserType, successIds);
        }
    }

    /**
     * 发布审批通过相关的事件
     *
     * @param type 审批类型
     * @param successIdsByUserType 按用户类型分组的成功ID
     * @param successIds 成功ID列表
     */
    private void publishApprovalEvents(String type, Map<String, List<Long>> successIdsByUserType, List<Long> successIds) {
        if ("pass".equalsIgnoreCase(type) && !successIdsByUserType.isEmpty()) {
            // 发布用户身份通过贡献值事件
            publishBatchUserIdentityApprovedEvent(successIdsByUserType);
            // 发布推荐成功贡献值事件
            publishBatchRecommendSuccessEvent(successIds);
            // 发布物料发放事件
            publishMaterialDistributionEvent(successIdsByUserType, successIds);
        }
    }

    /**
     * 发布物料发放事件
     *
     * @param successIdsByUserType 按用户类型分组的成功ID
     * @param successIds 成功ID列表
     */
    private void publishMaterialDistributionEvent(Map<String, List<Long>> successIdsByUserType, List<Long> successIds) {
        try {
            log.info("开始发布物料发放事件: 总申请数量={}, 用户类型数量={}",
                successIds.size(), successIdsByUserType.size());

            // 构建事件上下文
            Map<String, Object> context = new HashMap<>();
            context.put("successApplyIds", successIds);
            context.put("successIdsByUserType", successIdsByUserType);
            context.put("batchSize", successIds.size());
            context.put("auditTime", new Date());

            // 获取当前操作用户ID作为事件的用户ID
            Long currentUserId = SecurityUtils.getUserId();

            // 创建并发布物料发放事件
            ContributionActionEvent event = new ContributionActionEvent(
                this, currentUserId, "MATERIAL_DISTRIBUTION", context);
            eventPublisher.publishEvent(event);

            log.info("物料发放事件发布成功: 申请数量={}", successIds.size());

        } catch (Exception e) {
            // 记录日志但不影响主流程
            log.error("发布物料发放事件失败: successIdsByUserType={}, successIds={}",
                successIdsByUserType, successIds, e);
        }
    }
}
