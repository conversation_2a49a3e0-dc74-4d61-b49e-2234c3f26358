package com.drxin.bizz.service;


import com.drxin.bizz.domain.UserInfoCollection;
import com.drxin.bizz.vo.UserInfoCollectionVo;

import java.util.List;

/**
 * 用户信息收集Service接口
 *
 * <AUTHOR>
 * @date 2025-05-18
 */
public interface IUserInfoCollectionService {
    /**
     * 查询用户信息收集
     *
     * @param id 用户信息收集主键
     * @return 用户信息收集
     */
    public UserInfoCollection selectUserInfoCollectionById(Long id);

    /**
     * 查询用户信息收集列表
     *
     * @param userInfoCollection 用户信息收集
     * @return 用户信息收集集合
     */
    public List<UserInfoCollection> selectUserInfoCollectionList(UserInfoCollectionVo userInfoCollection);

    /**
     * 新增用户信息收集
     *
     * @param userInfoCollection 用户信息收集
     * @return 结果
     */
    public int insertUserInfoCollection(UserInfoCollection userInfoCollection);

    /**
     * 修改用户信息收集
     *
     * @param userInfoCollection 用户信息收集
     * @return 结果
     */
    public int updateUserInfoCollection(UserInfoCollection userInfoCollection);

    /**
     * 批量删除用户信息收集
     *
     * @param ids 需要删除的用户信息收集主键集合
     * @return 结果
     */
    public int deleteUserInfoCollectionByIds(Long[] ids);

    /**
     * 删除用户信息收集信息
     *
     * @param id 用户信息收集主键
     * @return 结果
     */
    public int deleteUserInfoCollectionById(Long id);
}
