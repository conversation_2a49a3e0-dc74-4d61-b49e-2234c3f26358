package com.drxin.bizz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;
import com.drxin.bizz.domain.UserAddress;

/**
 * 用户地址管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-02
 */
public interface IUserAddressService extends IService<UserAddress> {

    /**
     * 查询用户地址管理
     * 
     * @param id 用户地址管理主键
     * @return 用户地址管理
     */
    public UserAddress selectUserAddressById(String id);

    /**
     * 查询用户地址管理列表
     * 
     * @param userAddress 用户地址管理
     * @return 用户地址管理集合
     */
    public List<UserAddress> selectUserAddressList(UserAddress userAddress);

    /**
     * 新增用户地址管理
     * 
     * @param userAddress 用户地址管理
     * @return 结果
     */
    public int insertUserAddress(UserAddress userAddress);

    /**
     * 修改用户地址管理
     * 
     * @param userAddress 用户地址管理
     * @return 结果
     */
    public int updateUserAddress(UserAddress userAddress);

    /**
     * 批量删除用户地址管理
     * 
     * @param ids 需要删除的用户地址管理主键集合
     * @return 结果
     */
    public int deleteUserAddressByIds(String[] ids);

    /**
     * 删除用户地址管理信息
     * 
     * @param id 用户地址管理主键
     * @return 结果
     */
    public int deleteUserAddressById(String id);
}
