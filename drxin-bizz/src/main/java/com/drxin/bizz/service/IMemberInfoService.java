package com.drxin.bizz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.drxin.bizz.domain.MemberInfo;
import com.drxin.bizz.vo.MemberUpdateTypeVo;
import com.drxin.bizz.vo.MemberAiderStatVo;
import com.drxin.bizz.vo.MemberAiderStatExportVo;

import java.util.List;

public interface IMemberInfoService extends IService<MemberInfo> {
    List<MemberInfo> selectMemberInfoList(MemberInfo memberInfo);

    MemberInfo selectMemberInfoById(Long id);

    int updateMemberType(MemberUpdateTypeVo memberUpdateTypeVo);

    List<MemberInfo> selectMemberInfoListToSelect(MemberUpdateTypeVo memberUpdateTypeVo);

    /**
     * 更新成员成交人
     * 注意：方法名为updateMemberInviter但实际更新的是成交人(deal_inviter_id)
     */
    int updateMemberInviter(String ids, String newInviterId);

    /**
     * 获取推荐列表
     * @param memberInfo 查询条件
     * @return 推荐用户列表
     */
    List<MemberInfo> getRecommendList(MemberInfo memberInfo);

    /**
     * 查询成员急救员统计列表
     * @param memberAiderStatVo 查询条件
     * @return 成员急救员统计列表
     */
    List<MemberAiderStatVo> selectMemberAiderStatList(MemberAiderStatVo memberAiderStatVo);

    /**
     * 查询成员急救员统计导出列表
     * @param memberAiderStatVo 查询条件
     * @return 成员急救员统计导出列表
     */
    List<MemberAiderStatExportVo> selectMemberAiderStatExportList(MemberAiderStatVo memberAiderStatVo);
}
