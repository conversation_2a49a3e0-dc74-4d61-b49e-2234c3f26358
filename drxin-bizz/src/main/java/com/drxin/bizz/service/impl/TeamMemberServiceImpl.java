package com.drxin.bizz.service.impl;

import java.util.List;
import com.drxin.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.drxin.bizz.mapper.TeamMemberMapper;
import com.drxin.bizz.domain.TeamMember;
import com.drxin.bizz.service.ITeamMemberService;

/**
 * 团队成员Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class TeamMemberServiceImpl extends ServiceImpl<TeamMemberMapper, TeamMember> implements ITeamMemberService {
    @Autowired
    private TeamMemberMapper teamMemberMapper;

    /**
     * 查询团队成员
     * 
     * @param memberId 团队成员主键
     * @return 团队成员
     */
    @Override
    public TeamMember selectTeamMemberById(String memberId) {
        return teamMemberMapper.selectTeamMemberById(memberId);
    }

    /**
     * 查询团队成员列表
     * 
     * @param teamMember 团队成员
     * @return 团队成员
     */
    @Override
    public List<TeamMember> selectTeamMemberList(TeamMember teamMember) {
        return teamMemberMapper.selectTeamMemberList(teamMember);
    }

    /**
     * 新增团队成员
     * 
     * @param teamMember 团队成员
     * @return 结果
     */
    @Override
    public int insertTeamMember(TeamMember teamMember) {
        teamMember.setCreateTime(DateUtils.getNowDate());
        return teamMemberMapper.insertTeamMember(teamMember);
    }

    /**
     * 修改团队成员
     * 
     * @param teamMember 团队成员
     * @return 结果
     */
    @Override
    public int updateTeamMember(TeamMember teamMember) {
        teamMember.setUpdateTime(DateUtils.getNowDate());
        return teamMemberMapper.updateTeamMember(teamMember);
    }

    /**
     * 批量删除团队成员
     * 
     * @param memberIds 需要删除的团队成员主键
     * @return 结果
     */
    @Override
    public int deleteTeamMemberByIds(String[] memberIds) {
        return teamMemberMapper.deleteTeamMemberByIds(memberIds);
    }

    /**
     * 删除团队成员信息
     * 
     * @param memberId 团队成员主键
     * @return 结果
     */
    @Override
    public int deleteTeamMemberById(String memberId) {
        return teamMemberMapper.deleteTeamMemberById(memberId);
    }
}
