package com.drxin.bizz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.drxin.bizz.domain.ActivityReview;
import com.drxin.bizz.vo.ActivityReviewVo;

import java.util.List;

/**
 * 活动评审Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface IActivityReviewService extends IService<ActivityReview> {

    /**
     * 查询活动评审
     * 
     * @param id 活动评审主键
     * @return 活动评审
     */
    ActivityReview selectActivityReviewById(Long id);

    /**
     * 查询活动评审列表
     * 
     * @param activityReview 活动评审
     * @return 活动评审集合
     */
    List<ActivityReviewVo> selectActivityReviewList(ActivityReview activityReview);

    /**
     * 新增活动评审
     * 
     * @param activityReview 活动评审
     * @return 结果
     */
    int insertActivityReview(ActivityReview activityReview);

    /**
     * 修改活动评审
     * 
     * @param activityReview 活动评审
     * @return 结果
     */
    int updateActivityReview(ActivityReview activityReview);

    /**
     * 批量删除活动评审
     * 
     * @param ids 需要删除的活动评审主键集合
     * @return 结果
     */
    int deleteActivityReviewByIds(Long[] ids);

    /**
     * 删除活动评审信息
     *
     * @param id 活动评审主键
     * @return 结果
     */
    int deleteActivityReviewById(Long id);

    /**
     * 批量审批活动评审
     *
     * @param ids 活动评审ID数组
     * @param status 审批状态
     * @return 结果
     */
    int batchAuditActivityReview(Long[] ids, String status);
}
