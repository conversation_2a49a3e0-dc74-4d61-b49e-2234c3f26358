package com.drxin.bizz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
import com.drxin.common.utils.DateUtils;
import com.drxin.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.drxin.bizz.mapper.TeamMapper;
import com.drxin.bizz.domain.Team;
import com.drxin.bizz.service.ITeamService;
import com.drxin.framework.event.ContributionActionEvent;
import com.drxin.bizz.domain.ContributionEventLog;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 团队管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Slf4j
@Service
public class TeamServiceImpl extends ServiceImpl<TeamMapper, Team>  implements ITeamService {
    @Resource
    private TeamMapper teamMapper;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    /**
     * 查询团队管理
     * 
     * @param teamId 团队管理主键
     * @return 团队管理
     */
    @Override
    public Team selectTeamByTeamId(Long teamId) {
        return teamMapper.selectTeamByTeamId(teamId);
    }

    /**
     * 查询团队管理列表
     * 
     * @param team 团队管理
     * @return 团队管理
     */
    @Override
    public List<Team> selectTeamList(Team team) {
        return teamMapper.selectTeamList(team);
    }

    /**
     * 新增团队管理
     *
     * @param team 团队管理
     * @return 结果
     */
    @Override
    @Transactional
    public int insertTeam(Team team) {
        team.setCreateTime(DateUtils.getNowDate());
        int result = teamMapper.insertTeam(team);

        // 团队创建成功后，发布事件异步添加团队成员
        if (result > 0 && StringUtils.isNotEmpty(team.getLeaderId())) {
            publishTeamMemberAutoAddEvent(team);
        }

        return result;
    }

    /**
     * 修改团队管理
     * 
     * @param team 团队管理
     * @return 结果
     */
    @Override
    public int updateTeam(Team team) {
        return teamMapper.updateTeam(team);
    }

    /**
     * 批量删除团队管理
     * 
     * @param teamIds 需要删除的团队管理主键
     * @return 结果
     */
    @Override
    public int deleteTeamByTeamIds(Long[] teamIds) {
        return teamMapper.deleteTeamByTeamIds(teamIds);
    }

    /**
     * 删除团队管理信息
     *
     * @param teamId 团队管理主键
     * @return 结果
     */
    @Override
    public int deleteTeamByTeamId(Long teamId) {
        return teamMapper.deleteTeamByTeamId(teamId);
    }

    /**
     * 发布团队成员自动添加事件
     *
     * @param team 团队信息
     */
    private void publishTeamMemberAutoAddEvent(Team team) {
        try {
            // 构建事件上下文
            Map<String, Object> context = new HashMap<>();
            context.put("teamId", team.getTeamId().toString());
            context.put("leaderId", team.getLeaderId());
            context.put("teamName", team.getTeamName());

            // 创建贡献值事件（使用团长ID作为事件用户）
            ContributionActionEvent event = new ContributionActionEvent(
                this,
                Long.valueOf(team.getLeaderId()),
                ContributionEventLog.ACTION_TEAM_MEMBER_AUTO_ADD,
                context
            );

            eventPublisher.publishEvent(event);

            log.info("团队成员自动添加事件发布成功: teamId={}, leaderId={}, teamName={}",
                team.getTeamId(), team.getLeaderId(), team.getTeamName());

        } catch (Exception e) {
            // 记录日志但不影响主流程
            log.error("发布团队成员自动添加事件失败: teamId={}, leaderId={}",
                team.getTeamId(), team.getLeaderId(), e);
        }
    }
}
