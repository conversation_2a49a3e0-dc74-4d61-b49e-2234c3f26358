package com.drxin.bizz.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.drxin.bizz.domain.Course;
import com.drxin.bizz.mapper.CourseMapper;
import com.drxin.bizz.service.ICourseEnrollmentService;
import com.drxin.bizz.service.ICourseService;
import com.drxin.common.exception.ServiceException;
import com.drxin.common.utils.QrCodeUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 课程信息管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Service
public class CourseServiceImpl extends ServiceImpl<CourseMapper, Course>  implements ICourseService {
    @Resource
    private CourseMapper courseMapper;

    @Resource
    private ICourseEnrollmentService courseEnrollmentService;

    /**
     * 查询课程信息管理
     * 
     * @param id 课程信息管理主键
     * @return 课程信息管理
     */
    @Override
    public Course selectCourseById(Long id) {
        return courseMapper.selectCourseById(id);
    }


    @Override
    public List<Course> selectCourseList(Course course) {
        return courseMapper.selectCourseList(course);
    }

    /**
     * 新增课程信息管理
     * 
     * @param course 课程信息管理
     * @return 结果
     */
    @Override
    public int insertCourse(Course course) {
        int result = courseMapper.insertCourse(course);
        // 生成小程序二维码
        String pagePath = "pageA/course/detail";
        String scene = "courseId=" + course.getId();
        String fileName = course.getId() + "_courseDetail" + course.getCourseName() + ".png";
        String courseQRUrl = QrCodeUtils.generateAndUploadWxQrCode(pagePath, scene, fileName);
        pagePath = "pageA/checkin/index";
        fileName = course.getId() + "_checkin_" + course.getCourseName() + ".png";
        String checkinQRUrl = QrCodeUtils.generateAndUploadWxQrCode(pagePath, scene, fileName);
        course.setInfoQrUrl(courseQRUrl);
        course.setCheckinQrUrl(checkinQRUrl);
        // 更新课程二维码信息
        UpdateWrapper<Course> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", course.getId())
                .set("info_qr_url", courseQRUrl)
                .set("checkin_qr_url", checkinQRUrl);
        baseMapper.update(null, updateWrapper);
        return result;
    }

    /**
     * 修改课程信息管理
     * 
     * @param course 课程信息管理
     * @return 结果
     */
    @Override
    public int updateCourse(Course course) {
        return courseMapper.updateCourse(course);
    }

    /**
     * 批量删除课程信息管理
     * 
     * @param ids 需要删除的课程信息管理主键
     * @return 结果
     */
    @Override
    public int deleteCourseByIds(Long[] ids) {
        return courseMapper.deleteCourseByIds(ids);
    }

    @Override
    public int deleteCourseById(Long id) {
        // 检查是否有人报名
        Long count = courseEnrollmentService.countEnrollmentsByCourseId(id);
        if (count > 0) {
            throw new RuntimeException("该课程有用户报名，无法删除");
        }

        return courseMapper.deleteCourseById(id);
    }

    @Override
    public List<Course> getLatestCourse() {
        List<Course> courseList = courseMapper.selectLatestCourse();
        if (courseList == null || courseList.isEmpty()) {
            throw new ServiceException("没有最新课程信息");
        }
        return courseList;
    }

    @Override
    public List<String> getCurrentMonthCourse() {
        List<Course> courses = baseMapper.selectCurrentMonthCourse();
        Set<String> daySet = new HashSet<>();

        for (Course course : courses) {
            LocalDate startDate = course.getStartTime().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
            LocalDate endDate = course.getEndTime().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();

            // 遍历开始到结束的每一天
            while (!startDate.isAfter(endDate)) {
                daySet.add(String.valueOf(startDate.getDayOfMonth()));
                startDate = startDate.plusDays(1);
            }
        }

        // 排序后返回
        return daySet.stream()
                .sorted(Comparator.comparingInt(Integer::parseInt))
                .collect(Collectors.toList());
    }

    @Override
    public void cleanCourseStatus() {
        courseMapper.cleanCourseStatus();
    }
}
