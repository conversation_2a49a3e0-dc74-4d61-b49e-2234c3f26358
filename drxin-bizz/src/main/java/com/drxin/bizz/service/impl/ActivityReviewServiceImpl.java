package com.drxin.bizz.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.drxin.bizz.domain.ActivityReview;
import com.drxin.bizz.domain.ActivityReviewAttachment;
import com.drxin.bizz.domain.ActivityReviewUser;
import com.drxin.bizz.mapper.ActivityReviewMapper;
import com.drxin.bizz.mapper.ActivityReviewAttachmentMapper;
import com.drxin.bizz.mapper.ActivityReviewUserMapper;
import com.drxin.bizz.service.IActivityReviewService;
import com.drxin.bizz.vo.ActivityReviewVo;
import com.drxin.common.utils.DateUtils;
import com.drxin.common.utils.SecurityUtils;
import com.drxin.framework.event.ContributionActionEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 活动评审Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Slf4j
@Service
public class ActivityReviewServiceImpl extends ServiceImpl<ActivityReviewMapper, ActivityReview> implements IActivityReviewService {

    @Resource
    private ActivityReviewMapper activityReviewMapper;

    @Resource
    private ActivityReviewAttachmentMapper activityReviewAttachmentMapper;

    @Resource
    private ActivityReviewUserMapper activityReviewUserMapper;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    /**
     * 查询活动评审
     * 
     * @param id 活动评审主键
     * @return 活动评审
     */
    @Override
    public ActivityReview selectActivityReviewById(Long id) {
        return activityReviewMapper.selectActivityReviewById(id);
    }

    /**
     * 查询活动评审列表
     * 
     * @param activityReview 活动评审
     * @return 活动评审
     */
    @Override
    public List<ActivityReviewVo> selectActivityReviewList(ActivityReview activityReview) {
        List<ActivityReviewVo> activityReviews = activityReviewMapper.selectActivityReviewList(activityReview);
        
        // 循环查询活动附件和人员信息
        for (ActivityReviewVo reviewItem : activityReviews) {
            // 查询附件
            List<ActivityReviewAttachment> attachments = activityReviewAttachmentMapper.selectAttachmentsByActivityId(reviewItem.getId());
            reviewItem.setAttachments(attachments);

            // 查询所有人员，然后按角色类型分组
            List<ActivityReviewUser> allUsers = activityReviewUserMapper.selectUsersByActivityId(reviewItem.getId());

            // 使用Stream按角色类型分组
            List<ActivityReviewUser> organizers = allUsers.stream()
                    .filter(user -> user.getRoleType() == 0)
                    .collect(Collectors.toList());
            List<ActivityReviewUser> assistants = allUsers.stream()
                    .filter(user -> user.getRoleType() == 1)
                    .collect(Collectors.toList());

            reviewItem.setOrganizers(organizers);
            reviewItem.setAssistants(assistants);
        }
        
        return activityReviews;
    }

    /**
     * 新增活动评审
     * 
     * @param activityReview 活动评审
     * @return 结果
     */
    @Override
    public int insertActivityReview(ActivityReview activityReview) {
        activityReview.setCreateTime(DateUtils.getNowDate());
        int result = activityReviewMapper.insertActivityReview(activityReview);

        // 保存附件信息
        if (result > 0 && activityReview.getAttachments() != null && !activityReview.getAttachments().isEmpty()) {
            for (ActivityReviewAttachment attachment : activityReview.getAttachments()) {
                attachment.setActivityId(activityReview.getId());
                attachment.setCreateTime(DateUtils.getNowDate());
                activityReviewAttachmentMapper.insertActivityReviewAttachment(attachment);
            }
        }

        // 保存主办人和协办人信息
        if (result > 0) {
            List<ActivityReviewUser> allUsers = new ArrayList<>();

            // 添加主办人
            if (activityReview.getOrganizers() != null && !activityReview.getOrganizers().isEmpty()) {
                activityReview.getOrganizers().forEach(organizer -> {
                    organizer.setActivityId(activityReview.getId());
                    organizer.setRoleType(0); // 主办人
                    organizer.setCreateTime(DateUtils.getNowDate());
                });
                allUsers.addAll(activityReview.getOrganizers());
            }

            // 添加协办人
            if (activityReview.getAssistants() != null && !activityReview.getAssistants().isEmpty()) {
                activityReview.getAssistants().forEach(assistant -> {
                    assistant.setActivityId(activityReview.getId());
                    assistant.setRoleType(1); // 协办人
                    assistant.setCreateTime(DateUtils.getNowDate());
                });
                allUsers.addAll(activityReview.getAssistants());
            }

            // 批量保存
            if (!allUsers.isEmpty()) {
                activityReviewUserMapper.insertBatch(allUsers);
            }
        }

        return result;
    }

    /**
     * 修改活动评审
     * 
     * @param activityReview 活动评审
     * @return 结果
     */
    @Override
    public int updateActivityReview(ActivityReview activityReview) {
        activityReview.setUpdateTime(DateUtils.getNowDate());
        int result = activityReviewMapper.updateActivityReview(activityReview);

        // 更新附件信息
        if (result > 0 && activityReview.getAttachments() != null) {
            // 先删除原有的附件记录
            activityReviewAttachmentMapper.deleteAttachmentsByActivityId(activityReview.getId());

            // 重新插入附件记录
            if (!activityReview.getAttachments().isEmpty()) {
                for (ActivityReviewAttachment attachment : activityReview.getAttachments()) {
                    attachment.setActivityId(activityReview.getId());
                    attachment.setCreateTime(DateUtils.getNowDate());
                    activityReviewAttachmentMapper.insertActivityReviewAttachment(attachment);
                }
            }
        }

        // 更新人员信息
        if (result > 0) {
            // 先删除原有的人员关联记录
            activityReviewUserMapper.deleteUsersByActivityId(activityReview.getId());

            List<ActivityReviewUser> allUsers = new ArrayList<>();

            // 添加主办人
            if (activityReview.getOrganizers() != null && !activityReview.getOrganizers().isEmpty()) {
                activityReview.getOrganizers().forEach(organizer -> {
                    organizer.setActivityId(activityReview.getId());
                    organizer.setRoleType(0); // 主办人
                    organizer.setCreateTime(DateUtils.getNowDate());
                });
                allUsers.addAll(activityReview.getOrganizers());
            }

            // 添加协办人
            if (activityReview.getAssistants() != null && !activityReview.getAssistants().isEmpty()) {
                activityReview.getAssistants().forEach(assistant -> {
                    assistant.setActivityId(activityReview.getId());
                    assistant.setRoleType(1); // 协办人
                    assistant.setCreateTime(DateUtils.getNowDate());
                });
                allUsers.addAll(activityReview.getAssistants());
            }

            // 批量保存
            if (!allUsers.isEmpty()) {
                activityReviewUserMapper.insertBatch(allUsers);
            }
        }

        return result;
    }

    /**
     * 批量删除活动评审
     * 
     * @param ids 需要删除的活动评审主键
     * @return 结果
     */
    @Override
    public int deleteActivityReviewByIds(Long[] ids) {
        // 先删除相关的附件记录
        activityReviewAttachmentMapper.delete(new LambdaUpdateWrapper<ActivityReviewAttachment>()
                .in(ActivityReviewAttachment::getActivityId, Arrays.asList(ids)));
        
        // 删除相关的人员关联记录
        activityReviewUserMapper.delete(new LambdaUpdateWrapper<ActivityReviewUser>()
                .in(ActivityReviewUser::getActivityId, Arrays.asList(ids)));
        
        return activityReviewMapper.deleteActivityReviewByIds(ids);
    }

    /**
     * 删除活动评审信息
     *
     * @param id 活动评审主键
     * @return 结果
     */
    @Override
    public int deleteActivityReviewById(Long id) {
        // 先删除相关的附件记录
        activityReviewAttachmentMapper.deleteAttachmentsByActivityId(id);
        
        // 删除相关的人员关联记录
        activityReviewUserMapper.deleteUsersByActivityId(id);
        
        return activityReviewMapper.deleteActivityReviewById(id);
    }

    /**
     * 批量审批活动评审
     *
     * @param ids 活动评审ID数组
     * @param status 审批状态
     * @return 结果
     */
    @Override
    public int batchAuditActivityReview(Long[] ids, String status) {
        int result = 0;
        for (Long id : ids) {
            ActivityReview activityReview = new ActivityReview();
            activityReview.setId(id);
            activityReview.setReviewStatus(status);
            activityReview.setUpdateTime(DateUtils.getNowDate());
            int updateResult = activityReviewMapper.updateActivityReview(activityReview);
            result += updateResult;

            // 审批通过时发布贡献值事件
            if (updateResult > 0 && "3".equals(status)) {
                publishActivityApprovedEvent(id);
            }
        }
        return result;
    }

    /**
     * 发布活动审批通过事件
     *
     * @param activityId 活动ID
     */
    private void publishActivityApprovedEvent(Long activityId) {
        try {
            log.info("开始发布活动审批通过贡献值事件: activityId={}", activityId);

            // 查询活动详情
            ActivityReview activityReview = activityReviewMapper.selectActivityReviewById(activityId);
            if (activityReview == null) {
                log.warn("活动不存在，无法发布贡献值事件: activityId={}", activityId);
                return;
            }

            // 查询活动的主办人和协办人
            List<ActivityReviewUser> allUsers = activityReviewUserMapper.selectUsersByActivityId(activityId);
            if (allUsers == null || allUsers.isEmpty()) {
                log.info("活动无主办人和协办人，跳过贡献值事件发布: activityId={}", activityId);
                return;
            }

            // 分组获取主办人和协办人
            List<ActivityReviewUser> organizers = allUsers.stream()
                    .filter(user -> user.getRoleType() == 0)
                    .collect(Collectors.toList());
            List<ActivityReviewUser> assistants = allUsers.stream()
                    .filter(user -> user.getRoleType() == 1)
                    .collect(Collectors.toList());

            // 发布主办人贡献值事件
            if (!organizers.isEmpty()) {
                publishContributionEvent(activityReview, organizers, "ACTIVITY_ORGANIZER");
            }

            // 发布协办人贡献值事件
            if (!assistants.isEmpty()) {
                publishContributionEvent(activityReview, assistants, "ACTIVITY_ASSISTANT");
            }

            log.info("活动审批通过贡献值事件发布完成: activityId={}, 主办人数量={}, 协办人数量={}",
                activityId, organizers.size(), assistants.size());

        } catch (Exception e) {
            // 记录日志但不影响主流程
            log.error("发布活动审批通过贡献值事件失败: activityId={}", activityId, e);
        }
    }

    /**
     * 发布贡献值事件
     *
     * @param activityReview 活动信息
     * @param users 用户列表
     * @param actionCode 事件代码
     */
    private void publishContributionEvent(ActivityReview activityReview, List<ActivityReviewUser> users, String actionCode) {
        try {
            // 提取用户ID列表
            List<Long> userIds = users.stream()
                    .map(ActivityReviewUser::getUserId)
                    .collect(Collectors.toList());

            log.info("发布{}贡献值事件: activityId={}, userIds={}", actionCode, activityReview.getId(), userIds);

            // 构建事件上下文
            Map<String, Object> context = new HashMap<>();
            context.put("activityId", activityReview.getId());
            context.put("activityTitle", activityReview.getActivityTitle());
            context.put("startDate", activityReview.getStartDate());
            context.put("endDate", activityReview.getEndDate());
            context.put("lecturerName", activityReview.getLecturerName());
            context.put("activityDescription", activityReview.getActivityDescription());

            // 批量用户数据
            context.put("userIds", userIds);

            // 创建贡献值事件（使用第一个用户ID作为代表）
            ContributionActionEvent event = new ContributionActionEvent(
                    this, SecurityUtils.getUserId(), actionCode, context);

            // 发布事件，由监听器异步处理
            eventPublisher.publishEvent(event);

            log.info("{}贡献值事件发布成功: activityId={}, userCount={}",
                actionCode, activityReview.getId(), userIds.size());

        } catch (Exception e) {
            // 记录日志但不影响主流程
            log.error("发布贡献值事件失败: activityId={}, actionCode={}",
                activityReview.getId(), actionCode, e);
        }
    }
}
