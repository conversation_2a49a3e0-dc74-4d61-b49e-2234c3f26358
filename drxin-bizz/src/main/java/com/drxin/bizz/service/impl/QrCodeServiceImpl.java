package com.drxin.bizz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

import com.drxin.common.config.DrxinConfig;
import com.drxin.common.exception.ServiceException;
import com.drxin.common.utils.QrCodeUtils;
import com.google.zxing.WriterException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import com.drxin.bizz.mapper.QrCodeMapper;
import com.drxin.bizz.domain.QrCode;
import com.drxin.bizz.service.IQrCodeService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;

/**
 * 二维码信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Service
public class QrCodeServiceImpl extends ServiceImpl<QrCodeMapper, QrCode> implements IQrCodeService {
    @Autowired
    private QrCodeMapper qrCodeMapper;

    /**
     * 查询二维码信息
     *
     * @param id 二维码信息主键
     * @return 二维码信息
     */
    @Override
    public QrCode selectQrCodeById(Long id) {
        return qrCodeMapper.selectQrCodeById(id);
    }

    /**
     * 查询二维码信息列表
     *
     * @param qrCode 二维码信息
     * @return 二维码信息
     */
    @Override
    public List<QrCode> selectQrCodeList(QrCode qrCode) {
        return qrCodeMapper.selectQrCodeList(qrCode);
    }

    /**
     * 新增二维码信息
     *
     * @param qrCode 二维码信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertQrCode(QrCode qrCode, MultipartFile logoFile) {
        // 获取resource文件夹下的logo图片
        Resource resource = new ClassPathResource("static/logo/logo.png");
        try {
            InputStream inputStream = resource.getInputStream();
            BufferedImage image = ImageIO.read(inputStream);
            String qrCodeImg = QrCodeUtils.generateQrCodeWithBufferedLogo(qrCode.getDescription(), qrCode.getContent(), image);
            qrCode.setQrCodeBase64(qrCodeImg);
        } catch (IOException e) {
            throw new ServiceException("获取logo图片失败");
        } catch (WriterException e) {
            throw new ServiceException("生成二维码失败");
        }
        return qrCodeMapper.insertQrCode(qrCode);
    }

    /**
     * 修改二维码信息
     *
     * @param qrCode 二维码信息
     * @return 结果
     */
    @Override
    public int updateQrCode(QrCode qrCode) {
        return qrCodeMapper.updateQrCode(qrCode);
    }

    /**
     * 批量删除二维码信息
     *
     * @param ids 需要删除的二维码信息主键
     * @return 结果
     */
    @Override
    public int deleteQrCodeByIds(Long[] ids) {
        return qrCodeMapper.deleteQrCodeByIds(ids);
    }

    /**
     * 删除二维码信息信息
     *
     * @param id 二维码信息主键
     * @return 结果
     */
    @Override
    public int deleteQrCodeById(Long id) {
        return qrCodeMapper.deleteQrCodeById(id);
    }
}
