package com.drxin.bizz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Collections;
import java.util.List;

import com.drxin.bizz.vo.CourseAttendExportVo;
import com.drxin.bizz.vo.CourseAttendanceVo;
import com.drxin.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.drxin.bizz.mapper.CourseAttendanceMapper;
import com.drxin.bizz.domain.CourseAttendance;
import com.drxin.bizz.service.ICourseAttendanceService;

import javax.annotation.Resource;

/**
 * 课程签到记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Service
public class CourseAttendanceServiceImpl extends ServiceImpl<CourseAttendanceMapper, CourseAttendance>  implements ICourseAttendanceService {
    @Resource
    private CourseAttendanceMapper courseAttendanceMapper;

    /**
     * 查询课程签到记录
     * 
     * @param id 课程签到记录主键
     * @return 课程签到记录
     */
    @Override
    public CourseAttendance selectCourseAttendanceById(Long id) {
        return courseAttendanceMapper.selectCourseAttendanceById(id);
    }

    /**
     * 查询课程签到记录列表
     * 
     * @param courseAttendanceVo 课程签到记录
     * @return 课程签到记录
     */
    @Override
    public List<CourseAttendanceVo> selectCourseAttendanceList(CourseAttendanceVo courseAttendanceVo) {
        return courseAttendanceMapper.selectCourseAttendanceList(courseAttendanceVo);
    }

    @Override
    public List<CourseAttendExportVo> selectCourseAttendanceExportList(CourseAttendanceVo courseAttendanceVo) {
        List<CourseAttendExportVo> list = courseAttendanceMapper.selectCourseAttendanceExportList(courseAttendanceVo);
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }
        return list;
    }

    /**
     * 新增课程签到记录
     * 
     * @param courseAttendance 课程签到记录
     * @return 结果
     */
    @Override
    public int insertCourseAttendance(CourseAttendance courseAttendance) {
        courseAttendance.setCreateTime(DateUtils.getNowDate());
        return courseAttendanceMapper.insertCourseAttendance(courseAttendance);
    }

    /**
     * 修改课程签到记录
     * 
     * @param courseAttendance 课程签到记录
     * @return 结果
     */
    @Override
    public int updateCourseAttendance(CourseAttendance courseAttendance) {
        courseAttendance.setUpdateTime(DateUtils.getNowDate());
        return courseAttendanceMapper.updateCourseAttendance(courseAttendance);
    }

    /**
     * 批量删除课程签到记录
     * 
     * @param ids 需要删除的课程签到记录主键
     * @return 结果
     */
    @Override
    public int deleteCourseAttendanceByIds(Long[] ids) {
        return courseAttendanceMapper.deleteCourseAttendanceByIds(ids);
    }

    /**
     * 删除课程签到记录信息
     * 
     * @param id 课程签到记录主键
     * @return 结果
     */
    @Override
    public int deleteCourseAttendanceById(Long id) {
        return courseAttendanceMapper.deleteCourseAttendanceById(id);
    }


}
