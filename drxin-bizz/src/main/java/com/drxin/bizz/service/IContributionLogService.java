package com.drxin.bizz.service;

import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;
import com.drxin.bizz.domain.ContributionLog;

/**
 * 贡献值日志Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-29
 */
public interface IContributionLogService extends IService<ContributionLog> {

    /**
     * 查询贡献值日志
     * 
     * @param id 贡献值日志主键
     * @return 贡献值日志
     */
    public ContributionLog selectContributionLogById(Long id);

    /**
     * 查询贡献值日志列表
     * 
     * @param contributionLog 贡献值日志
     * @return 贡献值日志集合
     */
    public List<ContributionLog> selectContributionLogList(ContributionLog contributionLog);

    /**
     * 新增贡献值日志
     * 
     * @param contributionLog 贡献值日志
     * @return 结果
     */
    public int insertContributionLog(ContributionLog contributionLog);

    /**
     * 修改贡献值日志
     * 
     * @param contributionLog 贡献值日志
     * @return 结果
     */
    public int updateContributionLog(ContributionLog contributionLog);

    /**
     * 批量删除贡献值日志
     * 
     * @param ids 需要删除的贡献值日志主键集合
     * @return 结果
     */
    public int deleteContributionLogByIds(Long[] ids);

    /**
     * 删除贡献值日志信息
     * 
     * @param id 贡献值日志主键
     * @return 结果
     */
    public int deleteContributionLogById(Long id);

    void updateUserTotalContribution(Long userId, BigDecimal bigDecimal);

    ContributionLog getContributionLogByUserIdAndActionCode(Long userId, String actionCode);
}
