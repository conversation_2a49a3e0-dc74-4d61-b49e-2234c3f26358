package com.drxin.bizz.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.drxin.bizz.domain.ContributionEventLog;
import com.drxin.bizz.mapper.ContributionEventLogMapper;
import com.drxin.bizz.service.IContributionEventLogService;
import com.drxin.common.utils.DateUtils;
import com.drxin.framework.event.ContributionActionEvent;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 贡献值事件日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Slf4j
@Service
public class ContributionEventLogServiceImpl extends ServiceImpl<ContributionEventLogMapper, ContributionEventLog> implements IContributionEventLogService {

    @Resource
    private ContributionEventLogMapper contributionEventLogMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 查询贡献值事件日志
     *
     * @param id 贡献值事件日志主键
     * @return 贡献值事件日志
     */
    @Override
    public ContributionEventLog selectContributionEventLogById(Long id) {
        return contributionEventLogMapper.selectContributionEventLogById(id);
    }

    /**
     * 查询贡献值事件日志列表
     *
     * @param contributionEventLog 贡献值事件日志
     * @return 贡献值事件日志
     */
    @Override
    public List<ContributionEventLog> selectContributionEventLogList(ContributionEventLog contributionEventLog) {
        return contributionEventLogMapper.selectContributionEventLogList(contributionEventLog);
    }

    /**
     * 新增贡献值事件日志
     *
     * @param contributionEventLog 贡献值事件日志
     * @return 结果
     */
    @Override
    public int insertContributionEventLog(ContributionEventLog contributionEventLog) {
        contributionEventLog.setCreateTime(DateUtils.getNowDate());
        return contributionEventLogMapper.insertContributionEventLog(contributionEventLog);
    }

    /**
     * 修改贡献值事件日志
     *
     * @param contributionEventLog 贡献值事件日志
     * @return 结果
     */
    @Override
    public int updateContributionEventLog(ContributionEventLog contributionEventLog) {
        contributionEventLog.setUpdateTime(DateUtils.getNowDate());
        return contributionEventLogMapper.updateContributionEventLog(contributionEventLog);
    }

    /**
     * 批量删除贡献值事件日志
     *
     * @param ids 需要删除的贡献值事件日志主键
     * @return 结果
     */
    @Override
    public int deleteContributionEventLogByIds(Long[] ids) {
        return contributionEventLogMapper.deleteContributionEventLogByIds(ids);
    }

    /**
     * 删除贡献值事件日志信息
     *
     * @param id 贡献值事件日志主键
     * @return 结果
     */
    @Override
    public int deleteContributionEventLogById(Long id) {
        return contributionEventLogMapper.deleteContributionEventLogById(id);
    }

    /**
     * 记录事件日志（事件接收时调用）
     *
     * @param event 贡献值事件
     * @return 事件日志ID
     */
    @Override
    public Long recordEventLog(ContributionActionEvent event) {
        try {
            ContributionEventLog eventLog = new ContributionEventLog();
            eventLog.setUserId(event.getUserId());
            eventLog.setActionCode(event.getActionCode());
            eventLog.setEventSource(event.getSource().getClass().getSimpleName());
            
            // 序列化上下文数据
            if (event.getContext() != null && !event.getContext().isEmpty()) {
                eventLog.setContextData(objectMapper.writeValueAsString(event.getContext()));
            }

            eventLog.setProcessStatus(ContributionEventLog.STATUS_PENDING);
            eventLog.setCreateTime(new Date());
            
            // 保存事件日志
            contributionEventLogMapper.insertContributionEventLog(eventLog);
            
            log.info("事件日志记录成功: eventLogId={}, userId={}, actionCode={}", 
                eventLog.getId(), event.getUserId(), event.getActionCode());
            
            return eventLog.getId();
            
        } catch (JsonProcessingException e) {
            log.error("序列化事件上下文数据失败: userId={}, actionCode={}", 
                event.getUserId(), event.getActionCode(), e);
            return null;
        } catch (Exception e) {
            log.error("记录事件日志失败: userId={}, actionCode={}", 
                event.getUserId(), event.getActionCode(), e);
            return null;
        }
    }

    /**
     * 更新事件处理状态为成功
     *
     * @param eventLogId 事件日志ID
     * @param message 处理消息
     */
    @Override
    public void updateEventSuccess(Long eventLogId, String message) {
        if (eventLogId == null) {
            return;
        }
        
        try {
            UpdateWrapper<ContributionEventLog> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", eventLogId)
                        .set("process_status", ContributionEventLog.STATUS_SUCCESS)
                        .set("process_message", message)
                        .set("process_time", new Date())
                        .set("update_time", new Date());
            
            this.update(updateWrapper);
            
            log.debug("事件处理状态更新为成功: eventLogId={}", eventLogId);
            
        } catch (Exception e) {
            log.error("更新事件处理状态为成功失败: eventLogId={}", eventLogId, e);
        }
    }

    /**
     * 更新事件处理状态为失败
     *
     * @param eventLogId 事件日志ID
     * @param message 失败消息
     */
    @Override
    public void updateEventFailed(Long eventLogId, String message) {
        if (eventLogId == null) {
            return;
        }
        
        try {
            UpdateWrapper<ContributionEventLog> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", eventLogId)
                        .set("process_status", ContributionEventLog.STATUS_FAILED)
                        .set("process_message", message)
                        .set("process_time", new Date())
                        .set("update_time", new Date());
            
            this.update(updateWrapper);
            
            log.warn("事件处理状态更新为失败: eventLogId={}, message={}", eventLogId, message);
            
        } catch (Exception e) {
            log.error("更新事件处理状态为失败失败: eventLogId={}", eventLogId, e);
        }
    }



    /**
     * 查询待处理的事件日志
     *
     * @param limit 限制数量
     * @return 待处理的事件日志列表
     */
    @Override
    public List<ContributionEventLog> selectPendingEventLogs(int limit) {
        return contributionEventLogMapper.selectPendingEventLogs(limit);
    }

    /**
     * 查询处理失败的事件日志
     *
     * @param limit 限制数量
     * @return 处理失败的事件日志列表
     */
    @Override
    public List<ContributionEventLog> selectFailedEventLogs(int limit) {
        return contributionEventLogMapper.selectFailedEventLogs(limit);
    }
}
