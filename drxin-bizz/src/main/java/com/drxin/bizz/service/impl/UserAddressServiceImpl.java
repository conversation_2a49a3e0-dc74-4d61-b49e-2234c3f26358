package com.drxin.bizz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.drxin.bizz.domain.UserAddress;
import com.drxin.bizz.mapper.UserAddressMapper;
import com.drxin.bizz.service.IUserAddressService;
import com.drxin.common.utils.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户地址管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-02
 */
@Service
public class UserAddressServiceImpl extends ServiceImpl<UserAddressMapper, UserAddress>  implements IUserAddressService {
    @Resource
    private UserAddressMapper userAddressMapper;

    /**
     * 查询用户地址管理
     * 
     * @param id 用户地址管理主键
     * @return 用户地址管理
     */
    @Override
    public UserAddress selectUserAddressById(String id) {
        return userAddressMapper.selectUserAddressById(id);
    }

    /**
     * 查询用户地址管理列表
     * 
     * @param userAddress 用户地址管理
     * @return 用户地址管理
     */
    @Override
    public List<UserAddress> selectUserAddressList(UserAddress userAddress) {
        return userAddressMapper.selectUserAddressList(userAddress);
    }

    /**
     * 新增用户地址管理
     * 
     * @param userAddress 用户地址管理
     * @return 结果
     */
    @Override
    public int insertUserAddress(UserAddress userAddress) {
        userAddress.setCreateTime(DateUtils.getNowDate());
        return userAddressMapper.insertUserAddress(userAddress);
    }

    /**
     * 修改用户地址管理
     * 
     * @param userAddress 用户地址管理
     * @return 结果
     */
    @Override
    public int updateUserAddress(UserAddress userAddress) {
        userAddress.setUpdateTime(DateUtils.getNowDate());
        return userAddressMapper.updateUserAddress(userAddress);
    }

    /**
     * 批量删除用户地址管理
     * 
     * @param ids 需要删除的用户地址管理主键
     * @return 结果
     */
    @Override
    public int deleteUserAddressByIds(String[] ids) {
        return userAddressMapper.deleteUserAddressByIds(ids);
    }

    /**
     * 删除用户地址管理信息
     * 
     * @param id 用户地址管理主键
     * @return 结果
     */
    @Override
    public int deleteUserAddressById(String id) {
        return userAddressMapper.deleteUserAddressById(id);
    }
}
