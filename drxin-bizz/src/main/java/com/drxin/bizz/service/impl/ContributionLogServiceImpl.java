package com.drxin.bizz.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.math.BigDecimal;
import java.util.List;
import com.drxin.common.core.domain.entity.SysUser;
import com.drxin.common.utils.DateUtils;
import com.drxin.common.utils.StringUtils;
import com.drxin.system.mapper.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.drxin.bizz.mapper.ContributionLogMapper;
import com.drxin.bizz.domain.ContributionLog;
import com.drxin.bizz.service.IContributionLogService;

import javax.annotation.Resource;

/**
 * 贡献值日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
@Slf4j
@Service
public class ContributionLogServiceImpl extends ServiceImpl<ContributionLogMapper, ContributionLog>  implements IContributionLogService {
    @Resource
    private ContributionLogMapper contributionLogMapper;

    @Resource
    private SysUserMapper userMapper;

    /**
     * 查询贡献值日志
     * 
     * @param id 贡献值日志主键
     * @return 贡献值日志
     */
    @Override
    public ContributionLog selectContributionLogById(Long id) {
        return contributionLogMapper.selectContributionLogById(id);
    }

    /**
     * 查询贡献值日志列表
     * 
     * @param contributionLog 贡献值日志
     * @return 贡献值日志
     */
    @Override
    public List<ContributionLog> selectContributionLogList(ContributionLog contributionLog) {
        return contributionLogMapper.selectContributionLogList(contributionLog);
    }

    /**
     * 新增贡献值日志
     * 
     * @param contributionLog 贡献值日志
     * @return 结果
     */
    @Override
    public int insertContributionLog(ContributionLog contributionLog) {
        contributionLog.setCreateTime(DateUtils.getNowDate());
        return contributionLogMapper.insertContributionLog(contributionLog);
    }

    /**
     * 修改贡献值日志
     * 
     * @param contributionLog 贡献值日志
     * @return 结果
     */
    @Override
    public int updateContributionLog(ContributionLog contributionLog) {
        contributionLog.setUpdateTime(DateUtils.getNowDate());
        return contributionLogMapper.updateContributionLog(contributionLog);
    }

    /**
     * 批量删除贡献值日志
     * 
     * @param ids 需要删除的贡献值日志主键
     * @return 结果
     */
    @Override
    public int deleteContributionLogByIds(Long[] ids) {
        return contributionLogMapper.deleteContributionLogByIds(ids);
    }

    /**
     * 删除贡献值日志信息
     * 
     * @param id 贡献值日志主键
     * @return 结果
     */
    @Override
    public int deleteContributionLogById(Long id) {
        return contributionLogMapper.deleteContributionLogById(id);
    }

    @Override
    public void updateUserTotalContribution(Long userId, BigDecimal contributionValue) {
        try {
            // 使用原子性SQL更新，防止并发问题
            UpdateWrapper<SysUser> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("user_id", userId)
                        .setSql("contribution = IFNULL(CAST(contribution AS DECIMAL(10,2)), 0) + " + contributionValue)
                        .set("update_time", DateUtils.getNowDate());

            int updateResult = userMapper.update(null, updateWrapper);
            if (updateResult > 0) {
                log.info("用户总贡献值更新成功: userId={}, 增加贡献值={}", userId, contributionValue);
            } else {
                log.warn("用户总贡献值更新失败，可能用户不存在: userId={}, contributionValue={}", userId, contributionValue);
            }

        } catch (Exception e) {
            log.error("更新用户总贡献值异常: userId={}, contributionValue={}", userId, contributionValue, e);
        }
    }

    @Override
    public ContributionLog getContributionLogByUserIdAndActionCode(Long userId, String actionCode) {
        if (userId == null || StringUtils.isEmpty(actionCode)) {
            return null;
        }
        ContributionLog query = new ContributionLog();
        query.setUserId(userId);
        query.setActionCode(actionCode);
        List<ContributionLog> logs = contributionLogMapper.selectContributionLogList(query);
        if (logs != null && !logs.isEmpty()) {
            return logs.get(0);
        }
        return null;

    }
}
