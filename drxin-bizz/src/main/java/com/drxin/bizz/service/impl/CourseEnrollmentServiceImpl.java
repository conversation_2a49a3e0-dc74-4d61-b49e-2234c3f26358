package com.drxin.bizz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Collections;
import java.util.List;

import com.drxin.bizz.vo.CourseEnrollmentExportVo;
import com.drxin.bizz.vo.CourseEnrollmentVo;
import com.drxin.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.drxin.bizz.mapper.CourseEnrollmentMapper;
import com.drxin.bizz.domain.CourseEnrollment;
import com.drxin.bizz.service.ICourseEnrollmentService;

/**
 * 课程报名记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Service
public class CourseEnrollmentServiceImpl extends ServiceImpl<CourseEnrollmentMapper, CourseEnrollment>  implements ICourseEnrollmentService {
    @Autowired
    private CourseEnrollmentMapper courseEnrollmentMapper;

    /**
     * 查询课程报名记录
     * 
     * @param id 课程报名记录主键
     * @return 课程报名记录
     */
    @Override
    public CourseEnrollment selectCourseEnrollmentById(Long id) {
        return courseEnrollmentMapper.selectCourseEnrollmentById(id);
    }

    /**
     * 查询课程报名记录列表
     * 
     * @param courseEnrollment 课程报名记录
     * @return 课程报名记录
     */
    @Override
    public List<CourseEnrollmentVo> selectCourseEnrollmentList(CourseEnrollmentVo courseEnrollment) {
        return courseEnrollmentMapper.selectCourseEnrollmentList(courseEnrollment);
    }

    @Override
    public List<CourseEnrollmentExportVo> selectCourseEnrollmentExportList(CourseEnrollmentVo courseEnrollment) {
        return baseMapper.selectCourseEnrollmentExportList(courseEnrollment);
    }

    /**
     * 新增课程报名记录
     * 
     * @param courseEnrollment 课程报名记录
     * @return 结果
     */
    @Override
    public int insertCourseEnrollment(CourseEnrollment courseEnrollment) {
        courseEnrollment.setCreateTime(DateUtils.getNowDate());
        return courseEnrollmentMapper.insertCourseEnrollment(courseEnrollment);
    }

    /**
     * 修改课程报名记录
     * 
     * @param courseEnrollment 课程报名记录
     * @return 结果
     */
    @Override
    public int updateCourseEnrollment(CourseEnrollment courseEnrollment) {
        courseEnrollment.setUpdateTime(DateUtils.getNowDate());
        return courseEnrollmentMapper.updateCourseEnrollment(courseEnrollment);
    }

    /**
     * 批量删除课程报名记录
     * 
     * @param ids 需要删除的课程报名记录主键
     * @return 结果
     */
    @Override
    public int deleteCourseEnrollmentByIds(Long[] ids) {
        return courseEnrollmentMapper.deleteCourseEnrollmentByIds(ids);
    }

    /**
     * 删除课程报名记录信息
     * 
     * @param id 课程报名记录主键
     * @return 结果
     */
    @Override
    public int deleteCourseEnrollmentById(Long id) {
        return courseEnrollmentMapper.deleteCourseEnrollmentById(id);
    }

    @Override
    public Long countEnrollmentsByCourseId(Long id) {
        QueryWrapper<CourseEnrollment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("course_id", id);
        queryWrapper.eq("enroll_status", '1');
        return courseEnrollmentMapper.selectCount(queryWrapper);
    }
}
