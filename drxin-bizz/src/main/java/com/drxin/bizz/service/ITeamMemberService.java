package com.drxin.bizz.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.drxin.bizz.domain.TeamMember;

/**
 * 团队成员Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface ITeamMemberService extends IService<TeamMember> {

    /**
     * 查询团队成员
     * 
     * @param memberId 团队成员主键
     * @return 团队成员
     */
    public TeamMember selectTeamMemberById(String memberId);

    /**
     * 查询团队成员列表
     * 
     * @param teamMember 团队成员
     * @return 团队成员集合
     */
    public List<TeamMember> selectTeamMemberList(TeamMember teamMember);

    /**
     * 新增团队成员
     * 
     * @param teamMember 团队成员
     * @return 结果
     */
    public int insertTeamMember(TeamMember teamMember);

    /**
     * 修改团队成员
     * 
     * @param teamMember 团队成员
     * @return 结果
     */
    public int updateTeamMember(TeamMember teamMember);

    /**
     * 批量删除团队成员
     * 
     * @param memberIds 需要删除的团队成员主键集合
     * @return 结果
     */
    public int deleteTeamMemberByIds(String[] memberIds);

    /**
     * 删除团队成员信息
     * 
     * @param memberId 团队成员主键
     * @return 结果
     */
    public int deleteTeamMemberById(String memberId);
}
