package com.drxin.bizz.service.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.drxin.bizz.domain.UserInfoCollection;
import com.drxin.bizz.mapper.UserInfoCollectionMapper;
import com.drxin.bizz.service.IUserInfoCollectionService;
import com.drxin.bizz.vo.UserInfoCollectionVo;
import com.drxin.common.exception.ServiceException;
import com.drxin.common.utils.DateUtils;
import com.drxin.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 用户信息收集Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-18
 */
@Service
public class UserInfoCollectionServiceImpl implements IUserInfoCollectionService {
    @Autowired
    private UserInfoCollectionMapper userInfoCollectionMapper;

    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}([0-9Xx])$)");
    /**
     * 身份证校验码计算方法：
     * 1. 将身份证前17位分别乘以对应的权重因子(7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2)
     * 2. 将乘积之和除以11得到余数
     * 3. 根据余数对应的校验码(1,0,X,9,8,7,6,5,4,3,2)判断最后一位是否正确
     */
    private static boolean validateIdCardCheckCode(String idCard) {
        if (idCard.length() != 18) return false;

        int[] weights = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
        char[] checkCodes = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};

        int sum = 0;
        for (int i = 0; i < 17; i++) {
            sum += (idCard.charAt(i) - '0') * weights[i];
        }

        int remainder = sum % 11;
        return Character.toUpperCase(idCard.charAt(17)) == checkCodes[remainder];
    }
    /**
     * 查询用户信息收集
     *
     * @param id 用户信息收集主键
     * @return 用户信息收集
     */
    @Override
    public UserInfoCollection selectUserInfoCollectionById(Long id) {
        return userInfoCollectionMapper.selectUserInfoCollectionById(id);
    }

    /**
     * 查询用户信息收集列表
     *
     * @param userInfoCollection 用户信息收集
     * @return 用户信息收集
     */
    @Override
    public List<UserInfoCollection> selectUserInfoCollectionList(UserInfoCollectionVo userInfoCollection) {
        return userInfoCollectionMapper.selectUserInfoCollectionList(userInfoCollection);
    }

    /**
     * 新增用户信息收集
     *
     * @param userInfoCollection 用户信息收集
     * @return 结果
     */
    @Override
    public int insertUserInfoCollection(UserInfoCollection userInfoCollection) {
        String name = userInfoCollection.getName();
        String sex = userInfoCollection.getSex();
        String phone = userInfoCollection.getPhone();
        String idCard = userInfoCollection.getIdCard();
        String address = userInfoCollection.getAddress();
        String companions = userInfoCollection.getCompanions();

        // 检查必填字段
        List<String> missingFields = new ArrayList<>();
        if (StringUtils.isBlank(name)) missingFields.add("姓名");
        if (StringUtils.isBlank(sex)) missingFields.add("性别");
        if (StringUtils.isBlank(phone)) missingFields.add("手机号");
        if (StringUtils.isBlank(idCard)) missingFields.add("身份证号");
        if (StringUtils.isBlank(address)) missingFields.add("地址");

        if (!missingFields.isEmpty()) {
            throw new ServiceException("缺少必填字段: " + String.join("，", missingFields));
        }

        // 校验手机号格式
        if (!PHONE_PATTERN.matcher(phone).matches()) {
            throw new ServiceException("手机号格式不正确");
        }

        // 校验身份证格式
        if (!ID_CARD_PATTERN.matcher(idCard).matches()) {
            throw new ServiceException("身份证号格式不正确，请检查后重新提交");
        }

        // 校验身份证校验码
        if (!validateIdCardCheckCode(idCard)) {
            throw new ServiceException("身份证号校验码不正确，请检查后重新提交");
        }

        // 判断身份证是否重复
        LambdaQueryWrapper<UserInfoCollection> query = new LambdaQueryWrapper<>();
        query.eq(UserInfoCollection::getIdCard, idCard);
        if (userInfoCollectionMapper.selectCount(query) > 0) {
            throw new ServiceException("该身份证号已存在，请勿重复提交");
        }

        // 构建实体并插入
        UserInfoCollection userInfo = new UserInfoCollection();
        userInfo.setName(name);
        userInfo.setSex(sex);
        userInfo.setPhone(phone);
        userInfo.setIdCard(idCard);
        userInfo.setAddress(address);
        userInfo.setCompanions(companions);
        userInfo.setCreateTime(DateUtils.toDate(LocalDateTime.now()));
        userInfoCollection.setCreateTime(DateUtils.getNowDate());
        return userInfoCollectionMapper.insertUserInfoCollection(userInfoCollection);
    }

    /**
     * 修改用户信息收集
     *
     * @param userInfoCollection 用户信息收集
     * @return 结果
     */
    @Override
    public int updateUserInfoCollection(UserInfoCollection userInfoCollection) {
        return userInfoCollectionMapper.updateUserInfoCollection(userInfoCollection);
    }

    /**
     * 批量删除用户信息收集
     *
     * @param ids 需要删除的用户信息收集主键
     * @return 结果
     */
    @Override
    public int deleteUserInfoCollectionByIds(Long[] ids) {
        return userInfoCollectionMapper.deleteUserInfoCollectionByIds(ids);
    }

    /**
     * 删除用户信息收集信息
     *
     * @param id 用户信息收集主键
     * @return 结果
     */
    @Override
    public int deleteUserInfoCollectionById(Long id) {
        return userInfoCollectionMapper.deleteUserInfoCollectionById(id);
    }
}
