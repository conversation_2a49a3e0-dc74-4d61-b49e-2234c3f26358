package com.drxin.bizz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;
import com.drxin.bizz.domain.RescueCase;
import com.drxin.bizz.vo.RescueCaseVo;

/**
 * 急救案例Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface IRescueCaseService extends IService<RescueCase> {

    /**
     * 查询急救案例
     * 
     * @param id 急救案例主键
     * @return 急救案例
     */
    public RescueCase selectRescueCaseById(Long id);

    /**
     * 查询急救案例列表
     * 
     * @param rescueCase 急救案例
     * @return 急救案例集合
     */
    public List<RescueCaseVo> selectRescueCaseList(RescueCase rescueCase);

    /**
     * 新增急救案例
     * 
     * @param rescueCase 急救案例
     * @return 结果
     */
    public int insertRescueCase(RescueCase rescueCase);

    /**
     * 修改急救案例
     * 
     * @param rescueCase 急救案例
     * @return 结果
     */
    public int updateRescueCase(RescueCase rescueCase);

    /**
     * 批量删除急救案例
     * 
     * @param ids 需要删除的急救案例主键集合
     * @return 结果
     */
    public int deleteRescueCaseByIds(Long[] ids);

    /**
     * 删除急救案例信息
     *
     * @param id 急救案例主键
     * @return 结果
     */
    public int deleteRescueCaseById(Long id);

    /**
     * 批量审批急救案例
     *
     * @param ids 急救案例ID数组
     * @param status 审批状态
     * @return 结果
     */
    public int batchAuditRescueCase(Long[] ids, String status);
}
