package com.drxin.bizz.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.drxin.bizz.domain.RescueCase;
import com.drxin.bizz.domain.RescueCaseAttachment;
import com.drxin.bizz.domain.RescueCaseRescuer;
import com.drxin.bizz.mapper.RescueCaseAttachmentMapper;
import com.drxin.bizz.mapper.RescueCaseMapper;
import com.drxin.bizz.mapper.RescueCaseRescuerMapper;
import com.drxin.bizz.service.IRescueCaseService;
import com.drxin.bizz.vo.RescueCaseVo;
import com.drxin.common.utils.DateUtils;
import com.drxin.common.utils.SecurityUtils;
import com.drxin.framework.event.ContributionActionEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 急救案例Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@Service
public class RescueCaseServiceImpl extends ServiceImpl<RescueCaseMapper, RescueCase>  implements IRescueCaseService {
    @Resource
    private RescueCaseMapper rescueCaseMapper;

    @Resource
    private RescueCaseRescuerMapper rescueCaseRescuerMapper;

    @Resource
    private RescueCaseAttachmentMapper rescueCaseAttachmentMapper;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    /**
     * 查询急救案例
     * 
     * @param id 急救案例主键
     * @return 急救案例
     */
    @Override
    public RescueCase selectRescueCaseById(Long id) {
        return rescueCaseMapper.selectRescueCaseById(id);
    }

    /**
     * 查询急救案例列表
     * 
     * @param rescueCase 急救案例
     * @return 急救案例
     */
    @Override
    public List<RescueCaseVo> selectRescueCaseList(RescueCase rescueCase) {
        List<RescueCaseVo> rescueCases = rescueCaseMapper.selectRescueCaseList(rescueCase);
        // 循环查询急救案例急救员和附件
        for (RescueCaseVo caseItem : rescueCases) {
            // 查询急救员
            RescueCaseRescuer rescueCaseRescuer = new RescueCaseRescuer();
            rescueCaseRescuer.setCaseId(caseItem.getId());
            List<RescueCaseRescuer> rescuers = rescueCaseRescuerMapper.selectRescueCaseRescuerList(rescueCaseRescuer);
            caseItem.setRescuers(rescuers);

            // 查询附件
            List<RescueCaseAttachment> attachments = rescueCaseAttachmentMapper.selectAttachmentsByCaseId(caseItem.getId());
            caseItem.setAttachments(attachments);
        }
        return rescueCases;
    }

    /**
     * 新增急救案例
     * 
     * @param rescueCase 急救案例
     * @return 结果
     */
    @Override
    public int insertRescueCase(RescueCase rescueCase) {
        rescueCase.setCreateTime(DateUtils.getNowDate());
        int result = rescueCaseMapper.insertRescueCase(rescueCase);

        // 保存附件信息
        if (result > 0 && rescueCase.getAttachments() != null && !rescueCase.getAttachments().isEmpty()) {
            for (RescueCaseAttachment attachment : rescueCase.getAttachments()) {
                attachment.setCaseId(rescueCase.getId());
                attachment.setCreateTime(DateUtils.getNowDate());
                rescueCaseAttachmentMapper.insertRescueCaseAttachment(attachment);
            }
        }

        return result;
    }

    /**
     * 修改急救案例
     * 
     * @param rescueCase 急救案例
     * @return 结果
     */
    @Override
    public int updateRescueCase(RescueCase rescueCase) {
        rescueCase.setUpdateTime(DateUtils.getNowDate());
        int result = rescueCaseMapper.updateRescueCase(rescueCase);

        // 更新附件信息
        if (result > 0 && rescueCase.getAttachments() != null) {
            // 先删除原有的附件记录
            rescueCaseAttachmentMapper.deleteAttachmentsByCaseId(rescueCase.getId());

            // 重新插入附件记录
            if (!rescueCase.getAttachments().isEmpty()) {
                for (RescueCaseAttachment attachment : rescueCase.getAttachments()) {
                    attachment.setCaseId(rescueCase.getId());
                    attachment.setCreateTime(DateUtils.getNowDate());
                    rescueCaseAttachmentMapper.insertRescueCaseAttachment(attachment);
                }
            }
        }
        return result;
    }

    /**
     * 批量删除急救案例
     * 
     * @param ids 需要删除的急救案例主键
     * @return 结果
     */
    @Override
    public int deleteRescueCaseByIds(Long[] ids) {
        // 先删除相关的附件记录
        rescueCaseAttachmentMapper.delete(new LambdaUpdateWrapper<RescueCaseAttachment>()
                .in(RescueCaseAttachment::getCaseId, Arrays.asList(ids)));
        return rescueCaseMapper.deleteRescueCaseByIds(ids);
    }

    /**
     * 删除急救案例信息
     *
     * @param id 急救案例主键
     * @return 结果
     */
    @Override
    public int deleteRescueCaseById(Long id) {
        // 先删除相关的附件记录
        rescueCaseAttachmentMapper.deleteAttachmentsByCaseId(id);
        return rescueCaseMapper.deleteRescueCaseById(id);
    }

    /**
     * 批量审批急救案例
     *
     * @param ids 急救案例ID数组
     * @param status 审批状态
     * @return 结果
     */
    @Override
    public int batchAuditRescueCase(Long[] ids, String status) {
        int result = 0;
        List<Long> approvedCaseIds = new ArrayList<>();

        for (Long id : ids) {
            RescueCase rescueCase = new RescueCase();
            rescueCase.setId(id);
            rescueCase.setRescueStatus(status);
            rescueCase.setUpdateTime(DateUtils.getNowDate());

            int updateResult = rescueCaseMapper.updateRescueCase(rescueCase);
            if (updateResult > 0) {
                result += updateResult;
                // 如果是审核通过，记录案例ID用于后续发布贡献值事件
                if ("3".equals(status)) { // 假设3表示审核通过
                    approvedCaseIds.add(id);
                }
            }
        }

        // 如果有审核通过的案例，发布贡献值事件
        if (!approvedCaseIds.isEmpty()) {
            publishRescueCaseApprovedEvent(approvedCaseIds);
        }

        return result;
    }

    /**
     * 发布急救案例通过贡献值事件
     *
     * @param approvedCaseIds 审核通过的案例ID列表
     */
    private void publishRescueCaseApprovedEvent(List<Long> approvedCaseIds) {
        try {
            log.info("开始发布急救案例通过贡献值事件: 案例数量={}", approvedCaseIds.size());

            // 收集所有案例的急救人员
            List<Map<String, Object>> allRescueCaseUsers = new ArrayList<>();
            // 收集所有案例的远程指导员
            List<Map<String, Object>> allRemoteGuideUsers = new ArrayList<>();

            for (Long caseId : approvedCaseIds) {
                // 查询该案例的急救人员
                RescueCaseRescuer queryParam = new RescueCaseRescuer();
                queryParam.setCaseId(caseId);
                List<RescueCaseRescuer> rescuers = rescueCaseRescuerMapper.selectRescueCaseRescuerList(queryParam);

                for (RescueCaseRescuer rescuer : rescuers) {
                    Map<String, Object> rescueCaseUser = new HashMap<>();
                    rescueCaseUser.put("caseId", caseId);
                    rescueCaseUser.put("userId", rescuer.getRescuerId()); // 使用rescuerId作为userId
                    rescueCaseUser.put("userName", rescuer.getRescuerName());
                    rescueCaseUser.put("roleType", "急救人员"); // 设置默认角色类型
                    allRescueCaseUsers.add(rescueCaseUser);
                }

                // 查询该案例的远程指导员信息
                RescueCase rescueCase = rescueCaseMapper.selectRescueCaseById(caseId);
                if (rescueCase != null && rescueCase.getRemoteGuideUserId() != null) {
                    Map<String, Object> remoteGuideUser = new HashMap<>();
                    remoteGuideUser.put("caseId", caseId);
                    remoteGuideUser.put("userId", rescueCase.getRemoteGuideUserId());
                    remoteGuideUser.put("userName", rescueCase.getRemoteGuideRealName());
                    remoteGuideUser.put("roleType", "远程指导员");
                    allRemoteGuideUsers.add(remoteGuideUser);
                }
            }

            // 获取当前操作用户ID作为事件发布者
            Long currentUserId = SecurityUtils.getUserId();

            // 发布急救人员贡献值事件
            if (!allRescueCaseUsers.isEmpty()) {
                Map<String, Object> rescueContext = new HashMap<>();
                rescueContext.put("rescueCaseUsers", allRescueCaseUsers);

                ContributionActionEvent rescueEvent = new ContributionActionEvent(
                    this, currentUserId, "RESCUE_CASE", rescueContext);
                eventPublisher.publishEvent(rescueEvent);

                log.info("急救案例通过贡献值事件发布完成: 案例数量={}, 急救人员数量={}",
                    approvedCaseIds.size(), allRescueCaseUsers.size());
            }

            // 发布远程指导员贡献值事件
            if (!allRemoteGuideUsers.isEmpty()) {
                Map<String, Object> remoteGuideContext = new HashMap<>();
                remoteGuideContext.put("remoteGuideUsers", allRemoteGuideUsers);

                ContributionActionEvent remoteGuideEvent = new ContributionActionEvent(
                    this, currentUserId, "REMOTE_GUIDE_AID", remoteGuideContext);
                eventPublisher.publishEvent(remoteGuideEvent);

                log.info("远程指导员贡献值事件发布完成: 案例数量={}, 远程指导员数量={}",
                    approvedCaseIds.size(), allRemoteGuideUsers.size());
            }

            if (allRescueCaseUsers.isEmpty() && allRemoteGuideUsers.isEmpty()) {
                log.warn("未找到急救人员和远程指导员信息，跳过事件发布");
            }

        } catch (Exception e) {
            // 记录日志但不影响主流程
            log.error("发布急救案例通过贡献值事件失败: approvedCaseIds={}", approvedCaseIds, e);
        }
    }
}
