package com.drxin.bizz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
import com.drxin.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.drxin.bizz.mapper.RescueCaseRescuerMapper;
import com.drxin.bizz.domain.RescueCaseRescuer;
import com.drxin.bizz.service.IRescueCaseRescuerService;

import javax.annotation.Resource;

/**
 * 急救案例急救成员Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Service
public class RescueCaseRescuerServiceImpl extends ServiceImpl<RescueCaseRescuerMapper, RescueCaseRescuer>  implements IRescueCaseRescuerService {
    @Resource
    private RescueCaseRescuerMapper rescueCaseRescuerMapper;

    /**
     * 查询急救案例急救成员
     * 
     * @param id 急救案例急救成员主键
     * @return 急救案例急救成员
     */
    @Override
    public RescueCaseRescuer selectRescueCaseRescuerById(Long id) {
        return rescueCaseRescuerMapper.selectRescueCaseRescuerById(id);
    }

    /**
     * 查询急救案例急救成员列表
     * 
     * @param rescueCaseRescuer 急救案例急救成员
     * @return 急救案例急救成员
     */
    @Override
    public List<RescueCaseRescuer> selectRescueCaseRescuerList(RescueCaseRescuer rescueCaseRescuer) {
        return rescueCaseRescuerMapper.selectRescueCaseRescuerList(rescueCaseRescuer);
    }

    /**
     * 新增急救案例急救成员
     * 
     * @param rescueCaseRescuer 急救案例急救成员
     * @return 结果
     */
    @Override
    public int insertRescueCaseRescuer(RescueCaseRescuer rescueCaseRescuer) {
        rescueCaseRescuer.setCreateTime(DateUtils.getNowDate());
        return rescueCaseRescuerMapper.insertRescueCaseRescuer(rescueCaseRescuer);
    }

    /**
     * 修改急救案例急救成员
     * 
     * @param rescueCaseRescuer 急救案例急救成员
     * @return 结果
     */
    @Override
    public int updateRescueCaseRescuer(RescueCaseRescuer rescueCaseRescuer) {
        rescueCaseRescuer.setUpdateTime(DateUtils.getNowDate());
        return rescueCaseRescuerMapper.updateRescueCaseRescuer(rescueCaseRescuer);
    }

    /**
     * 批量删除急救案例急救成员
     * 
     * @param ids 需要删除的急救案例急救成员主键
     * @return 结果
     */
    @Override
    public int deleteRescueCaseRescuerByIds(Long[] ids) {
        return rescueCaseRescuerMapper.deleteRescueCaseRescuerByIds(ids);
    }

    /**
     * 删除急救案例急救成员信息
     * 
     * @param id 急救案例急救成员主键
     * @return 结果
     */
    @Override
    public int deleteRescueCaseRescuerById(Long id) {
        return rescueCaseRescuerMapper.deleteRescueCaseRescuerById(id);
    }
}
