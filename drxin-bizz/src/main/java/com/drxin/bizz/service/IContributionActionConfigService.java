package com.drxin.bizz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;
import com.drxin.bizz.domain.ContributionActionConfig;

import java.util.List;

/**
 * 贡献值配置Service接口
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
public interface IContributionActionConfigService extends IService<ContributionActionConfig> {

    /**
     * 查询贡献值配置
     *
     * @param id 贡献值配置主键
     * @return 贡献值配置
     */
    public ContributionActionConfig selectContributionActionConfigById(Long id);

    /**
     * 查询贡献值配置列表
     *
     * @param contributionActionConfig 贡献值配置
     * @return 贡献值配置集合
     */
    public List<ContributionActionConfig> selectContributionActionConfigList(ContributionActionConfig contributionActionConfig);

    /**
     * 新增贡献值配置
     *
     * @param contributionActionConfig 贡献值配置
     * @return 结果
     */
    public int insertContributionActionConfig(ContributionActionConfig contributionActionConfig);

    /**
     * 修改贡献值配置
     *
     * @param contributionActionConfig 贡献值配置
     * @return 结果
     */
    public int updateContributionActionConfig(ContributionActionConfig contributionActionConfig);

    /**
     * 批量删除贡献值配置
     *
     * @param ids 需要删除的贡献值配置主键集合
     * @return 结果
     */
    public int deleteContributionActionConfigByIds(Long[] ids);

    /**
     * 删除贡献值配置信息
     *
     * @param id 贡献值配置主键
     * @return 结果
     */
    public int deleteContributionActionConfigById(Long id);

    /**
     * 根据行为编码获取启用的配置
     *
     * @param actionCode 行为编码
     * @return 贡献值行为配置，如果未找到或未启用则返回null
     */
    public ContributionActionConfig getEnabledConfigByCode(String actionCode);
}
