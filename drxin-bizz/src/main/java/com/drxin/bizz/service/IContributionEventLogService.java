package com.drxin.bizz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.drxin.bizz.domain.ContributionEventLog;
import com.drxin.framework.event.ContributionActionEvent;

import java.util.List;

/**
 * 贡献值事件日志Service接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface IContributionEventLogService extends IService<ContributionEventLog> {

    /**
     * 查询贡献值事件日志
     *
     * @param id 贡献值事件日志主键
     * @return 贡献值事件日志
     */
    ContributionEventLog selectContributionEventLogById(Long id);

    /**
     * 查询贡献值事件日志列表
     *
     * @param contributionEventLog 贡献值事件日志
     * @return 贡献值事件日志集合
     */
    List<ContributionEventLog> selectContributionEventLogList(ContributionEventLog contributionEventLog);

    /**
     * 新增贡献值事件日志
     *
     * @param contributionEventLog 贡献值事件日志
     * @return 结果
     */
    int insertContributionEventLog(ContributionEventLog contributionEventLog);

    /**
     * 修改贡献值事件日志
     *
     * @param contributionEventLog 贡献值事件日志
     * @return 结果
     */
    int updateContributionEventLog(ContributionEventLog contributionEventLog);

    /**
     * 批量删除贡献值事件日志
     *
     * @param ids 需要删除的贡献值事件日志主键集合
     * @return 结果
     */
    int deleteContributionEventLogByIds(Long[] ids);

    /**
     * 删除贡献值事件日志信息
     *
     * @param id 贡献值事件日志主键
     * @return 结果
     */
    int deleteContributionEventLogById(Long id);

    /**
     * 记录事件日志（事件接收时调用）
     *
     * @param event 贡献值事件
     * @return 事件日志ID
     */
    Long recordEventLog(ContributionActionEvent event);

    /**
     * 更新事件处理状态为成功
     *
     * @param eventLogId 事件日志ID
     * @param message 处理消息
     */
    void updateEventSuccess(Long eventLogId, String message);

    /**
     * 更新事件处理状态为失败
     *
     * @param eventLogId 事件日志ID
     * @param message 失败消息
     */
    void updateEventFailed(Long eventLogId, String message);

    /**
     * 查询待处理的事件日志
     *
     * @param limit 限制数量
     * @return 待处理的事件日志列表
     */
    List<ContributionEventLog> selectPendingEventLogs(int limit);

    /**
     * 查询处理失败的事件日志
     *
     * @param limit 限制数量
     * @return 处理失败的事件日志列表
     */
    List<ContributionEventLog> selectFailedEventLogs(int limit);
}
