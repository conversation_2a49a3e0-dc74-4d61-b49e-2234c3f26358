package com.drxin.bizz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
import com.drxin.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.drxin.bizz.mapper.ContributionActionConfigMapper;
import com.drxin.bizz.domain.ContributionActionConfig;
import com.drxin.bizz.service.IContributionActionConfigService;

import javax.annotation.Resource;

/**
 * 贡献值配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-29
 */
@Service
public class ContributionActionConfigServiceImpl extends ServiceImpl<ContributionActionConfigMapper, ContributionActionConfig>  implements IContributionActionConfigService {
    @Resource
    private ContributionActionConfigMapper contributionActionConfigMapper;

    /**
     * 查询贡献值配置
     * 
     * @param id 贡献值配置主键
     * @return 贡献值配置
     */
    @Override
    public ContributionActionConfig selectContributionActionConfigById(Long id) {
        return contributionActionConfigMapper.selectContributionActionConfigById(id);
    }

    /**
     * 查询贡献值配置列表
     * 
     * @param contributionActionConfig 贡献值配置
     * @return 贡献值配置
     */
    @Override
    public List<ContributionActionConfig> selectContributionActionConfigList(ContributionActionConfig contributionActionConfig) {
        return contributionActionConfigMapper.selectContributionActionConfigList(contributionActionConfig);
    }

    /**
     * 新增贡献值配置
     * 
     * @param contributionActionConfig 贡献值配置
     * @return 结果
     */
    @Override
    public int insertContributionActionConfig(ContributionActionConfig contributionActionConfig) {
        contributionActionConfig.setCreateTime(DateUtils.getNowDate());
        return contributionActionConfigMapper.insertContributionActionConfig(contributionActionConfig);
    }

    /**
     * 修改贡献值配置
     * 
     * @param contributionActionConfig 贡献值配置
     * @return 结果
     */
    @Override
    public int updateContributionActionConfig(ContributionActionConfig contributionActionConfig) {
        contributionActionConfig.setUpdateTime(DateUtils.getNowDate());
        return contributionActionConfigMapper.updateContributionActionConfig(contributionActionConfig);
    }

    /**
     * 批量删除贡献值配置
     * 
     * @param ids 需要删除的贡献值配置主键
     * @return 结果
     */
    @Override
    public int deleteContributionActionConfigByIds(Long[] ids) {
        return contributionActionConfigMapper.deleteContributionActionConfigByIds(ids);
    }

    /**
     * 删除贡献值配置信息
     * 
     * @param id 贡献值配置主键
     * @return 结果
     */
    @Override
    public int deleteContributionActionConfigById(Long id) {
        return contributionActionConfigMapper.deleteContributionActionConfigById(id);
    }

    /**
     * 根据行为编码获取启用的配置
     *
     * @param actionCode 行为编码
     * @return 贡献值行为配置，如果未找到或未启用则返回null
     */
    @Override
    public ContributionActionConfig getEnabledConfigByCode(String actionCode) {
        if (actionCode == null || actionCode.trim().isEmpty()) {
            return null;
        }

        QueryWrapper<ContributionActionConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", actionCode.trim())
                   .eq("enabled", "1");

        return this.getOne(queryWrapper);
    }
}
