package com.drxin.bizz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;
import com.drxin.bizz.domain.RescueCaseRescuer;

/**
 * 急救案例急救成员Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface IRescueCaseRescuerService extends IService<RescueCaseRescuer> {

    /**
     * 查询急救案例急救成员
     * 
     * @param id 急救案例急救成员主键
     * @return 急救案例急救成员
     */
    public RescueCaseRescuer selectRescueCaseRescuerById(Long id);

    /**
     * 查询急救案例急救成员列表
     * 
     * @param rescueCaseRescuer 急救案例急救成员
     * @return 急救案例急救成员集合
     */
    public List<RescueCaseRescuer> selectRescueCaseRescuerList(RescueCaseRescuer rescueCaseRescuer);

    /**
     * 新增急救案例急救成员
     * 
     * @param rescueCaseRescuer 急救案例急救成员
     * @return 结果
     */
    public int insertRescueCaseRescuer(RescueCaseRescuer rescueCaseRescuer);

    /**
     * 修改急救案例急救成员
     * 
     * @param rescueCaseRescuer 急救案例急救成员
     * @return 结果
     */
    public int updateRescueCaseRescuer(RescueCaseRescuer rescueCaseRescuer);

    /**
     * 批量删除急救案例急救成员
     * 
     * @param ids 需要删除的急救案例急救成员主键集合
     * @return 结果
     */
    public int deleteRescueCaseRescuerByIds(Long[] ids);

    /**
     * 删除急救案例急救成员信息
     * 
     * @param id 急救案例急救成员主键
     * @return 结果
     */
    public int deleteRescueCaseRescuerById(Long id);
}
