package com.drxin.bizz.strategy.impl;

import com.drxin.bizz.domain.ContributionActionConfig;
import com.drxin.bizz.domain.ContributionLog;
import com.drxin.bizz.service.IContributionActionConfigService;
import com.drxin.bizz.strategy.ContributionStrategy;
import com.drxin.bizz.utils.ContributionCalculationUtils;
import com.drxin.bizz.utils.ContributionLogUtils;
import com.drxin.framework.event.ContributionActionEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 弟子身份通过贡献值策略
 * 
 * 当用户弟子身份申请通过时，为用户分配贡献值
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Slf4j
@Component
public class IdentityDiscipleContributionStrategy implements ContributionStrategy {

    @Resource
    private IContributionActionConfigService contributionActionConfigService;

    @Resource
    private ContributionLogUtils contributionLogUtils;

    @Override
    public String getSupportAction() {
        return "IDENTITY_DISCIPLE";
    }

    @Override
    public void process(ContributionActionEvent event) {
        log.info("开始处理弟子身份通过贡献值: actionCode={}", event.getActionCode());

        // 获取行为配置
        ContributionActionConfig actionConfig = contributionActionConfigService.getEnabledConfigByCode(getSupportAction());
        if (!ContributionCalculationUtils.isValidConfig(actionConfig)) {
            log.warn("弟子身份通过贡献值配置无效");
            return;
        }

        // 获取用户ID列表
        @SuppressWarnings("unchecked")
        List<Long> userIds = (List<Long>) event.getContext().get("successApplyIds");

        if (userIds == null || userIds.isEmpty()) {
            log.warn("用户ID列表为空，跳过处理");
            return;
        }

        // 计算贡献值（根据用户数量和配置的平均分配标志）
        double score = ContributionCalculationUtils.calculateScore(actionConfig, userIds.size());
        if (score <= 0) {
            log.warn("计算得到的贡献值为0或负数，跳过处理: score={}", score);
            return;
        }

        log.info("批量处理弟子身份通过贡献值: 用户数量={}, 每人贡献值={}, 配置={}",
            userIds.size(), score, ContributionCalculationUtils.getConfigDescription(actionConfig));

        // 为每个用户记录贡献值
        int processedCount = 0;
        for (Long userId : userIds) {
            try {
                // 防止重复发放贡献值，检查是否已存在贡献值日志
                ContributionLog existingLog = contributionLogUtils.getContributionLogByUserIdAndActionCode(
                    userId, getSupportAction());
                if (existingLog != null) {
                    log.warn("用户已存在弟子身份贡献值记录，跳过发放: userId={}", userId);
                    continue;
                }

                // 记录贡献值日志
                contributionLogUtils.recordContributionLog(userId, event, score,
                    actionConfig.getRemark(), userId);

                processedCount++;

            } catch (Exception e) {
                log.error("处理用户贡献值失败: userId={}", userId, e);
            }
        }

        log.info("弟子身份通过贡献值处理完成: 总数={}, 成功数={}",
            userIds.size(), processedCount);
    }
}
