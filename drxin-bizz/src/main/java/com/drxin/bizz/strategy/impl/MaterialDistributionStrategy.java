package com.drxin.bizz.strategy.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.drxin.bizz.domain.MaterialInfo;
import com.drxin.bizz.domain.MaterialReceiveLog;
import com.drxin.bizz.domain.UserApply;
import com.drxin.bizz.service.IMaterialInfoService;
import com.drxin.bizz.service.IMaterialReceiveLogService;
import com.drxin.bizz.service.IUserApplyService;
import com.drxin.bizz.strategy.ContributionStrategy;
import com.drxin.common.core.domain.entity.SysUser;
import com.drxin.framework.event.ContributionActionEvent;
import com.drxin.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 物料发放策略
 * 
 * 当用户身份申请通过时，自动为用户生成物料领取记录
 * 支持批量处理多个用户的物料发放
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
@Slf4j
@Component
public class MaterialDistributionStrategy implements ContributionStrategy {

    @Resource
    private IMaterialInfoService materialInfoService;

    @Resource
    private IMaterialReceiveLogService materialReceiveLogService;

    @Resource
    private ISysUserService sysUserService;

    @Resource
    private IUserApplyService userApplyService;

    @Override
    public String getSupportAction() {
        return "MATERIAL_DISTRIBUTION";
    }

    @Override
    public void process(ContributionActionEvent event) {
        log.info("开始处理物料发放事件: actionCode={}", event.getActionCode());

        try {
            // 获取成功的申请ID列表和用户类型分组
            @SuppressWarnings("unchecked")
            List<Long> successApplyIds = (List<Long>) event.getContext().get("successApplyIds");
            
            @SuppressWarnings("unchecked")
            Map<String, List<Long>> successIdsByUserType = (Map<String, List<Long>>) event.getContext().get("successIdsByUserType");

            if (successApplyIds == null || successApplyIds.isEmpty()) {
                log.warn("成功申请ID列表为空，跳过物料发放处理");
                return;
            }

            if (successIdsByUserType == null || successIdsByUserType.isEmpty()) {
                log.warn("按用户类型分组的申请ID为空，跳过物料发放处理");
                return;
            }

            log.info("开始批量处理物料发放: 总申请数量={}, 用户类型数量={}", 
                successApplyIds.size(), successIdsByUserType.size());

            // 按用户类型处理物料发放
            for (Map.Entry<String, List<Long>> entry : successIdsByUserType.entrySet()) {
                String userType = entry.getKey();
                List<Long> applyIds = entry.getValue();
                
                log.info("处理用户类型[{}]的物料发放: 申请数量={}", userType, applyIds.size());
                
                // 处理该用户类型的物料发放
                processUserTypeMaterialDistribution(userType, applyIds);
            }

            log.info("批量物料发放处理完成: 总处理数量={}", successApplyIds.size());

        } catch (Exception e) {
            log.error("处理物料发放事件失败: actionCode={}", event.getActionCode(), e);
            throw new RuntimeException("物料发放处理失败: " + e.getMessage());
        }
    }

    /**
     * 处理指定用户类型的物料发放
     *
     * @param userType 用户类型
     * @param applyIds 申请ID列表
     */
    private void processUserTypeMaterialDistribution(String userType, List<Long> applyIds) {
        try {
            // 1. 查询该用户类型可以领取的物料
            List<MaterialInfo> availableMaterials = getAvailableMaterialsByUserType(userType);
            
            if (availableMaterials == null || availableMaterials.isEmpty()) {
                log.info("用户类型[{}]没有可发放的物料，跳过处理", userType);
                return;
            }

            log.info("用户类型[{}]可发放物料数量: {}", userType, availableMaterials.size());

            // 2. 为每个申请ID对应的用户生成物料领取记录
            for (Long applyId : applyIds) {
                // 根据申请ID获取用户信息
                Long userId = getUserIdByApplyId(applyId);
                if (userId == null) {
                    log.warn("申请ID[{}]对应的用户不存在，跳过物料发放", applyId);
                    continue;
                }

                // 为该用户生成所有可领取物料的记录
                createMaterialReceiveRecords(userId, availableMaterials);
            }

        } catch (Exception e) {
            log.error("处理用户类型[{}]物料发放失败: applyIds={}", userType, applyIds, e);
        }
    }

    /**
     * 根据用户类型获取可发放的物料列表
     *
     * @param userType 用户类型
     * @return 可发放的物料列表
     */
    private List<MaterialInfo> getAvailableMaterialsByUserType(String userType) {
        try {
            // 查询条件：物料状态正常且用户类型匹配
            // 这里需要根据实际的物料配置逻辑来查询
            LambdaQueryWrapper<MaterialInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.like(MaterialInfo::getFreeRoles, userType)
                    .eq(MaterialInfo::getFirstFreeFlag, "Y"); // 只查询免费物料
            List<MaterialInfo> allMaterials = materialInfoService.list(queryWrapper);
            // 根据用户类型过滤物料（这里需要根据实际业务逻辑实现）
            return filterMaterialsByUserType(allMaterials, userType);
            
        } catch (Exception e) {
            log.error("查询用户类型[{}]可发放物料失败", userType, e);
            return null;
        }
    }

    /**
     * 根据用户类型过滤物料
     *
     * @param allMaterials 所有物料
     * @param userType 用户类型
     * @return 过滤后的物料列表
     */
    private List<MaterialInfo> filterMaterialsByUserType(List<MaterialInfo> allMaterials, String userType) {
        if (allMaterials == null || allMaterials.isEmpty()) {
            return allMaterials;
        }

        // 将用户类型转换为中文角色名称
        String roleName = convertUserTypeToRoleName(userType);
        if (roleName == null) {
            log.warn("未知的用户类型: {}", userType);
            return new ArrayList<>();
        }

        // 过滤出该用户类型可以领取的物料
        List<MaterialInfo> filteredMaterials = new ArrayList<>();
        for (MaterialInfo material : allMaterials) {
            if (canUserReceiveMaterial(material, userType)) {
                filteredMaterials.add(material);
            }
        }

        log.info("用户类型[{}]过滤后可领取物料数量: {} -> {}",
            userType, allMaterials.size(), filteredMaterials.size());

        return filteredMaterials;
    }

    /**
     * 将用户类型转换为角色名称
     *
     * @param userType 用户类型
     * @return 角色名称
     */
    private String convertUserTypeToRoleName(String userType) {
        if (userType == null) {
            return null;
        }

        switch (userType.toUpperCase()) {
            case "AIDER":
                return "急救员";
            case "MENTOR":
                return "急救导师";
            case "DISCIPLE":
                return "弟子";
            default:
                return null;
        }
    }

    /**
     * 判断用户是否可以领取指定物料
     *
     * @param material 物料信息
     * @param roleName 角色名称
     * @return 是否可以领取
     */
    private boolean canUserReceiveMaterial(MaterialInfo material, String roleName) {
        // 如果物料没有设置首次免费标志或免费角色，则所有人都可以领取
        if (!"Y".equals(material.getFirstFreeFlag()) ||
            material.getFreeRoles() == null || material.getFreeRoles().trim().isEmpty()) {
            return false;
        }

        // 检查用户角色是否在允许的免费角色列表中
        String[] allowedRoles = material.getFreeRoles().split(",");
        for (String allowedRole : allowedRoles) {
            if (roleName.equals(allowedRole.trim())) {
                return true;
            }
        }

        return false;
    }

    /**
     * 根据申请ID获取用户ID
     *
     * @param applyId 申请ID
     * @return 用户ID
     */
    private Long getUserIdByApplyId(Long applyId) {
        try {
            UserApply userApply = userApplyService.selectUserApplyById(applyId);
            if (userApply != null) {
                return userApply.getUserId();
            } else {
                log.warn("申请记录不存在: applyId={}", applyId);
                return null;
            }
        } catch (Exception e) {
            log.error("根据申请ID获取用户ID失败: applyId={}", applyId, e);
            return null;
        }
    }

    /**
     * 为用户创建物料领取记录
     *
     * @param userId 用户ID
     * @param materials 物料列表
     */
    private void createMaterialReceiveRecords(Long userId, List<MaterialInfo> materials) {
        try {
            SysUser user = sysUserService.selectUserById(userId);
            if (user == null) {
                log.warn("用户不存在: userId={}", userId);
                return;
            }

            for (MaterialInfo material : materials) {
                // 检查用户是否已经有该物料的记录
                if (hasExistingRecord(userId, material.getMaterialId())) {
                    log.debug("用户[{}]已存在物料[{}]的领取记录，跳过创建", userId, material.getMaterialId());
                    continue;
                }

                // 创建物料领取记录
                MaterialReceiveLog receiveLog = getMaterialReceiveLog(userId, material, user);

                int result = materialReceiveLogService.insertMaterialReceiveLog(receiveLog);
                
                if (result > 0) {
                    log.info("为用户[{}]创建物料[{}]领取记录成功: recordId={}", 
                        userId, material.getMaterialName(), receiveLog.getRecordId());
                } else {
                    log.warn("为用户[{}]创建物料[{}]领取记录失败", userId, material.getMaterialName());
                }
            }

        } catch (Exception e) {
            log.error("为用户[{}]创建物料领取记录失败", userId, e);
        }
    }

    private static MaterialReceiveLog getMaterialReceiveLog(Long userId, MaterialInfo material, SysUser user) {
        MaterialReceiveLog receiveLog = new MaterialReceiveLog();
        receiveLog.setUserId(userId);
        receiveLog.setUserName(user.getRealName() != null ? user.getRealName() : user.getUserName());
        receiveLog.setMaterialId(material.getMaterialId());
        receiveLog.setMaterialName(material.getMaterialName());
        receiveLog.setQuantity(1L); // 默认数量为1
        receiveLog.setStatus("pending"); // 待领取状态
        receiveLog.setTriggerType("auto"); // 系统自动生成
        receiveLog.setCreateTime(new Date());
        receiveLog.setUpdateTime(new Date());
        return receiveLog;
    }

    /**
     * 检查用户是否已存在指定物料的领取记录
     *
     * @param userId 用户ID
     * @param materialId 物料ID
     * @return 是否已存在记录
     */
    private boolean hasExistingRecord(Long userId, Long materialId) {
        try {

            LambdaQueryWrapper<MaterialReceiveLog> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MaterialReceiveLog::getUserId, userId)
                    .eq(MaterialReceiveLog::getMaterialId, materialId);

            long count = materialReceiveLogService.count(queryWrapper);
            return count > 0;
            
        } catch (Exception e) {
            log.error("检查用户[{}]物料[{}]领取记录失败", userId, materialId, e);
            return false;
        }
    }
}
