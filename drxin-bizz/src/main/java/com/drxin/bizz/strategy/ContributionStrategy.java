package com.drxin.bizz.strategy;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.drxin.bizz.domain.ContributionActionConfig;
import com.drxin.bizz.service.IContributionActionConfigService;
import com.drxin.framework.event.ContributionActionEvent;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 贡献值策略接口
 *
 * 设计原则：
 * - 策略负责完整的处理逻辑：计算 + 记录日志
 * - 监听器只负责调用策略
 * - 支持单个用户和批量用户处理（通过context区分）
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface ContributionStrategy {
    /**
     * 每个行为对应一个唯一 code，如 "COURSE_CHECKIN"
     *
     * @return 支持的行为编码
     */
    String getSupportAction();

    /**
     * 处理贡献值逻辑：计算 + 记录日志
     * 支持单个用户和批量用户处理，通过context中的数据区分：
     * - 单个用户：直接使用event.getUserId()
     * - 批量用户：context中包含"userIds"列表
     *
     * @param event 贡献值行为事件
     */
    void process(ContributionActionEvent event);
}

