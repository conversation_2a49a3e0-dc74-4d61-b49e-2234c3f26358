package com.drxin.bizz.strategy.impl;

import com.drxin.bizz.domain.ContributionActionConfig;
import com.drxin.bizz.service.IContributionActionConfigService;
import com.drxin.bizz.strategy.ContributionStrategy;
import com.drxin.bizz.utils.ContributionCalculationUtils;
import com.drxin.bizz.utils.ContributionLogUtils;
import com.drxin.framework.event.ContributionActionEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 远程指导员贡献值策略
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Slf4j
@Component
public class RemoteGuideContributionStrategy implements ContributionStrategy {

    @Resource
    private IContributionActionConfigService contributionActionConfigService;

    @Resource
    private ContributionLogUtils contributionLogUtils;

    @Override
    public String getSupportAction() {
        return "REMOTE_GUIDE_AID";
    }

    @Override
    public void process(ContributionActionEvent event) {
        log.info("开始处理远程指导员贡献值: actionCode={}", event.getActionCode());

        // 获取行为配置
        ContributionActionConfig actionConfig = contributionActionConfigService.getEnabledConfigByCode(getSupportAction());
        if (ContributionCalculationUtils.isValidConfig(actionConfig)) {
            log.warn("远程指导员贡献值配置无效");
            return;
        }

        // 获取远程指导员列表
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> remoteGuideUsers = (List<Map<String, Object>>) event.getContext().get("remoteGuideUsers");
        
        if (remoteGuideUsers == null || remoteGuideUsers.isEmpty()) {
            log.warn("远程指导员列表为空，跳过处理");
            return;
        }

        // 计算贡献值（根据远程指导员数量和配置的平均分配标志）
        double score = ContributionCalculationUtils.calculateScore(actionConfig, remoteGuideUsers.size());
        if (score <= 0) {
            log.warn("计算得到的贡献值为0或负数，跳过处理: score={}", score);
            return;
        }

        log.info("批量处理远程指导员贡献值: 远程指导员数量={}, 每人贡献值={}, 配置={}", 
            remoteGuideUsers.size(), score, ContributionCalculationUtils.getConfigDescription(actionConfig));

        // 为每个远程指导员记录贡献值
        int processedCount = 0;
        for (Map<String, Object> remoteGuideUser : remoteGuideUsers) {
            try {
                Long userId = (Long) remoteGuideUser.get("userId");
                Long caseId = (Long) remoteGuideUser.get("caseId");
                String userName = (String) remoteGuideUser.get("userName");
                String roleType = (String) remoteGuideUser.get("roleType");

                if (userId == null) {
                    log.warn("用户ID为空，跳过处理: remoteGuideUser={}", remoteGuideUser);
                    continue;
                }

                // 构建自定义描述信息
                String customDescription = String.format("远程指导员贡献值 - 案例ID: %s, 角色: %s", 
                    caseId, roleType != null ? roleType : "远程指导员");

                // 记录贡献值日志
                contributionLogUtils.recordContributionLog(userId, event, score, 
                    customDescription, caseId);
                
                processedCount++;
                log.debug("远程指导员贡献值记录成功: userId={}, userName={}, caseId={}, roleType={}, score={}", 
                    userId, userName, caseId, roleType, score);

            } catch (Exception e) {
                log.error("处理远程指导员贡献值失败: remoteGuideUser={}", remoteGuideUser, e);
            }
        }

        log.info("远程指导员贡献值处理完成: 总远程指导员数={}, 成功处理数={}", 
            remoteGuideUsers.size(), processedCount);
    }
}
