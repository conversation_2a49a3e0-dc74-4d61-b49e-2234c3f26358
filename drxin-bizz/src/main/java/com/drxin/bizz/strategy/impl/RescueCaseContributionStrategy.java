package com.drxin.bizz.strategy.impl;

import com.drxin.bizz.domain.ContributionActionConfig;
import com.drxin.bizz.service.IContributionActionConfigService;
import com.drxin.bizz.strategy.ContributionStrategy;
import com.drxin.bizz.utils.ContributionCalculationUtils;
import com.drxin.bizz.utils.ContributionLogUtils;
import com.drxin.framework.event.ContributionActionEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 急救案例通过贡献值策略
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Slf4j
@Component
public class RescueCaseContributionStrategy implements ContributionStrategy {

    @Resource
    private IContributionActionConfigService contributionActionConfigService;

    @Resource
    private ContributionLogUtils contributionLogUtils;

    @Override
    public String getSupportAction() {
        return "RESCUE_CASE";
    }

    @Override
    public void process(ContributionActionEvent event) {
        log.info("开始处理急救案例通过贡献值: actionCode={}", event.getActionCode());

        // 获取行为配置
        ContributionActionConfig actionConfig = contributionActionConfigService.getEnabledConfigByCode(getSupportAction());
        if (ContributionCalculationUtils.isValidConfig(actionConfig)) {
            log.warn("急救案例通过贡献值配置无效");
            return;
        }

        // 获取急救人员列表
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> rescueCaseUsers = (List<Map<String, Object>>) event.getContext().get("rescueCaseUsers");
        
        if (rescueCaseUsers == null || rescueCaseUsers.isEmpty()) {
            log.warn("急救人员列表为空，跳过处理");
            return;
        }

        // 计算贡献值（根据急救人员数量和配置的平均分配标志）
        double score = ContributionCalculationUtils.calculateScore(actionConfig, rescueCaseUsers.size());
        if (score <= 0) {
            log.warn("计算得到的贡献值为0或负数，跳过处理: score={}", score);
            return;
        }

        log.info("批量处理急救案例通过贡献值: 急救人员数量={}, 每人贡献值={}, 配置={}", 
            rescueCaseUsers.size(), score, ContributionCalculationUtils.getConfigDescription(actionConfig));

        // 为每个急救人员记录贡献值
        int processedCount = 0;
        for (Map<String, Object> rescueCaseUser : rescueCaseUsers) {
            try {
                Long userId = (Long) rescueCaseUser.get("userId");
                Long caseId = (Long) rescueCaseUser.get("caseId");
                String userName = (String) rescueCaseUser.get("userName");
                String roleType = (String) rescueCaseUser.get("roleType");

                if (userId == null) {
                    log.warn("用户ID为空，跳过处理: rescueCaseUser={}", rescueCaseUser);
                    continue;
                }

                // 构建自定义描述信息
                String customDescription = String.format("急救案例通过贡献值 - 案例ID: %s, 角色: %s", 
                    caseId, roleType != null ? roleType : "急救人员");

                // 记录贡献值日志
                contributionLogUtils.recordContributionLog(userId, event, score, 
                    customDescription, caseId);
                
                processedCount++;
                log.debug("急救案例通过贡献值记录成功: userId={}, userName={}, caseId={}, roleType={}, score={}", 
                    userId, userName, caseId, roleType, score);

            } catch (Exception e) {
                log.error("处理急救人员贡献值失败: rescueCaseUser={}", rescueCaseUser, e);
            }
        }

        log.info("急救案例通过贡献值处理完成: 总急救人员数={}, 成功处理数={}", 
            rescueCaseUsers.size(), processedCount);
    }
}
