package com.drxin.bizz.strategy.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.drxin.bizz.domain.ContributionActionConfig;
import com.drxin.bizz.service.IContributionActionConfigService;
import com.drxin.bizz.strategy.ContributionStrategy;
import com.drxin.bizz.utils.ContributionLogUtils;
import com.drxin.framework.event.ContributionActionEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 活动协办人贡献值策略
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@Slf4j
@Component
public class ActivityAssistantContributionStrategy implements ContributionStrategy {

    @Resource
    private IContributionActionConfigService contributionActionConfigService;

    @Resource
    private ContributionLogUtils contributionLogUtils;

    @Override
    public String getSupportAction() {
        return "ACTIVITY_ASSISTANT";
    }

    @Override
    public void process(ContributionActionEvent event) {
        try {
            // 获取用户ID列表，如果没有则直接返回
            @SuppressWarnings("unchecked")
            List<Long> userIds = (List<Long>) event.getContext().get("userIds");

            if (userIds == null || userIds.isEmpty()) {
                return;
            }

            // 获取行为配置
            ContributionActionConfig actionConfig = contributionActionConfigService.getEnabledConfigByCode(getSupportAction());
            if (actionConfig == null) {
                log.warn("活动协办贡献值配置未找到或未启用");
                return;
            }

            // 计算每个用户的贡献值（只计算一次）
            double score = calculateScore(actionConfig, userIds.size());

            // 获取活动ID作为关联业务ID
            Long activityId = (Long) event.getContext().get("activityId");

            // 循环处理每个用户
            for (Long userId : userIds) {
                if (score > 0) {
                    contributionLogUtils.recordContributionLog(userId, event, score, actionConfig.getRemark(), activityId);
                }
            }

            log.info("活动协办贡献值处理完成: userCount={}", userIds.size());

        } catch (Exception e) {
            log.error("处理活动协办贡献值失败: userId={}", event.getUserId(), e);
        }
    }

    /**
     * 计算贡献值
     *
     * @param config 行为配置
     * @param userCount 用户数量
     * @return 每个用户的贡献值
     */
    private double calculateScore(ContributionActionConfig config, int userCount) {
        try {
            if (config == null) {
                return 0.0;
            }

            BigDecimal baseScore = config.getScore();
            if (baseScore == null) {
                log.warn("活动协办贡献值配置分值为空");
                return 0.0;
            }

            double totalScore = baseScore.doubleValue();

            // 根据配置决定是否平均分配
            boolean shouldAverage = "Y".equals(config.getAverageFlag());
            double finalScore = shouldAverage && userCount > 1 ?
                totalScore / userCount : totalScore;

            log.info("计算活动协办贡献值: baseScore={}, userCount={}, shouldAverage={}, finalScore={}",
                baseScore, userCount, shouldAverage, finalScore);

            return finalScore;

        } catch (Exception e) {
            log.error("计算活动协办贡献值失败: configCode={}", config.getCode(), e);
            return 0.0;
        }
    }


}
