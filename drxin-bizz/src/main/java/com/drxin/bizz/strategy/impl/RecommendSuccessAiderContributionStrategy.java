package com.drxin.bizz.strategy.impl;

import com.drxin.bizz.domain.ContributionActionConfig;
import com.drxin.bizz.service.IContributionActionConfigService;
import com.drxin.bizz.strategy.ContributionStrategy;
import com.drxin.bizz.utils.ContributionCalculationUtils;
import com.drxin.bizz.utils.ContributionLogUtils;
import com.drxin.common.core.domain.entity.SysUser;
import com.drxin.framework.event.ContributionActionEvent;
import com.drxin.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 推荐成功急救员贡献值策略
 * 
 * 当用户身份申请通过时，为推荐人和成交人分配贡献值
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
@Component
public class RecommendSuccessAiderContributionStrategy implements ContributionStrategy {

    @Resource
    private IContributionActionConfigService contributionActionConfigService;

    @Resource
    private ContributionLogUtils contributionLogUtils;

    @Resource
    private ISysUserService sysUserService;

    @Override
    public String getSupportAction() {
        return "RECOMMEND_SUCCESS_AIDER";
    }

    @Override
    public void process(ContributionActionEvent event) {
        log.info("开始处理推荐成功急救员贡献值: actionCode={}", event.getActionCode());

        // 获取行为配置
        ContributionActionConfig actionConfig = contributionActionConfigService.getEnabledConfigByCode(getSupportAction());
        if (ContributionCalculationUtils.isValidConfig(actionConfig)) {
            log.warn("推荐成功急救员贡献值配置无效");
            return;
        }

        // 获取推荐信息列表
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> recommendInfoList = (List<Map<String, Object>>) event.getContext().get("recommendInfoList");

        if (recommendInfoList == null || recommendInfoList.isEmpty()) {
            log.warn("推荐信息列表为空，跳过处理");
            return;
        }

        log.info("批量处理推荐成功急救员贡献值: 推荐信息数量={}, 配置={}",
            recommendInfoList.size(), ContributionCalculationUtils.getConfigDescription(actionConfig));

        // 处理每个推荐信息
        int processedCount = 0;
        for (Map<String, Object> recommendInfo : recommendInfoList) {
            try {
                processRecommendInfo(recommendInfo, actionConfig, event);
                processedCount++;
            } catch (Exception e) {
                log.error("处理推荐信息失败: recommendInfo={}", recommendInfo, e);
            }
        }

        log.info("推荐成功急救员贡献值处理完成: 总数={}, 成功数={}",
            recommendInfoList.size(), processedCount);
    }

    /**
     * 处理单个推荐信息
     */
    private void processRecommendInfo(Map<String, Object> recommendInfo, ContributionActionConfig actionConfig, ContributionActionEvent event) {
        Long applyId = (Long) recommendInfo.get("applyId");
        Long userId = (Long) recommendInfo.get("userId");
        String userType = (String) recommendInfo.get("userType");
        Long inviterId = (Long) recommendInfo.get("inviterId");
        String dealInviterId = (String) recommendInfo.get("dealInviterId");
        String realName = (String) recommendInfo.get("realName");

        log.debug("处理推荐信息: applyId={}, userId={}, userType={}, inviterId={}, dealInviterId={}, realName={}",
            applyId, userId, userType, inviterId, dealInviterId, realName);

        double score = ContributionCalculationUtils.calculateScore(actionConfig);

        // 处理推荐人贡献值 - 增加贡献值
        if (inviterId != null) {
            contributionLogUtils.recordContributionLog(userId, event, score, "推荐人改变增加贡献值", applyId);
        }

        // 处理成交人贡献值 - 删除贡献值（负数）
        if (dealInviterId != null && !dealInviterId.trim().isEmpty()) {
            contributionLogUtils.recordContributionLog(userId, event, -score, "成交人改变删除贡献值", applyId);
        }
    }
}
