package com.drxin.bizz.strategy.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.drxin.bizz.domain.TeamMember;
import com.drxin.bizz.service.ITeamMemberService;
import com.drxin.bizz.strategy.ContributionStrategy;
import com.drxin.common.core.domain.entity.SysUser;
import com.drxin.common.utils.StringUtils;
import com.drxin.framework.event.ContributionActionEvent;
import com.drxin.system.mapper.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 团队退出策略
 * 
 * 当用户升级为弟子时，退出所在团队，并连同底下所有下级都退出原团队
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Slf4j
@Component
public class TeamExitStrategy implements ContributionStrategy {

    @Resource
    private ITeamMemberService teamMemberService;

    @Resource
    private SysUserMapper sysUserMapper;

    @Override
    public String getSupportAction() {
        return "TEAM_EXIT";
    }

    @Override
    public void process(ContributionActionEvent event) {
        try {
            List<Long> userIds = (List<Long>) event.getContext().get("userIds");
            
            if (userIds == null || userIds.isEmpty()) {
                log.warn("用户ID列表为空，跳过团队退出处理");
                return;
            }

            log.info("开始处理团队退出: userIds={}", userIds);

            // 处理每个用户的团队退出
            for (Long userId : userIds) {
                processUserTeamExit(userId);
            }

            log.info("团队退出处理完成: userCount={}", userIds.size());

        } catch (Exception e) {
            log.error("处理团队退出失败: userId={}, actionCode={}", 
                event.getUserId(), event.getActionCode(), e);
            throw e; // 重新抛出异常，让监听器记录失败状态
        }
    }

    /**
     * 处理单个用户的团队退出
     * 
     * @param userId 用户ID
     */
    private void processUserTeamExit(Long userId) {
        try {
            log.info("开始处理用户团队退出: userId={}", userId);

            // 获取所有需要退出团队的用户（包括该用户及其所有下级）
            List<String> allExitUserIds = new ArrayList<>();
            
            // 递归查找该用户的所有下级
            findAllSubordinates(userId.toString(), allExitUserIds, new HashSet<>());
            
            // 将当前用户也加入退出列表
            allExitUserIds.add(userId.toString());

            if (allExitUserIds.isEmpty()) {
                log.info("没有需要退出团队的用户: userId={}", userId);
                return;
            }

            // 批量删除团队成员记录
            batchDeleteTeamMembers(allExitUserIds);
            
            log.info("用户团队退出完成: userId={}, 退出成员数量={}", userId, allExitUserIds.size());

        } catch (Exception e) {
            log.error("处理用户团队退出失败: userId={}", userId, e);
            throw e;
        }
    }

    /**
     * 递归查找所有下级用户
     * 
     * @param userId 用户ID
     * @param allExitUserIds 所有需要退出的用户ID集合
     * @param visited 已访问的用户ID集合，防止循环引用
     */
    private void findAllSubordinates(String userId, List<String> allExitUserIds, Set<String> visited) {
        if (StringUtils.isEmpty(userId) || visited.contains(userId)) {
            return;
        }
        
        visited.add(userId);
        
        // 查询当前用户的直接下级
        List<SysUser> subordinates = sysUserMapper.selectUsersByInviterId(userId);
        
        for (SysUser subordinate : subordinates) {
            String subordinateId = subordinate.getUserId().toString();
            if (!visited.contains(subordinateId)) {
                // 将下级用户加入退出列表
                allExitUserIds.add(subordinateId);
                
                // 继续递归查找下级的下级
                findAllSubordinates(subordinateId, allExitUserIds, visited);
            }
        }
    }

    /**
     * 批量删除团队成员记录
     * 
     * @param memberIds 需要删除的成员ID列表
     */
    private void batchDeleteTeamMembers(List<String> memberIds) {
        if (memberIds.isEmpty()) {
            log.info("没有需要删除的团队成员记录");
            return;
        }
        
        try {
            // 使用 MyBatis Plus 的批量删除
            LambdaQueryWrapper<TeamMember> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(TeamMember::getMemberId, memberIds);
            
            int deletedCount = teamMemberService.remove(queryWrapper);
            
            log.info("批量删除团队成员记录完成: 删除数量={}, 成员ID列表={}", deletedCount, memberIds);
            
        } catch (Exception e) {
            log.error("批量删除团队成员记录失败: memberIds={}", memberIds, e);
            throw e;
        }
    }
}
