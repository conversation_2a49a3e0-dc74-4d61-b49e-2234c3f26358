package com.drxin.bizz.strategy.impl;

import com.drxin.bizz.domain.ContributionActionConfig;
import com.drxin.bizz.domain.UserApply;
import com.drxin.bizz.mapper.UserApplyMapper;
import com.drxin.bizz.service.IContributionActionConfigService;
import com.drxin.bizz.strategy.ContributionStrategy;
import com.drxin.bizz.utils.ContributionCalculationUtils;
import com.drxin.bizz.utils.ContributionLogUtils;
import com.drxin.framework.event.ContributionActionEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 急救员身份通过贡献值策略
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Slf4j
@Component
public class IdentityAiderContributionStrategy implements ContributionStrategy {

    @Resource
    private IContributionActionConfigService contributionActionConfigService;

    @Resource
    private ContributionLogUtils contributionLogUtils;

    @Resource
    private UserApplyMapper userApplyMapper;

    @Override
    public String getSupportAction() {
        return "IDENTITY_AIDER";
    }

    @Override
    public void process(ContributionActionEvent event) {
        log.info("开始处理急救员身份通过贡献值: actionCode={}", event.getActionCode());

        // 获取行为配置
        ContributionActionConfig actionConfig = contributionActionConfigService.getEnabledConfigByCode(getSupportAction());
        if (ContributionCalculationUtils.isValidConfig(actionConfig)) {
            log.warn("急救员身份通过贡献值配置无效");
            return;
        }

        // 获取成功的申请ID列表
        @SuppressWarnings("unchecked")
        List<Long> successApplyIds = (List<Long>) event.getContext().get("successApplyIds");

        if (successApplyIds == null || successApplyIds.isEmpty()) {
            log.warn("成功申请ID列表为空，跳过处理");
            return;
        }

        // 计算贡献值（根据用户数量和配置的平均分配标志）
        double score = ContributionCalculationUtils.calculateScore(actionConfig, successApplyIds.size());
        if (score <= 0) {
            log.warn("计算得到的贡献值为0或负数，跳过处理: score={}", score);
            return;
        }

        log.info("批量处理急救员身份通过贡献值: 申请数量={}, 每人贡献值={}, 配置={}",
            successApplyIds.size(), score, ContributionCalculationUtils.getConfigDescription(actionConfig));

        // 为每个成功的申请记录贡献值
        int processedCount = 0;
        for (Long applyId : successApplyIds) {
            try {
                // 查询申请信息获取用户ID
                UserApply userApply = userApplyMapper.selectUserApplyById(applyId);
                if (userApply == null || userApply.getUserId() == null) {
                    log.warn("申请记录不存在或用户ID为空: applyId={}", applyId);
                    continue;
                }
                // 防止重复发放贡献值，检查是否已存在贡献值日志
                boolean alreadyExists = contributionLogUtils.getContributionLogByUserIdAndActionCode(
                    userApply.getUserId(), event.getActionCode()) != null;
                if (alreadyExists) {
                    log.warn("用户已存在急救员身份贡献值记录，跳过发放: userId={}, applyId={}", userApply.getUserId(), applyId);
                    continue;
                }

                // 记录贡献值日志
                contributionLogUtils.recordContributionLog(userApply.getUserId(), event, score,
                    actionConfig.getRemark(), applyId);
                processedCount++;
            } catch (Exception e) {
                log.error("处理申请贡献值失败: applyId={}", applyId, e);
            }
        }
        log.info("急救员身份通过贡献值处理完成: 总数={}，成功处理数={}", successApplyIds.size(), processedCount);
    }
}
