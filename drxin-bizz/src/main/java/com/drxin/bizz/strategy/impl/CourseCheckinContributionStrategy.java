package com.drxin.bizz.strategy.impl;

import com.drxin.bizz.domain.ContributionActionConfig;
import com.drxin.bizz.service.IContributionActionConfigService;
import com.drxin.bizz.strategy.ContributionStrategy;
import com.drxin.bizz.utils.ContributionCalculationUtils;
import com.drxin.bizz.utils.ContributionLogUtils;
import com.drxin.framework.event.ContributionActionEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 课程签到贡献值策略
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Slf4j
@Component
public class CourseCheckinContributionStrategy implements ContributionStrategy {

    @Resource
    private IContributionActionConfigService contributionActionConfigService;

    @Resource
    private ContributionLogUtils contributionLogUtils;

    @Override
    public String getSupportAction() {
        return "COURSE_CHECKIN";
    }

    @Override
    public void process(ContributionActionEvent event) {
        log.info("开始处理课程签到贡献值: userId={}, actionCode={}",
            event.getUserId(), event.getActionCode());

        // 获取行为配置
        ContributionActionConfig actionConfig = contributionActionConfigService.getEnabledConfigByCode(event.getActionCode());
        if (ContributionCalculationUtils.isValidConfig(actionConfig)) {
            log.warn("课程签到贡献值配置无效");
            return;
        }

        // 计算贡献值（课程签到通常是单个用户行为，用户数量为1）
        double score = ContributionCalculationUtils.calculateScore(actionConfig);
        if (score <= 0) {
            log.warn("计算得到的贡献值为0或负数，跳过处理: score={}", score);
            return;
        }

        // 获取考勤ID作为关联业务ID
        Long attendanceId = (Long) event.getContext().get("attendanceId");

        // 记录贡献值日志
        contributionLogUtils.recordContributionLog(event.getUserId(), event, score,
            actionConfig.getRemark(), attendanceId);

        log.info("课程签到贡献值处理完成: userId={}, actionCode={}, score={}",
            event.getUserId(), event.getActionCode(), score);
    }
}
