package com.drxin.bizz.utils;

import com.drxin.bizz.domain.ContributionActionConfig;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 贡献值计算工具类
 * 
 * 提供统一的贡献值计算逻辑，支持平均分配和固定分值两种模式
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Slf4j
public class ContributionCalculationUtils {

    /**
     * 计算贡献值
     * 
     * @param config 行为配置
     * @param userCount 用户数量
     * @return 每个用户应得的贡献值
     */
    public static double calculateScore(ContributionActionConfig config, int userCount) {
        if (config == null || config.getScore() == null) {
            log.warn("贡献值配置为空或分值为空");
            return 0.0;
        }

        if (userCount <= 0) {
            log.warn("用户数量必须大于0: userCount={}", userCount);
            return 0.0;
        }

        BigDecimal baseScore = config.getScore();
        boolean needAverage = "Y".equalsIgnoreCase(config.getAverageFlag());

        double finalScore;
        if (needAverage) {
            // 需要平均分配：总分值除以用户数量
            finalScore = baseScore.divide(BigDecimal.valueOf(userCount), 2, RoundingMode.HALF_UP).doubleValue();
            log.debug("平均分配贡献值: baseScore={}, userCount={}, finalScore={}", 
                baseScore, userCount, finalScore);
        } else {
            // 固定分值：每个用户都获得完整分值
            finalScore = baseScore.doubleValue();
            log.debug("固定分值贡献值: baseScore={}, userCount={}, finalScore={}", 
                baseScore, userCount, finalScore);
        }

        return finalScore;
    }

    /**
     * 计算单个用户贡献值（用户数量为1的便捷方法）
     * 
     * @param config 行为配置
     * @return 用户应得的贡献值
     */
    public static double calculateScore(ContributionActionConfig config) {
        return calculateScore(config, 1);
    }

    /**
     * 验证贡献值配置是否有效
     * 
     * @param config 行为配置
     * @return true-配置有效，false-配置无效
     */
    public static boolean isValidConfig(ContributionActionConfig config) {
        if (config == null) {
            log.warn("贡献值配置为空");
            return true;
        }

        if (config.getScore() == null) {
            log.warn("贡献值配置分值为空: code={}", config.getCode());
            return true;
        }

        if (config.getScore().compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("贡献值配置分值必须大于0: code={}, score={}", config.getCode(), config.getScore());
            return true;
        }

        if (!"1".equalsIgnoreCase(config.getEnabled())) {
            log.warn("贡献值配置未启用: code={}, enabled={}", config.getCode(), config.getEnabled());
            return true;
        }

        return false;
    }

    /**
     * 获取配置描述信息
     * 
     * @param config 行为配置
     * @return 描述信息
     */
    public static String getConfigDescription(ContributionActionConfig config) {
        if (config == null) {
            return "配置为空";
        }

        boolean needAverage = "Y".equalsIgnoreCase(config.getAverageFlag());
        String mode = needAverage ? "平均分配" : "固定分值";
        
        return String.format("%s (分值: %s, 模式: %s)", 
            config.getName(), config.getScore(), mode);
    }

    /**
     * 计算总贡献值（所有用户的贡献值总和）
     * 
     * @param config 行为配置
     * @param userCount 用户数量
     * @return 总贡献值
     */
    public static double calculateTotalScore(ContributionActionConfig config, int userCount) {
        if (config == null || config.getScore() == null || userCount <= 0) {
            return 0.0;
        }

        boolean needAverage = "Y".equalsIgnoreCase(config.getAverageFlag());
        
        if (needAverage) {
            // 平均分配模式：总分值就是基础分值
            return config.getScore().doubleValue();
        } else {
            // 固定分值模式：总分值 = 基础分值 × 用户数量
            return config.getScore().multiply(BigDecimal.valueOf(userCount)).doubleValue();
        }
    }
}
