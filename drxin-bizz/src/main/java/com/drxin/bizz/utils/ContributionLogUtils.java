package com.drxin.bizz.utils;

import com.drxin.bizz.domain.ContributionLog;
import com.drxin.bizz.service.IContributionLogService;
import com.drxin.common.core.domain.entity.SysUser;
import com.drxin.framework.event.ContributionActionEvent;
import com.drxin.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * 贡献值日志工具类
 * 提供统一的贡献值日志记录功能，供各个策略类复用
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@Slf4j
@Component
public class ContributionLogUtils {

    @Resource
    private IContributionLogService contributionLogService;

    @Resource
    private ISysUserService userService;

    /**
     * 记录贡献值日志
     *
     * @param userId 用户ID
     * @param event 贡献值事件
     * @param score 贡献值分数
     * @param description 描述信息
     * @param refId 关联业务ID
     */
    public void recordContributionLog(Long userId, ContributionActionEvent event, double score, String description, Long refId) {
        try {
            SysUser user = userService.selectUserById(userId);
            if (user == null) {
                log.warn("用户不存在: userId={}", userId);
                return;
            }

            ContributionLog contributionLog = new ContributionLog();
            contributionLog.setUserId(userId);
            contributionLog.setUserName(user.getUserName());
            contributionLog.setActionCode(event.getActionCode());
            contributionLog.setScore(BigDecimal.valueOf(score));
            contributionLog.setDescription(description);
            contributionLog.setSourceType("SYSTEM");
            contributionLog.setLogStatus("1");

            // 设置关联业务ID
            if (refId != null) {
                contributionLog.setActionRefId(refId);
            }

            contributionLogService.insertContributionLog(contributionLog);

            // 更新用户总贡献值
            contributionLogService.updateUserTotalContribution(userId, BigDecimal.valueOf(score));

            log.debug("贡献值日志记录成功: userId={}, actionCode={}, score={}, logId={}",
                userId, event.getActionCode(), score, contributionLog.getId());

        } catch (Exception e) {
            log.error("记录贡献值日志失败: userId={}, actionCode={}, score={}", 
                userId, event.getActionCode(), score, e);
        }
    }

    // 根据userId和actionCode查询用户贡献值日志
    public ContributionLog getContributionLogByUserIdAndActionCode(Long userId, String actionCode) {
        return contributionLogService.getContributionLogByUserIdAndActionCode(userId, actionCode);
    }

}
