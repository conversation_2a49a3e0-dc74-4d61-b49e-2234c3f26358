package com.drxin.bizz.factory;

import com.drxin.bizz.strategy.ContributionStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ContributionStrategyFactory {

    private final Map<String, ContributionStrategy> strategyMap = new HashMap<>();

    @Autowired
    public ContributionStrategyFactory(List<ContributionStrategy> strategies) {
        for (ContributionStrategy strategy : strategies) {
            strategyMap.put(strategy.getSupportAction(), strategy);
        }
    }

    public ContributionStrategy getStrategy(String actionCode) {
        return strategyMap.get(actionCode);
    }
}
