package com.drxin.bizz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import com.drxin.bizz.domain.CretApply;
import com.drxin.bizz.vo.CretApplyExportVo;
import com.drxin.bizz.vo.CretApplyVo;
import org.apache.ibatis.annotations.Param;

/**
 * 证件申请Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-02
 */
public interface CretApplyMapper extends BaseMapper<CretApply> {
    /**
     * 查询证件申请
     * 
     * @param id 证件申请主键
     * @return 证件申请
     */
    public CretApply selectCretApplyById(Long id);

    /**
     * 查询证件申请列表
     * 
     * @param cretApply 证件申请
     * @return 证件申请集合
     */
    public List<CretApply> selectCretApplyList(CretApplyVo cretApply);

    /**
     * 新增证件申请
     * 
     * @param cretApply 证件申请
     * @return 结果
     */
    public int insertCretApply(CretApply cretApply);

    /**
     * 修改证件申请
     * 
     * @param cretApply 证件申请
     * @return 结果
     */
    public int updateCretApply(CretApply cretApply);

    /**
     * 删除证件申请
     * 
     * @param id 证件申请主键
     * @return 结果
     */
    public int deleteCretApplyById(Long id);

    /**
     * 批量删除证件申请
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCretApplyByIds(Long[] ids);

    List<CretApplyExportVo> selectCretApplyExportVoList(CretApplyVo cretApply);
}
