package com.drxin.bizz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import com.drxin.bizz.domain.UserApply;

/**
 * 用户身份申请Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
public interface UserApplyMapper extends BaseMapper<UserApply> {
    /**
     * 查询用户身份申请
     * 
     * @param id 用户身份申请主键
     * @return 用户身份申请
     */
    public UserApply selectUserApplyById(Long id);

    /**
     * 查询用户身份申请列表
     * 
     * @param userApply 用户身份申请
     * @return 用户身份申请集合
     */
    public List<UserApply> selectUserApplyList(UserApply userApply);

    /**
     * 新增用户身份申请
     * 
     * @param userApply 用户身份申请
     * @return 结果
     */
    public int insertUserApply(UserApply userApply);

    /**
     * 修改用户身份申请
     * 
     * @param userApply 用户身份申请
     * @return 结果
     */
    public int updateUserApply(UserApply userApply);

    /**
     * 删除用户身份申请
     * 
     * @param id 用户身份申请主键
     * @return 结果
     */
    public int deleteUserApplyById(Long id);

    /**
     * 批量删除用户身份申请
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserApplyByIds(Long[] ids);

    /**
     * 查询用户是否有审核中的申请
     *
     * @param userId 用户ID
     * @return 审核中的申请数量
     */
    public int countPendingApplyByUserId(Long userId);
}
