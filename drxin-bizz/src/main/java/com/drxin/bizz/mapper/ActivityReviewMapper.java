package com.drxin.bizz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.drxin.bizz.domain.ActivityReview;
import com.drxin.bizz.vo.ActivityReviewVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 活动评审Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Mapper
public interface ActivityReviewMapper extends BaseMapper<ActivityReview> {

    /**
     * 查询活动评审
     * 
     * @param id 活动评审主键
     * @return 活动评审
     */
    ActivityReview selectActivityReviewById(Long id);

    /**
     * 查询活动评审列表
     * 
     * @param activityReview 活动评审
     * @return 活动评审集合
     */
    List<ActivityReviewVo> selectActivityReviewList(ActivityReview activityReview);

    /**
     * 新增活动评审
     * 
     * @param activityReview 活动评审
     * @return 结果
     */
    int insertActivityReview(ActivityReview activityReview);

    /**
     * 修改活动评审
     * 
     * @param activityReview 活动评审
     * @return 结果
     */
    int updateActivityReview(ActivityReview activityReview);

    /**
     * 删除活动评审
     * 
     * @param id 活动评审主键
     * @return 结果
     */
    int deleteActivityReviewById(Long id);

    /**
     * 批量删除活动评审
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteActivityReviewByIds(Long[] ids);
}
