package com.drxin.bizz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.drxin.bizz.domain.ContributionEventLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 贡献值事件日志Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Mapper
public interface ContributionEventLogMapper extends BaseMapper<ContributionEventLog> {

    /**
     * 查询贡献值事件日志
     *
     * @param id 贡献值事件日志主键
     * @return 贡献值事件日志
     */
    ContributionEventLog selectContributionEventLogById(Long id);

    /**
     * 查询贡献值事件日志列表
     *
     * @param contributionEventLog 贡献值事件日志
     * @return 贡献值事件日志集合
     */
    List<ContributionEventLog> selectContributionEventLogList(ContributionEventLog contributionEventLog);

    /**
     * 新增贡献值事件日志
     *
     * @param contributionEventLog 贡献值事件日志
     * @return 结果
     */
    int insertContributionEventLog(ContributionEventLog contributionEventLog);

    /**
     * 修改贡献值事件日志
     *
     * @param contributionEventLog 贡献值事件日志
     * @return 结果
     */
    int updateContributionEventLog(ContributionEventLog contributionEventLog);

    /**
     * 删除贡献值事件日志
     *
     * @param id 贡献值事件日志主键
     * @return 结果
     */
    int deleteContributionEventLogById(Long id);

    /**
     * 批量删除贡献值事件日志
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteContributionEventLogByIds(Long[] ids);

    /**
     * 查询待处理的事件日志
     *
     * @param limit 限制数量
     * @return 待处理的事件日志列表
     */
    List<ContributionEventLog> selectPendingEventLogs(@Param("limit") int limit);

    /**
     * 查询处理失败的事件日志
     *
     * @param limit 限制数量
     * @return 处理失败的事件日志列表
     */
    List<ContributionEventLog> selectFailedEventLogs(@Param("limit") int limit);
}
