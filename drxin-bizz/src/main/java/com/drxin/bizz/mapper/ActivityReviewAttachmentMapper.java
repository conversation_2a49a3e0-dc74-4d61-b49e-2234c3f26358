package com.drxin.bizz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.drxin.bizz.domain.ActivityReviewAttachment;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 活动评审附件Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Mapper
public interface ActivityReviewAttachmentMapper extends BaseMapper<ActivityReviewAttachment> {

    /**
     * 根据活动ID查询附件列表
     * 
     * @param activityId 活动ID
     * @return 附件集合
     */
    List<ActivityReviewAttachment> selectAttachmentsByActivityId(Long activityId);

    /**
     * 新增活动评审附件
     * 
     * @param activityReviewAttachment 活动评审附件
     * @return 结果
     */
    int insertActivityReviewAttachment(ActivityReviewAttachment activityReviewAttachment);

    /**
     * 根据活动ID删除附件
     * 
     * @param activityId 活动ID
     * @return 结果
     */
    int deleteAttachmentsByActivityId(Long activityId);
}
