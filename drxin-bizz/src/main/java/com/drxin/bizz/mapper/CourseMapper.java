package com.drxin.bizz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import com.drxin.bizz.domain.Course;

/**
 * 课程信息管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface CourseMapper extends BaseMapper<Course> {
    /**
     * 查询课程信息管理
     * 
     * @param id 课程信息管理主键
     * @return 课程信息管理
     */
    public Course selectCourseById(Long id);

    /**
     * 查询课程信息管理列表
     * 
     * @param course 课程信息管理
     * @return 课程信息管理集合
     */
    public List<Course> selectCourseList(Course course);

    /**
     * 新增课程信息管理
     * 
     * @param course 课程信息管理
     * @return 结果
     */
    public int insertCourse(Course course);

    /**
     * 修改课程信息管理
     * 
     * @param course 课程信息管理
     * @return 结果
     */
    public int updateCourse(Course course);

    /**
     * 删除课程信息管理
     * 
     * @param id 课程信息管理主键
     * @return 结果
     */
    public int deleteCourseById(Long id);

    /**
     * 批量删除课程信息管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCourseByIds(Long[] ids);

    List<Course> selectLatestCourse();

    List<Course> selectCurrentMonthCourse();

    void cleanCourseStatus();
}
