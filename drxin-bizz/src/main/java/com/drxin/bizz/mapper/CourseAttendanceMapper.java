package com.drxin.bizz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import com.drxin.bizz.domain.CourseAttendance;
import com.drxin.bizz.vo.CourseAttendExportVo;
import com.drxin.bizz.vo.CourseAttendanceVo;

/**
 * 课程签到记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface CourseAttendanceMapper extends BaseMapper<CourseAttendance> {
    /**
     * 查询课程签到记录
     * 
     * @param id 课程签到记录主键
     * @return 课程签到记录
     */
    public CourseAttendance selectCourseAttendanceById(Long id);

    /**
     * 查询课程签到记录列表
     * 
     * @param courseAttendanceVo 课程签到记录
     * @return 课程签到记录集合
     */
    public List<CourseAttendanceVo> selectCourseAttendanceList(CourseAttendanceVo courseAttendanceVo);

    List<CourseAttendExportVo> selectCourseAttendanceExportList(CourseAttendanceVo courseAttendanceVo);

    /**
     * 新增课程签到记录
     * 
     * @param courseAttendance 课程签到记录
     * @return 结果
     */
    public int insertCourseAttendance(CourseAttendance courseAttendance);

    /**
     * 修改课程签到记录
     * 
     * @param courseAttendance 课程签到记录
     * @return 结果
     */
    public int updateCourseAttendance(CourseAttendance courseAttendance);

    /**
     * 删除课程签到记录
     * 
     * @param id 课程签到记录主键
     * @return 结果
     */
    public int deleteCourseAttendanceById(Long id);

    /**
     * 批量删除课程签到记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCourseAttendanceByIds(Long[] ids);
}
