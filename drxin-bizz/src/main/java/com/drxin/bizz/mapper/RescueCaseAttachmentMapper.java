package com.drxin.bizz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.drxin.bizz.domain.RescueCaseAttachment;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 急救案例附件Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
@Mapper
public interface RescueCaseAttachmentMapper extends BaseMapper<RescueCaseAttachment> {

    /**
     * 根据案例ID查询附件列表
     * 
     * @param caseId 急救案例ID
     * @return 附件集合
     */
    List<RescueCaseAttachment> selectAttachmentsByCaseId(Long caseId);

    /**
     * 新增急救案例附件
     * 
     * @param rescueCaseAttachment 急救案例附件
     * @return 结果
     */
    int insertRescueCaseAttachment(RescueCaseAttachment rescueCaseAttachment);

    /**
     * 根据案例ID删除附件
     * 
     * @param caseId 急救案例ID
     * @return 结果
     */
    int deleteAttachmentsByCaseId(Long caseId);
}
