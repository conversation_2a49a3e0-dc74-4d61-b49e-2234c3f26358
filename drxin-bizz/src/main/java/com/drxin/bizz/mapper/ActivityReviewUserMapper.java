package com.drxin.bizz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.drxin.bizz.domain.ActivityReviewUser;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 活动评审人员关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Mapper
public interface ActivityReviewUserMapper extends BaseMapper<ActivityReviewUser> {

    /**
     * 根据活动ID查询人员列表
     * 
     * @param activityId 活动ID
     * @return 人员集合
     */
    List<ActivityReviewUser> selectUsersByActivityId(Long activityId);

    /**
     * 根据活动ID和角色类型查询人员列表
     * 
     * @param activityId 活动ID
     * @param roleType 角色类型
     * @return 人员集合
     */
    List<ActivityReviewUser> selectUsersByActivityIdAndRoleType(Long activityId, Integer roleType);

    /**
     * 新增活动评审人员关联
     * 
     * @param activityReviewUser 活动评审人员关联
     * @return 结果
     */
    int insertActivityReviewUser(ActivityReviewUser activityReviewUser);

    /**
     * 根据活动ID删除人员关联
     *
     * @param activityId 活动ID
     * @return 结果
     */
    int deleteUsersByActivityId(Long activityId);

    /**
     * 批量插入人员关联
     *
     * @param users 人员列表
     * @return 结果
     */
    int insertBatch(List<ActivityReviewUser> users);
}
