package com.drxin.bizz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import com.drxin.bizz.domain.RescueCaseRescuer;

/**
 * 急救案例急救成员Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface RescueCaseRescuerMapper extends BaseMapper<RescueCaseRescuer> {
    /**
     * 查询急救案例急救成员
     * 
     * @param id 急救案例急救成员主键
     * @return 急救案例急救成员
     */
    public RescueCaseRescuer selectRescueCaseRescuerById(Long id);

    /**
     * 查询急救案例急救成员列表
     * 
     * @param rescueCaseRescuer 急救案例急救成员
     * @return 急救案例急救成员集合
     */
    public List<RescueCaseRescuer> selectRescueCaseRescuerList(RescueCaseRescuer rescueCaseRescuer);

    /**
     * 新增急救案例急救成员
     * 
     * @param rescueCaseRescuer 急救案例急救成员
     * @return 结果
     */
    public int insertRescueCaseRescuer(RescueCaseRescuer rescueCaseRescuer);

    /**
     * 修改急救案例急救成员
     * 
     * @param rescueCaseRescuer 急救案例急救成员
     * @return 结果
     */
    public int updateRescueCaseRescuer(RescueCaseRescuer rescueCaseRescuer);

    /**
     * 删除急救案例急救成员
     * 
     * @param id 急救案例急救成员主键
     * @return 结果
     */
    public int deleteRescueCaseRescuerById(Long id);

    /**
     * 批量删除急救案例急救成员
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRescueCaseRescuerByIds(Long[] ids);
}
