package com.drxin.bizz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import com.drxin.bizz.domain.ContributionActionConfig;

/**
 * 贡献值配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-29
 */
public interface ContributionActionConfigMapper extends BaseMapper<ContributionActionConfig> {
    /**
     * 查询贡献值配置
     * 
     * @param id 贡献值配置主键
     * @return 贡献值配置
     */
    public ContributionActionConfig selectContributionActionConfigById(Long id);

    /**
     * 查询贡献值配置列表
     * 
     * @param contributionActionConfig 贡献值配置
     * @return 贡献值配置集合
     */
    public List<ContributionActionConfig> selectContributionActionConfigList(ContributionActionConfig contributionActionConfig);

    /**
     * 新增贡献值配置
     * 
     * @param contributionActionConfig 贡献值配置
     * @return 结果
     */
    public int insertContributionActionConfig(ContributionActionConfig contributionActionConfig);

    /**
     * 修改贡献值配置
     * 
     * @param contributionActionConfig 贡献值配置
     * @return 结果
     */
    public int updateContributionActionConfig(ContributionActionConfig contributionActionConfig);

    /**
     * 删除贡献值配置
     * 
     * @param id 贡献值配置主键
     * @return 结果
     */
    public int deleteContributionActionConfigById(Long id);

    /**
     * 批量删除贡献值配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteContributionActionConfigByIds(Long[] ids);
}
