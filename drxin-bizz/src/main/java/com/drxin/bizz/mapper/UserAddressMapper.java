package com.drxin.bizz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import com.drxin.bizz.domain.UserAddress;

/**
 * 用户地址管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-02
 */
public interface UserAddressMapper extends BaseMapper<UserAddress> {
    /**
     * 查询用户地址管理
     * 
     * @param id 用户地址管理主键
     * @return 用户地址管理
     */
    public UserAddress selectUserAddressById(String id);

    /**
     * 查询用户地址管理列表
     * 
     * @param userAddress 用户地址管理
     * @return 用户地址管理集合
     */
    public List<UserAddress> selectUserAddressList(UserAddress userAddress);

    /**
     * 新增用户地址管理
     * 
     * @param userAddress 用户地址管理
     * @return 结果
     */
    public int insertUserAddress(UserAddress userAddress);

    /**
     * 修改用户地址管理
     * 
     * @param userAddress 用户地址管理
     * @return 结果
     */
    public int updateUserAddress(UserAddress userAddress);

    /**
     * 删除用户地址管理
     * 
     * @param id 用户地址管理主键
     * @return 结果
     */
    public int deleteUserAddressById(String id);

    /**
     * 批量删除用户地址管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserAddressByIds(String[] ids);
}
