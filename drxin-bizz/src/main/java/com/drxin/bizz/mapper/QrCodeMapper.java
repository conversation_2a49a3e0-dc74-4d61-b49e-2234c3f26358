package com.drxin.bizz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import com.drxin.bizz.domain.QrCode;

/**
 * 二维码信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface QrCodeMapper extends BaseMapper<QrCode> {
    /**
     * 查询二维码信息
     * 
     * @param id 二维码信息主键
     * @return 二维码信息
     */
    public QrCode selectQrCodeById(Long id);

    /**
     * 查询二维码信息列表
     * 
     * @param qrCode 二维码信息
     * @return 二维码信息集合
     */
    public List<QrCode> selectQrCodeList(QrCode qrCode);

    /**
     * 新增二维码信息
     * 
     * @param qrCode 二维码信息
     * @return 结果
     */
    public int insertQrCode(QrCode qrCode);

    /**
     * 修改二维码信息
     * 
     * @param qrCode 二维码信息
     * @return 结果
     */
    public int updateQrCode(QrCode qrCode);

    /**
     * 删除二维码信息
     * 
     * @param id 二维码信息主键
     * @return 结果
     */
    public int deleteQrCodeById(Long id);

    /**
     * 批量删除二维码信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteQrCodeByIds(Long[] ids);
}
