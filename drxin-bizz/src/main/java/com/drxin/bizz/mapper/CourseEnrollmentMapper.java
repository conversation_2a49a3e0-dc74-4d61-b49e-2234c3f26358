package com.drxin.bizz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import com.drxin.bizz.domain.CourseEnrollment;
import com.drxin.bizz.vo.CourseEnrollmentExportVo;
import com.drxin.bizz.vo.CourseEnrollmentVo;

/**
 * 课程报名记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface CourseEnrollmentMapper extends BaseMapper<CourseEnrollment> {
    /**
     * 查询课程报名记录
     * 
     * @param id 课程报名记录主键
     * @return 课程报名记录
     */
    public CourseEnrollment selectCourseEnrollmentById(Long id);

    /**
     * 查询课程报名记录列表
     * 
     * @param courseEnrollment 课程报名记录
     * @return 课程报名记录集合
     */
    public List<CourseEnrollmentVo> selectCourseEnrollmentList(CourseEnrollmentVo courseEnrollment);

    /**
     * 查询课程报名记录导出列表
     *
     * @param courseEnrollment 课程报名记录
     * @return 课程报名记录集合
     */
    public List<CourseEnrollmentExportVo> selectCourseEnrollmentExportList(CourseEnrollmentVo courseEnrollment);

    /**
     * 新增课程报名记录
     * 
     * @param courseEnrollment 课程报名记录
     * @return 结果
     */
    public int insertCourseEnrollment(CourseEnrollment courseEnrollment);

    /**
     * 修改课程报名记录
     * 
     * @param courseEnrollment 课程报名记录
     * @return 结果
     */
    public int updateCourseEnrollment(CourseEnrollment courseEnrollment);

    /**
     * 删除课程报名记录
     * 
     * @param id 课程报名记录主键
     * @return 结果
     */
    public int deleteCourseEnrollmentById(Long id);

    /**
     * 批量删除课程报名记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCourseEnrollmentByIds(Long[] ids);
}
