package com.drxin.bizz.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.drxin.common.annotation.Excel;
import com.drxin.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
/**
 * 贡献值配置对象 contribution_action_config
 * 
 * <AUTHOR>
 * @date 2025-06-29
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ContributionActionConfig extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 行为编码 */
    @Excel(name = "行为编码")
    private String code;

    /** 行为名称 */
    @Excel(name = "行为名称")
    private String name;

    /** 基础分值 */
    @Excel(name = "基础分值")
    private BigDecimal score;

    /** 是否均分 */
    @Excel(name = "是否均分")
    private String averageFlag;

    /** 是否启用 */
    @Excel(name = "是否启用")
    private String enabled;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("code", getCode())
            .append("name", getName())
            .append("score", getScore())
            .append("averageFlag", getAverageFlag())
            .append("remark", getRemark())
            .append("enabled", getEnabled())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
