package com.drxin.bizz.domain;

import java.util.Date;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.drxin.common.annotation.Excel;
import com.drxin.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
/**
 * 急救案例对象 rescue_case
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class RescueCase extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 急救日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "急救日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rescueDate;

    /** 急救所在城市 */
    @Excel(name = "急救所在城市")
    private String city;

    /** 详细地址 */
    @Excel(name = "详细地址")
    private String address;

    /** 被救人姓名 */
    @Excel(name = "被救人姓名")
    private String patientName;

    /** 被救人性别 */
    @Excel(name = "被救人性别")
    private String patientGender;

    /** 被救人年龄 */
    @Excel(name = "被救人年龄")
    private String patientAge;

    /** 病症类型 */
    @Excel(name = "病症类型")
    private String illnessType;

    /** 急救情况说明 */
    @Excel(name = "急救情况说明")
    private String rescueDescription;

    /** 急救案例状态 */
    @Excel(name = "急救案例状态")
    private String rescueStatus;

    /** 在线标识 */
    @Excel(name = "在线标识")
    private String onlineFlag;

    /** 远程指导员用户ID */
    @Excel(name = "远程指导员用户ID")
    private Long remoteGuideUserId;

    /** 远程指导员真实姓名 */
    @Excel(name = "远程指导员真实姓名")
    private String remoteGuideRealName;

    /** 急救员列表 */
    @TableField(exist = false)
    private List<RescueCaseRescuer> rescuers;

    /** 附件列表 */
    @TableField(exist = false)
    private List<RescueCaseAttachment> attachments;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("rescueDate", getRescueDate())
            .append("city", getCity())
            .append("address", getAddress())
            .append("patientName", getPatientName())
            .append("patientGender", getPatientGender())
            .append("patientAge", getPatientAge())
            .append("illnessType", getIllnessType())
            .append("rescueDescription", getRescueDescription())
            .append("rescueStatus", getRescueStatus())
            .append("onlineFlag", getOnlineFlag())
            .append("remoteGuideUserId", getRemoteGuideUserId())
            .append("remoteGuideRealName", getRemoteGuideRealName())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
