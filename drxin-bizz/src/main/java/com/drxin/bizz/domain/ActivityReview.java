package com.drxin.bizz.domain;

import java.util.Date;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.drxin.common.annotation.Excel;
import com.drxin.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 活动评审对象 activity_review
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("activity_review")
public class ActivityReview extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId
    private Long id;

    /** 活动主题 */
    @Excel(name = "活动主题")
    private String activityTitle;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /** 讲师姓名 */
    @Excel(name = "讲师姓名")
    private String lecturerName;

    /** 讲师电话 */
    @Excel(name = "讲师电话")
    private String lecturerPhone;

    /** 讲师 */
    @Excel(name = "讲师")
    private String lecturer;

    /** 参与人数 */
    @Excel(name = "参与人数")
    private Integer attendeeCount;

    /** 活动描述 */
    @Excel(name = "活动描述")
    private String activityDescription;

    /** 审核状态 1-新建 2-待审核 3-审核通过 4-审核拒绝 */
    @Excel(name = "审核状态")
    private String reviewStatus;

    /** 附件列表 */
    @TableField(exist = false)
    private List<ActivityReviewAttachment> attachments;

    /** 主办人列表 */
    @TableField(exist = false)
    private List<ActivityReviewUser> organizers;

    /** 协办人列表 */
    @TableField(exist = false)
    private List<ActivityReviewUser> assistants;
}
