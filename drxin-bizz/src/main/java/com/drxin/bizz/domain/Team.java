package com.drxin.bizz.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.drxin.common.annotation.Excel;
import com.drxin.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
/**
 * 团队管理对象 team
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "team")
public class Team extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** 团队ID，自增主键 */
    private Long teamId;

    /** 团队名称（唯一） */
    @Excel(name = "团队名称", readConverterExp = "唯=一")
    private String teamName;

    /** 团队长用户ID */
    @Excel(name = "团队长用户ID")
    private String leaderId;

    /** 团队长姓名 */
    @Excel(name = "团队长姓名")
    private String leaderName;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("teamId", getTeamId())
            .append("teamName", getTeamName())
            .append("leaderId", getLeaderId())
            .append("leaderName", getLeaderName())
            .append("createTime", getCreateTime())
            .toString();
    }
}
