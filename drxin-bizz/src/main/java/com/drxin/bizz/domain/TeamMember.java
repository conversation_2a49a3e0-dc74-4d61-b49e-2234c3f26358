package com.drxin.bizz.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.drxin.common.annotation.Excel;
import com.drxin.common.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 团队成员对象 team_member
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("team_member")
public class TeamMember extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 成员ID（即 user_id） */
    @TableId
    @Excel(name = "成员ID")
    private String memberId;

    /** 团队ID（真实团队ID 或 虚拟团队ID） */
    @Excel(name = "团队ID")
    private String teamId;

    /** 成员姓名 */
    @Excel(name = "成员姓名")
    private String memberName;

    /** 成员身份类型（急救员/导师/弟子） */
    @Excel(name = "成员身份类型", readConverterExp = "急救员=急救员,导师=导师,弟子=弟子")
    private String memberType;

    /** 身份升级时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "身份升级时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date upgradedTime;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("memberId", getMemberId())
            .append("teamId", getTeamId())
            .append("memberName", getMemberName())
            .append("memberType", getMemberType())
            .append("upgradedTime", getUpgradedTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
