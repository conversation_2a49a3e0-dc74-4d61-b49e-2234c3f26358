package com.drxin.bizz.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.drxin.common.annotation.Excel;
import com.drxin.common.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
/**
 * 物料领取记录对象 material_receive_log
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("material_receive_log")
public class MaterialReceiveLog extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** 领取记录ID */
    @TableId(type = IdType.AUTO)
    private Long recordId;

    /** 领取用户ID */
    @TableField("user_id")
    private Long userId;

    /** 用户姓名（冗余字段） */
    @Excel(name = "用户姓名", readConverterExp = "冗=余字段")
    @TableField("user_name")
    private String userName;

    /** 物料ID */
    @TableField("material_id")
    private Long materialId;

    /** 物料名称（冗余字段） */
    @Excel(name = "物料名称", readConverterExp = "冗=余字段")
    @TableField("material_name")
    private String materialName;

    /** 领取数量，默认1 */
    @Excel(name = "领取数量，默认1")
    @TableField("quantity")
    private Long quantity;

    /** 领取状态：pending=待领取，applying=申请中，completed=已领取 */
    @Excel(name = "领取状态：pending=待领取，applying=申请中，completed=已领取")
    @TableField("status")
    private String status;

    /** 记录创建方式：auto=系统自动生成，manual=手动创建 */
    @Excel(name = "记录创建方式：auto=系统自动生成，manual=手动创建")
    @TableField("trigger_type")
    private String triggerType;

    /** 用户点击确认申请的时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "用户点击确认申请的时间", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField("apply_time")
    private Date applyTime;

    /** 管理员确认发放时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "管理员确认发放时间", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField("confirm_time")
    private Date confirmTime;

    /** 发放操作管理员ID */
    @TableField("handler_id")
    private Long handlerId;

    /** 操作管理员姓名（冗余字段） */
    @TableField("handler_name")
    private String handlerName;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("recordId", getRecordId())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("materialId", getMaterialId())
            .append("materialName", getMaterialName())
            .append("quantity", getQuantity())
            .append("status", getStatus())
            .append("triggerType", getTriggerType())
            .append("applyTime", getApplyTime())
            .append("confirmTime", getConfirmTime())
            .append("handlerId", getHandlerId())
            .append("handlerName", getHandlerName())
            .append("remark", getRemark())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
