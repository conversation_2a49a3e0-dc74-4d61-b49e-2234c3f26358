package com.drxin.bizz.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.drxin.common.annotation.Excel;
import com.drxin.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
/**
 * 课程信息管理对象 course
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class Course extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 课程名称 */
    @Excel(name = "课程名称")
    private String courseName;

    /** 课程描述 */
    private String description;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 上课地址 */
    @Excel(name = "上课地址")
    private String location;

    /** 最大报名人数 */
    @Excel(name = "最大报名人数")
    private Long maxQuota;

    /** 当前报名人数 */
    @Excel(name = "当前报名人数")
    private Long currentQuota;

    /** 报名截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "报名截止时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date enrollDeadline;

    /** 课程信息url */
    private String infoQrUrl;

    /** 签到二维码URL */
    private String checkinQrUrl;

    /** 课程状态 */
    @Excel(name = "课程状态")
    private String courseStatus;

    /** 课程价格 */
    @Excel(name = "课程价格")
    private BigDecimal coursePrice;

    /** 课程类型 */
    private String courseType;
    /** 允许报名的角色 */
    private String allowedRoles;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("courseName", getCourseName())
            .append("description", getDescription())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("location", getLocation())
            .append("maxQuota", getMaxQuota())
            .append("currentQuota", getCurrentQuota())
            .append("enrollDeadline", getEnrollDeadline())
            .append("infoQrUrl", getInfoQrUrl())
            .append("checkinQrUrl", getCheckinQrUrl())
            .append("courseStatus", getCourseStatus())
            .append("coursePrice", getCoursePrice())
            .append("courseType", getCourseType())
            .toString();
    }
}
