package com.drxin.bizz.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.drxin.common.annotation.Excel;
import com.drxin.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
/**
 * 二维码信息对象 qr_code
 * 
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class QrCode extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 二维码内容 */
    @Excel(name = "二维码内容")
    private String content;

    /** 二维码用途说明 */
    @Excel(name = "二维码用途说明")
    private String description;

    /** 二维码logo文件名 */
    @Excel(name = "二维码logo文件名")
    private String logoFilename;

    /** 二维码base64编码数据 */
    @Excel(name = "二维码base64编码数据")
    private String qrCodeBase64;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }

    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }

    public void setLogoFilename(String logoFilename) 
    {
        this.logoFilename = logoFilename;
    }

    public String getLogoFilename() 
    {
        return logoFilename;
    }

    public void setQrCodeBase64(String qrCodeBase64) 
    {
        this.qrCodeBase64 = qrCodeBase64;
    }

    public String getQrCodeBase64() 
    {
        return qrCodeBase64;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("content", getContent())
            .append("description", getDescription())
            .append("logoFilename", getLogoFilename())
            .append("qrCodeBase64", getQrCodeBase64())
            .append("createBy", getCreateBy())
            .toString();
    }
}
