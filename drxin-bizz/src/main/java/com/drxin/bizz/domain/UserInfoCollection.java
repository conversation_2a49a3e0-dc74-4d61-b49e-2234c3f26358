package com.drxin.bizz.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.drxin.common.annotation.Excel;
import com.drxin.common.core.domain.BaseEntity;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户信息收集对象 user_info_collection
 *
 * <AUTHOR>
 * @date 2025-05-18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("user_info_collection")
public class UserInfoCollection extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 姓名
     */
    @Excel(name = "名字")
    private String name;

    /**
     * 性别
     */
    @Excel(name = "性别", readConverterExp = "0=男,1=女,2=未知")
    private String sex;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    private String phone;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    private String idCard;

    /**
     * 邮寄地址
     */
    @Excel(name = "邮寄地址")
    private String address;

    /**
     * 同行人员
     */
    @Excel(name = "同行人员")
    private String companions;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("sex", getSex())
                .append("phone", getPhone())
                .append("idCard", getIdCard())
                .append("address", getAddress())
                .append("companions", getCompanions())
                .append("createTime", getCreateTime())
                .toString();
    }

}