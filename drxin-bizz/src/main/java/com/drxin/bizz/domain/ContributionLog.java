package com.drxin.bizz.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.drxin.common.annotation.Excel;
import com.drxin.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
/**
 * 贡献值日志对象 contribution_log
 * 
 * <AUTHOR>
 * @date 2025-06-29
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ContributionLog extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 用户id */
    private Long userId;

    /** 用户姓名 */
    @Excel(name = "用户姓名")
    private String userName;

    /** 行为编码 */
    @Excel(name = "行为编码")
    private String actionCode;

    /** 关联业务主键id */
    private Long actionRefId;

    /** 本次贡献值 */
    @Excel(name = "本次贡献值")
    private BigDecimal score;

    /** 描述 */
    @Excel(name = "描述")
    private String description;

    /** 来源类型 */
    @Excel(name = "来源类型")
    private String sourceType;

    /** 状态 */
    private String logStatus;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("actionCode", getActionCode())
            .append("actionRefId", getActionRefId())
            .append("score", getScore())
            .append("description", getDescription())
            .append("sourceType", getSourceType())
            .append("logStatus", getLogStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
