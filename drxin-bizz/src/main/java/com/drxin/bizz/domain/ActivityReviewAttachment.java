package com.drxin.bizz.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.drxin.common.annotation.Excel;
import com.drxin.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 活动评审附件对象 activity_review_attachments
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("activity_review_attachments")
public class ActivityReviewAttachment extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId
    private Long id;

    /** 关联活动ID */
    @Excel(name = "关联活动ID")
    private Long activityId;

    /** 附件编号 */
    @Excel(name = "附件编号")
    private String attachmentId;

    /** 附件URL */
    @Excel(name = "附件URL")
    private String attachmentUrl;

    /** 附件类型 */
    @Excel(name = "附件类型")
    private String attachmentType;
}
