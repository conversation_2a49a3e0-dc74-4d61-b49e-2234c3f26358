package com.drxin.bizz.domain;

import java.util.List;

/**
 * 审核结果对象
 * 
 * 用于封装批量审核操作的结果信息，包含成功/失败统计和详细信息
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
public class AuditResult {
    
    /**
     * 成功处理的数量
     */
    private final int successCount;
    
    /**
     * 失败处理的数量
     */
    private final int failCount;
    
    /**
     * 成功处理的申请ID列表
     */
    private final List<Long> successIds;
    
    /**
     * 失败处理的申请ID列表
     */
    private final List<Long> failIds;
    
    /**
     * 错误信息汇总
     */
    private final String errorMessage;

    /**
     * 构造函数
     *
     * @param successCount 成功数量
     * @param failCount 失败数量
     * @param successIds 成功的申请ID列表
     * @param failIds 失败的申请ID列表
     * @param errorMessage 错误信息汇总
     */
    public AuditResult(int successCount, int failCount, List<Long> successIds, List<Long> failIds, String errorMessage) {
        this.successCount = successCount;
        this.failCount = failCount;
        this.successIds = successIds;
        this.failIds = failIds;
        this.errorMessage = errorMessage;
    }

    /**
     * 获取成功处理的数量
     *
     * @return 成功数量
     */
    public int getSuccessCount() {
        return successCount;
    }

    /**
     * 获取失败处理的数量
     *
     * @return 失败数量
     */
    public int getFailCount() {
        return failCount;
    }

    /**
     * 获取成功处理的申请ID列表
     *
     * @return 成功的申请ID列表
     */
    public List<Long> getSuccessIds() {
        return successIds;
    }

    /**
     * 获取失败处理的申请ID列表
     *
     * @return 失败的申请ID列表
     */
    public List<Long> getFailIds() {
        return failIds;
    }

    /**
     * 获取错误信息汇总
     *
     * @return 错误信息
     */
    public String getErrorMessage() {
        return errorMessage;
    }

    /**
     * 获取总处理数量
     *
     * @return 总数量（成功数量 + 失败数量）
     */
    public int getTotalCount() {
        return successCount + failCount;
    }

    /**
     * 判断是否全部成功
     *
     * @return true-全部成功，false-有失败的
     */
    public boolean isAllSuccess() {
        return failCount == 0;
    }

    /**
     * 判断是否部分成功
     *
     * @return true-部分成功（既有成功也有失败），false-全部成功或全部失败
     */
    public boolean hasPartialSuccess() {
        return successCount > 0 && failCount > 0;
    }

    /**
     * 判断是否全部失败
     *
     * @return true-全部失败，false-有成功的
     */
    public boolean isAllFailed() {
        return successCount == 0;
    }

    /**
     * 获取成功率
     *
     * @return 成功率（0.0-1.0）
     */
    public double getSuccessRate() {
        int total = getTotalCount();
        return total == 0 ? 0.0 : (double) successCount / total;
    }

    /**
     * 获取成功率百分比
     *
     * @return 成功率百分比（0-100）
     */
    public double getSuccessPercentage() {
        return getSuccessRate() * 100;
    }

    @Override
    public String toString() {
        return String.format("AuditResult{总数=%d, 成功=%d, 失败=%d, 成功率=%.1f%%}", 
            getTotalCount(), successCount, failCount, getSuccessPercentage());
    }
}
