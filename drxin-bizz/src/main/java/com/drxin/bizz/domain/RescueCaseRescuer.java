package com.drxin.bizz.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.drxin.common.annotation.Excel;
import com.drxin.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
/**
 * 急救案例急救成员对象 rescue_case_rescuer
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class RescueCaseRescuer extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 急救案例ID */
    private Long caseId;

    /** 急救员id */
    private Long rescuerId;

    /** 急救员姓名 */
    @Excel(name = "急救员姓名")
    private String rescuerName;

    /** 急救员手机号 */
    @Excel(name = "急救员手机号")
    private String rescuerPhone;

    /** 分配到的贡献值 */
    @Excel(name = "分配到的贡献值")
    private String score;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("caseId", getCaseId())
            .append("rescuerId", getRescuerId())
            .append("rescuerName", getRescuerName())
            .append("rescuerPhone", getRescuerPhone())
            .append("score", getScore())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
