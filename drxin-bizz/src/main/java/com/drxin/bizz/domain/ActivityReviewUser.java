package com.drxin.bizz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.drxin.common.annotation.Excel;
import com.drxin.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 活动评审人员关联对象 activity_review_user
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("activity_review_user")
public class ActivityReviewUser extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId
    private Long id;

    /** 关联活动ID */
    @Excel(name = "关联活动ID")
    private Long activityId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 角色类型 0-主办人 1-协办人 */
    @Excel(name = "角色类型")
    private Integer roleType;

    /** 用户名 */
    @TableField(exist = false)
    @Excel(name = "用户名")
    private String userName;
}
