package com.drxin.bizz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.drxin.common.annotation.Excel;
import com.drxin.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 贡献值事件日志对象 contribution_event_log
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("contribution_event_log")
public class ContributionEventLog extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 行为编码 */
    @Excel(name = "行为编码")
    private String actionCode;

    /** 事件来源类 */
    @Excel(name = "事件来源类")
    private String eventSource;

    /** 事件上下文数据(JSON格式) */
    @Excel(name = "事件上下文数据")
    private String contextData;

    /** 处理状态(0-待处理 1-处理成功 2-处理失败) */
    @Excel(name = "处理状态", readConverterExp = "0=待处理,1=处理成功,2=处理失败")
    private String processStatus;

    /** 处理结果消息 */
    @Excel(name = "处理结果消息")
    private String processMessage;

    /** 处理时间 */
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date processTime;

    // 处理状态常量
    public static final String STATUS_PENDING = "0";    // 待处理
    public static final String STATUS_SUCCESS = "1";    // 处理成功
    public static final String STATUS_FAILED = "2";     // 处理失败

    // 行为编码常量
    public static final String ACTION_COURSE_CHECKIN = "COURSE_CHECKIN";
    public static final String ACTION_ACTIVITY_ORGANIZER = "ACTIVITY_ORGANIZER";
    public static final String ACTION_RESCUE_CASE = "RESCUE_CASE";
    public static final String ACTION_TEAM_MEMBER_AUTO_ADD = "TEAM_MEMBER_AUTO_ADD";
}
