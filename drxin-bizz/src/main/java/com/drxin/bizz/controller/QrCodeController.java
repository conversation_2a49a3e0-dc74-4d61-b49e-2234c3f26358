package com.drxin.bizz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.drxin.common.annotation.Log;
import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.common.enums.BusinessType;
import com.drxin.bizz.domain.QrCode;
import com.drxin.bizz.service.IQrCodeService;
import com.drxin.common.utils.poi.ExcelUtil;
import com.drxin.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 二维码信息Controller
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@RestController
@RequestMapping("/bizz/qr_code")
public class QrCodeController extends BaseController {
    @Autowired
    private IQrCodeService qrCodeService;

    /**
     * 查询二维码信息列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:qr_code:list')")
    @GetMapping("/list")
    public TableDataInfo list(QrCode qrCode) {
        startPage();
        List<QrCode> list = qrCodeService.selectQrCodeList(qrCode);
        return getDataTable(list);
    }

    /**
     * 导出二维码信息列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:qr_code:export')")
    @Log(title = "二维码信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, QrCode qrCode) {
        List<QrCode> list = qrCodeService.selectQrCodeList(qrCode);
        ExcelUtil<QrCode> util = new ExcelUtil<QrCode>(QrCode.class);
        util.exportExcel(response, list, "二维码信息数据");
    }

    /**
     * 获取二维码信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('bizz:qr_code:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(qrCodeService.selectQrCodeById(id));
    }

    /**
     * 新增二维码信息
     */
    @PreAuthorize("@ss.hasPermi('bizz:qr_code:add')")
    @Log(title = "二维码信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody QrCode qrCode) {
        return toAjax(qrCodeService.insertQrCode(qrCode, null));
    }

    /**
     * 修改二维码信息
     */
    @PreAuthorize("@ss.hasPermi('bizz:qr_code:edit')")
    @Log(title = "二维码信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody QrCode qrCode) {
        return toAjax(qrCodeService.updateQrCode(qrCode));
    }

    /**
     * 删除二维码信息
     */
    @PreAuthorize("@ss.hasPermi('bizz:qr_code:remove')")
    @Log(title = "二维码信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(qrCodeService.deleteQrCodeByIds(ids));
    }
}
