package com.drxin.bizz.controller;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.drxin.bizz.domain.Course;
import com.drxin.common.core.domain.entity.SysRole;
import com.drxin.common.utils.StringUtils;
import com.drxin.system.service.ISysRoleService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.drxin.common.annotation.Log;
import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.common.enums.BusinessType;
import com.drxin.bizz.domain.MaterialInfo;
import com.drxin.bizz.service.IMaterialInfoService;
import com.drxin.common.utils.poi.ExcelUtil;
import com.drxin.common.core.page.TableDataInfo;

/**
 * 物料信息Controller
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
@RestController
@RequestMapping("/bizz/material_info")
public class MaterialInfoController extends BaseController {
    @Autowired
    private IMaterialInfoService materialInfoService;
    @Autowired
    private ISysRoleService roleService;
    /**
     * 查询物料信息列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:material_info:list')")
    @GetMapping("/list")
    public TableDataInfo list(MaterialInfo materialInfo){
        startPage();
        List<MaterialInfo> list = materialInfoService.selectMaterialInfoList(materialInfo);
        return getDataTable(list);
    }

    /**
     * 导出物料信息列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:material_info:export')")
    @Log(title = "物料信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MaterialInfo materialInfo) {
        List<MaterialInfo> list = materialInfoService.selectMaterialInfoList(materialInfo);
        ExcelUtil<MaterialInfo> util = new ExcelUtil<MaterialInfo>(MaterialInfo.class);
        util.exportExcel(response, list, "物料信息数据");
    }

    /**
     * 获取物料信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('bizz:material_info:query')")
    @GetMapping(value = {"/","/{materialId}"})
    public AjaxResult getInfo(@PathVariable(name = "materialId", required = false) Long materialId) {
        AjaxResult ajax = AjaxResult.success();
        if (StringUtils.isNotNull(materialId)) {
            MaterialInfo materialInfo = materialInfoService.selectMaterialInfoByMaterialId(materialId);
            ajax.put(AjaxResult.DATA_TAG, materialInfo);
        }
        return ajax;
    }

    /**
     * 新增物料信息
     */
    @PreAuthorize("@ss.hasPermi('bizz:material_info:add')")
    @Log(title = "物料信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MaterialInfo materialInfo) {
        return toAjax(materialInfoService.insertMaterialInfo(materialInfo));
    }

    /**
     * 修改物料信息
     */
    @PreAuthorize("@ss.hasPermi('bizz:material_info:edit')")
    @Log(title = "物料信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MaterialInfo materialInfo) {
        return toAjax(materialInfoService.updateMaterialInfo(materialInfo));
    }

    /**
     * 删除物料信息
     */
    @PreAuthorize("@ss.hasPermi('bizz:material_info:remove')")
    @Log(title = "物料信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{materialIds}")
    public AjaxResult remove(@PathVariable Long[] materialIds) {
        return toAjax(materialInfoService.deleteMaterialInfoByMaterialIds(materialIds));
    }
}
