package com.drxin.bizz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.drxin.common.annotation.Log;
import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.common.enums.BusinessType;
import com.drxin.bizz.domain.UserAddress;
import com.drxin.bizz.service.IUserAddressService;
import com.drxin.common.utils.poi.ExcelUtil;
import com.drxin.common.core.page.TableDataInfo;

/**
 * 用户地址管理Controller
 * 
 * <AUTHOR>
 * @date 2025-06-02
 */
@RestController
@RequestMapping("/bizz/user_address")
public class UserAddressController extends BaseController {
    @Autowired
    private IUserAddressService userAddressService;

    /**
     * 查询用户地址管理列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:user_address:list')")
    @GetMapping("/list")
    public TableDataInfo list(UserAddress userAddress){
        startPage();
        List<UserAddress> list = userAddressService.selectUserAddressList(userAddress);
        return getDataTable(list);
    }

    /**
     * 导出用户地址管理列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:user_address:export')")
    @Log(title = "用户地址管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserAddress userAddress) {
        List<UserAddress> list = userAddressService.selectUserAddressList(userAddress);
        ExcelUtil<UserAddress> util = new ExcelUtil<UserAddress>(UserAddress.class);
        util.exportExcel(response, list, "用户地址管理数据");
    }

    /**
     * 获取用户地址管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('bizz:user_address:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(userAddressService.selectUserAddressById(id));
    }

    /**
     * 新增用户地址管理
     */
    @PreAuthorize("@ss.hasPermi('bizz:user_address:add')")
    @Log(title = "用户地址管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserAddress userAddress) {
        return toAjax(userAddressService.insertUserAddress(userAddress));
    }

    /**
     * 修改用户地址管理
     */
    @PreAuthorize("@ss.hasPermi('bizz:user_address:edit')")
    @Log(title = "用户地址管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserAddress userAddress) {
        return toAjax(userAddressService.updateUserAddress(userAddress));
    }

    /**
     * 删除用户地址管理
     */
    @PreAuthorize("@ss.hasPermi('bizz:user_address:remove')")
    @Log(title = "用户地址管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(userAddressService.deleteUserAddressByIds(ids));
    }
}
