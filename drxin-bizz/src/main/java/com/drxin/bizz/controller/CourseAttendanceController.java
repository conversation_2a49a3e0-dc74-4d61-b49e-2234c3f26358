package com.drxin.bizz.controller;

import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import cn.idev.excel.FastExcel;
import com.drxin.bizz.vo.CourseAttendExportVo;
import com.drxin.bizz.vo.CourseAttendanceVo;
import com.drxin.common.exception.ServiceException;
import com.drxin.common.utils.ExportUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.drxin.common.annotation.Log;
import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.common.enums.BusinessType;
import com.drxin.bizz.domain.CourseAttendance;
import com.drxin.bizz.service.ICourseAttendanceService;
import com.drxin.common.utils.poi.ExcelUtil;
import com.drxin.common.core.page.TableDataInfo;

/**
 * 课程签到记录Controller
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@RestController
@RequestMapping("/bizz/course_attendance")
public class CourseAttendanceController extends BaseController {
    @Autowired
    private ICourseAttendanceService courseAttendanceService;

    /**
     * 查询课程签到记录列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:course_attendance:list')")
    @GetMapping("/list")
    public TableDataInfo list(CourseAttendanceVo courseAttendanceVo){
        startPage();
        List<CourseAttendanceVo> list = courseAttendanceService.selectCourseAttendanceList(courseAttendanceVo);
        return getDataTable(list);
    }

    /**
     * 导出课程签到记录列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:course_attendance:export')")
    @Log(title = "课程签到记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CourseAttendanceVo courseAttendanceVo) throws IOException {
        List<CourseAttendExportVo> list = courseAttendanceService.selectCourseAttendanceExportList(courseAttendanceVo);
        if (list == null || list.isEmpty()) {
            throw new ServiceException("没有可导出的数据");
        }
        FastExcel.write(response.getOutputStream(), CourseAttendExportVo.class)
                .sheet("课程签到记录")
                .doWrite(list);
    }

    /**
     * 获取课程签到记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('bizz:course_attendance:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(courseAttendanceService.selectCourseAttendanceById(id));
    }

    /**
     * 新增课程签到记录
     */
    @PreAuthorize("@ss.hasPermi('bizz:course_attendance:add')")
    @Log(title = "课程签到记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CourseAttendance courseAttendance) {
        return toAjax(courseAttendanceService.insertCourseAttendance(courseAttendance));
    }

    /**
     * 修改课程签到记录
     */
    @PreAuthorize("@ss.hasPermi('bizz:course_attendance:edit')")
    @Log(title = "课程签到记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CourseAttendance courseAttendance) {
        return toAjax(courseAttendanceService.updateCourseAttendance(courseAttendance));
    }

    /**
     * 删除课程签到记录
     */
    @PreAuthorize("@ss.hasPermi('bizz:course_attendance:remove')")
    @Log(title = "课程签到记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(courseAttendanceService.deleteCourseAttendanceByIds(ids));
    }
}
