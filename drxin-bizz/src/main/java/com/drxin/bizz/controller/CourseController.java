package com.drxin.bizz.controller;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.drxin.common.core.domain.entity.SysRole;
import com.drxin.common.core.domain.entity.SysUser;
import com.drxin.common.utils.StringUtils;
import com.drxin.system.service.ISysRoleService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.drxin.common.annotation.Log;
import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.common.enums.BusinessType;
import com.drxin.bizz.domain.Course;
import com.drxin.bizz.service.ICourseService;
import com.drxin.common.utils.poi.ExcelUtil;
import com.drxin.common.core.page.TableDataInfo;

/**
 * 课程信息管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@RestController
@RequestMapping("/bizz/course")
public class CourseController extends BaseController {
    @Autowired
    private ICourseService courseService;

    @Autowired
    private ISysRoleService roleService;

    /**
     * 查询课程信息管理列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:course:list')")
    @GetMapping("/list")
    public TableDataInfo list(Course course) {
        startPage();
        List<Course> list = courseService.selectCourseList(course);
        return getDataTable(list);
    }

    /**
     * 导出课程信息管理列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:course:export')")
    @Log(title = "课程信息管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Course course) {
        List<Course> list = courseService.selectCourseList(course);
        ExcelUtil<Course> util = new ExcelUtil<Course>(Course.class);
        util.exportExcel(response, list, "课程信息管理数据");
    }

    /**
     * 获取课程信息管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('bizz:course:query')")
    @GetMapping(value = {"/", "/{id}"})
    public AjaxResult getInfo(@PathVariable(name = "id", required = false) Long id) {
        AjaxResult ajax = AjaxResult.success();
        if (StringUtils.isNotNull(id)) {
            Course course = courseService.selectCourseById(id);
            String allowedRoles = course.getAllowedRoles();
            ajax.put(AjaxResult.DATA_TAG, course);
            if (StringUtils.isNotEmpty(allowedRoles)) {
                ajax.put("allowedRoleIds", Arrays.stream(allowedRoles.split(",")).map(Integer::parseInt).collect(Collectors.toList()));
            } else {
                ajax.put("allowedRoleIds", null);
            }
        }
        List<SysRole> roles = roleService.selectRoleAll();
        ajax.put("roles", roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        return ajax;
    }

    /**
     * 新增课程信息管理
     */
    @PreAuthorize("@ss.hasPermi('bizz:course:add')")
    @Log(title = "课程信息管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Course course) {
        return toAjax(courseService.insertCourse(course));
    }

    /**
     * 修改课程信息管理
     */
    @PreAuthorize("@ss.hasPermi('bizz:course:edit')")
    @Log(title = "课程信息管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Course course) {
        return toAjax(courseService.updateCourse(course));
    }

    /**
     * 删除课程信息管理
     */
    @PreAuthorize("@ss.hasPermi('bizz:course:remove')")
    @Log(title = "课程信息管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(courseService.deleteCourseByIds(ids));
    }
}
