package com.drxin.bizz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.drxin.common.annotation.Log;
import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.common.enums.BusinessType;
import com.drxin.bizz.domain.ContributionLog;
import com.drxin.bizz.service.IContributionLogService;
import com.drxin.common.utils.poi.ExcelUtil;
import com.drxin.common.core.page.TableDataInfo;

/**
 * 贡献值日志Controller
 * 
 * <AUTHOR>
 * @date 2025-06-29
 */
@RestController
@RequestMapping("/bizz/contribution_log")
public class ContributionLogController extends BaseController {
    @Autowired
    private IContributionLogService contributionLogService;

    /**
     * 查询贡献值日志列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:contribution_log:list')")
    @GetMapping("/list")
    public TableDataInfo list(ContributionLog contributionLog){
        startPage();
        List<ContributionLog> list = contributionLogService.selectContributionLogList(contributionLog);
        return getDataTable(list);
    }

    /**
     * 导出贡献值日志列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:contribution_log:export')")
    @Log(title = "贡献值日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ContributionLog contributionLog) {
        List<ContributionLog> list = contributionLogService.selectContributionLogList(contributionLog);
        ExcelUtil<ContributionLog> util = new ExcelUtil<ContributionLog>(ContributionLog.class);
        util.exportExcel(response, list, "贡献值日志数据");
    }

    /**
     * 获取贡献值日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('bizz:contribution_log:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(contributionLogService.selectContributionLogById(id));
    }

    /**
     * 新增贡献值日志
     */
    @PreAuthorize("@ss.hasPermi('bizz:contribution_log:add')")
    @Log(title = "贡献值日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ContributionLog contributionLog) {
        return toAjax(contributionLogService.insertContributionLog(contributionLog));
    }

    /**
     * 修改贡献值日志
     */
    @PreAuthorize("@ss.hasPermi('bizz:contribution_log:edit')")
    @Log(title = "贡献值日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ContributionLog contributionLog) {
        return toAjax(contributionLogService.updateContributionLog(contributionLog));
    }

    /**
     * 删除贡献值日志
     */
    @PreAuthorize("@ss.hasPermi('bizz:contribution_log:remove')")
    @Log(title = "贡献值日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(contributionLogService.deleteContributionLogByIds(ids));
    }
}
