package com.drxin.bizz.controller;

import com.drxin.bizz.domain.ActivityReview;
import com.drxin.bizz.service.IActivityReviewService;
import com.drxin.bizz.vo.ActivityReviewVo;
import com.drxin.common.annotation.Log;
import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.common.core.page.TableDataInfo;
import com.drxin.common.enums.BusinessType;
import com.drxin.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 活动评审Controller
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/bizz/activity_review")
public class ActivityReviewController extends BaseController {
    
    @Autowired
    private IActivityReviewService activityReviewService;

    /**
     * 查询活动评审列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:activity_review:list')")
    @GetMapping("/list")
    public TableDataInfo list(ActivityReview activityReview) {
        startPage();
        List<ActivityReviewVo> list = activityReviewService.selectActivityReviewList(activityReview);
        return getDataTable(list);
    }

    /**
     * 导出活动评审列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:activity_review:export')")
    @Log(title = "活动评审", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ActivityReview activityReview) {
        List<ActivityReviewVo> list = activityReviewService.selectActivityReviewList(activityReview);
        ExcelUtil<ActivityReviewVo> util = new ExcelUtil<ActivityReviewVo>(ActivityReviewVo.class);
        util.exportExcel(response, list, "活动评审数据");
    }

    /**
     * 获取活动评审详细信息
     */
    @PreAuthorize("@ss.hasPermi('bizz:activity_review:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(activityReviewService.selectActivityReviewById(id));
    }

    /**
     * 新增活动评审
     */
    @PreAuthorize("@ss.hasPermi('bizz:activity_review:add')")
    @Log(title = "活动评审", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ActivityReview activityReview) {
        return toAjax(activityReviewService.insertActivityReview(activityReview));
    }

    /**
     * 修改活动评审
     */
    @PreAuthorize("@ss.hasPermi('bizz:activity_review:edit')")
    @Log(title = "活动评审", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ActivityReview activityReview) {
        return toAjax(activityReviewService.updateActivityReview(activityReview));
    }

    /**
     * 删除活动评审
     */
    @PreAuthorize("@ss.hasPermi('bizz:activity_review:remove')")
    @Log(title = "活动评审", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(activityReviewService.deleteActivityReviewByIds(ids));
    }

    /**
     * 批量审批活动评审
     */
    @PreAuthorize("@ss.hasPermi('bizz:activity_review:audit')")
    @Log(title = "活动评审审批", businessType = BusinessType.UPDATE)
    @GetMapping("/batchAudit/{ids}")
    public AjaxResult batchAudit(@PathVariable Long[] ids,
                                @RequestParam String status) {
        int result = activityReviewService.batchAuditActivityReview(ids, status);
        if (result > 0) {
            return AjaxResult.success("审批成功");
        }
        return AjaxResult.error("审批失败");
    }
}
