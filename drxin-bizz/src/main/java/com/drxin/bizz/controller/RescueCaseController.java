package com.drxin.bizz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.drxin.bizz.vo.RescueCaseVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.drxin.common.annotation.Log;
import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.common.enums.BusinessType;
import com.drxin.bizz.domain.RescueCase;
import com.drxin.bizz.service.IRescueCaseService;
import com.drxin.common.utils.poi.ExcelUtil;
import com.drxin.common.core.page.TableDataInfo;

/**
 * 急救案例Controller
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@RestController
@RequestMapping("/bizz/rescue_case")
public class RescueCaseController extends BaseController {
    @Autowired
    private IRescueCaseService rescueCaseService;

    /**
     * 查询急救案例列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:rescue_case:list')")
    @GetMapping("/list")
    public TableDataInfo list(RescueCase rescueCase){
        startPage();
        List<RescueCaseVo> list = rescueCaseService.selectRescueCaseList(rescueCase);
        return getDataTable(list);
    }

    /**
     * 导出急救案例列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:rescue_case:export')")
    @Log(title = "急救案例", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RescueCase rescueCase) {
//        List<RescueCase> list = rescueCaseService.selectRescueCaseList(rescueCase);
//        ExcelUtil<RescueCase> util = new ExcelUtil<RescueCase>(RescueCase.class);
//        util.exportExcel(response, list, "急救案例数据");
    }

    /**
     * 获取急救案例详细信息
     */
    @PreAuthorize("@ss.hasPermi('bizz:rescue_case:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(rescueCaseService.selectRescueCaseById(id));
    }

    /**
     * 新增急救案例
     */
    @PreAuthorize("@ss.hasPermi('bizz:rescue_case:add')")
    @Log(title = "急救案例", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RescueCase rescueCase) {
        return toAjax(rescueCaseService.insertRescueCase(rescueCase));
    }

    /**
     * 修改急救案例
     */
    @PreAuthorize("@ss.hasPermi('bizz:rescue_case:edit')")
    @Log(title = "急救案例", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RescueCase rescueCase) {
        return toAjax(rescueCaseService.updateRescueCase(rescueCase));
    }

    /**
     * 删除急救案例
     */
    @PreAuthorize("@ss.hasPermi('bizz:rescue_case:remove')")
    @Log(title = "急救案例", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(rescueCaseService.deleteRescueCaseByIds(ids));
    }

    /**
     * 批量审批急救案例
     */
    @PreAuthorize("@ss.hasPermi('bizz:rescue_case:audit')")
    @Log(title = "急救案例审批", businessType = BusinessType.UPDATE)
    @GetMapping("/batchAudit/{ids}")
    public AjaxResult batchAudit(@PathVariable Long[] ids,
                                @RequestParam String status) {
        int result = rescueCaseService.batchAuditRescueCase(ids, status);
        if (result > 0) {
            return AjaxResult.success("审批成功");
        }
        return AjaxResult.error("审批失败");
    }


}
