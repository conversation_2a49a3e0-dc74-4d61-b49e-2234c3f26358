package com.drxin.bizz.controller;

import com.drxin.bizz.domain.ContributionActionConfig;
import com.drxin.bizz.service.IContributionActionConfigService;
import com.drxin.common.annotation.Log;
import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.common.core.page.TableDataInfo;
import com.drxin.common.enums.BusinessType;
import com.drxin.common.utils.poi.ExcelUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 贡献值配置Controller
 * 
 * <AUTHOR>
 * @date 2025-06-29
 */
@RestController
@RequestMapping("/bizz/contribution_action_config")
public class ContributionActionConfigController extends BaseController {
    @Resource
    private IContributionActionConfigService contributionActionConfigService;

    /**
     * 查询贡献值配置列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:contribution_action_config:list')")
    @GetMapping("/list")
    public TableDataInfo list(ContributionActionConfig contributionActionConfig){
        startPage();
        List<ContributionActionConfig> list = contributionActionConfigService.selectContributionActionConfigList(contributionActionConfig);
        return getDataTable(list);
    }

    /**
     * 导出贡献值配置列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:contribution_action_config:export')")
    @Log(title = "贡献值配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ContributionActionConfig contributionActionConfig) {
        List<ContributionActionConfig> list = contributionActionConfigService.selectContributionActionConfigList(contributionActionConfig);
        ExcelUtil<ContributionActionConfig> util = new ExcelUtil<>(ContributionActionConfig.class);
        util.exportExcel(response, list, "贡献值配置数据");
    }

    /**
     * 获取贡献值配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('bizz:contribution_action_config:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(contributionActionConfigService.selectContributionActionConfigById(id));
    }

    /**
     * 新增贡献值配置
     */
    @PreAuthorize("@ss.hasPermi('bizz:contribution_action_config:add')")
    @Log(title = "贡献值配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ContributionActionConfig contributionActionConfig) {
        return toAjax(contributionActionConfigService.insertContributionActionConfig(contributionActionConfig));
    }

    /**
     * 修改贡献值配置
     */
    @PreAuthorize("@ss.hasPermi('bizz:contribution_action_config:edit')")
    @Log(title = "贡献值配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ContributionActionConfig contributionActionConfig) {
        return toAjax(contributionActionConfigService.updateContributionActionConfig(contributionActionConfig));
    }

    /**
     * 删除贡献值配置
     */
    @PreAuthorize("@ss.hasPermi('bizz:contribution_action_config:remove')")
    @Log(title = "贡献值配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(contributionActionConfigService.deleteContributionActionConfigByIds(ids));
    }
}
