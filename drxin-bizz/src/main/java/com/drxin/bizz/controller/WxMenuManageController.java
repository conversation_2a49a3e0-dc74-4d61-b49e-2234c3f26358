package com.drxin.bizz.controller;

import com.drxin.common.annotation.Log;
import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.common.core.page.TableDataInfo;
import com.drxin.common.enums.BusinessType;
import com.drxin.common.utils.poi.ExcelUtil;
import com.drxin.system.domain.WxMenu;
import com.drxin.system.service.IWxMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 微信小程序菜单管理Controller
 *
 * <AUTHOR>
 * @date 2024-07-14
 */
@RestController
@RequestMapping("/bizz/wx_menu")
public class WxMenuManageController extends BaseController {

    @Autowired
    private IWxMenuService wxMenuService;

    /**
     * 查询微信小程序菜单列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:wx_menu:list')")
    @GetMapping("/list")
    public TableDataInfo list(WxMenu wxMenu) {
        startPage();
        List<WxMenu> list = wxMenuService.selectWxMenuList(wxMenu);
        return getDataTable(list);
    }

    /**
     * 获取所有菜单（管理后台用）
     */
    @PreAuthorize("@ss.hasPermi('bizz:wx_menu:manage')")
    @GetMapping("/manage")
    public AjaxResult manage() {
        WxMenu wxMenu = new WxMenu();
        List<WxMenu> list = wxMenuService.selectWxMenuList(wxMenu);
        return AjaxResult.success(list);
    }

    /**
     * 导出微信小程序菜单列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:wx_menu:export')")
    @Log(title = "微信小程序菜单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WxMenu wxMenu) {
        List<WxMenu> list = wxMenuService.selectWxMenuList(wxMenu);
        ExcelUtil<WxMenu> util = new ExcelUtil<WxMenu>(WxMenu.class);
        util.exportExcel(response, list, "微信小程序菜单数据");
    }

    /**
     * 获取微信小程序菜单详细信息
     */
    @PreAuthorize("@ss.hasPermi('bizz:wx_menu:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(wxMenuService.selectWxMenuById(id));
    }

    /**
     * 新增微信小程序菜单
     */
    @PreAuthorize("@ss.hasPermi('bizz:wx_menu:add')")
    @Log(title = "微信小程序菜单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WxMenu wxMenu) {
        return toAjax(wxMenuService.insertWxMenu(wxMenu));
    }

    /**
     * 修改微信小程序菜单
     */
    @PreAuthorize("@ss.hasPermi('bizz:wx_menu:edit')")
    @Log(title = "微信小程序菜单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WxMenu wxMenu) {
        return toAjax(wxMenuService.updateWxMenu(wxMenu));
    }

    /**
     * 删除微信小程序菜单
     */
    @PreAuthorize("@ss.hasPermi('bizz:wx_menu:remove')")
    @Log(title = "微信小程序菜单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(wxMenuService.deleteWxMenuByIds(ids));
    }

    /**
     * 更新微信小程序菜单状态
     */
    @PreAuthorize("@ss.hasPermi('bizz:wx_menu:edit')")
    @Log(title = "微信小程序菜单状态", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/status")
    public AjaxResult updateMenuStatus(@PathVariable("id") Integer id, @RequestBody WxMenu wxMenu) {
        WxMenu menu = new WxMenu();
        menu.setId(id);
        menu.setStatus(wxMenu.getStatus());
        return toAjax(wxMenuService.updateWxMenu(menu));
    }

}
