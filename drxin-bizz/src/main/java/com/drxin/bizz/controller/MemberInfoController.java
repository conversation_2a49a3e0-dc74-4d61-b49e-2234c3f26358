package com.drxin.bizz.controller;

import cn.idev.excel.FastExcel;
import com.drxin.bizz.domain.MemberInfo;
import com.drxin.bizz.service.IMemberInfoService;
import com.drxin.bizz.vo.MemberUpdateTypeVo;
import com.drxin.bizz.vo.MemberAiderStatVo;
import com.drxin.bizz.vo.MemberAiderStatExportVo;
import com.drxin.common.annotation.Log;
import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.common.core.page.TableDataInfo;
import com.drxin.common.enums.BusinessType;
import com.drxin.common.exception.ServiceException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/bizz/member_info")
public class MemberInfoController extends BaseController {

    @Resource
    private IMemberInfoService memberInfoService;

    @GetMapping("/list")
    public TableDataInfo list(MemberInfo memberInfo) {
        startPage();
        List<MemberInfo> memberInfos = memberInfoService.selectMemberInfoList(memberInfo);
        return getDataTable(memberInfos);
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(memberInfoService.selectMemberInfoById(id));
    }

    @PutMapping("/updateMemberType")
    public AjaxResult updateMemberType(@RequestBody MemberUpdateTypeVo memberUpdateTypeVo) {
        return toAjax(memberInfoService.updateMemberType(memberUpdateTypeVo));
    }

    @GetMapping("/selectMemberList")
    public TableDataInfo selectMemberInfoListToSelect(MemberUpdateTypeVo memberUpdateTypeVo) {
        startPage();
        List<MemberInfo> memberInfos = memberInfoService.selectMemberInfoListToSelect(memberUpdateTypeVo);
        return getDataTable(memberInfos);
    }

    @PutMapping("/updateMemberInviter/{ids}/{newInviterId}")
    public AjaxResult updateMemberInviter(@PathVariable("ids") String ids, @PathVariable("newInviterId") String newInviterId) {
        return toAjax(memberInfoService.updateMemberInviter(ids, newInviterId));
    }

    @GetMapping("/getRecommendList")
    public AjaxResult getRecommendList(MemberInfo memberInfo) {
        startPage();
        return AjaxResult.success(getDataTable(memberInfoService.getRecommendList(memberInfo)));
    }

    @GetMapping("/aiderStat")
    public TableDataInfo getAiderStatList(MemberAiderStatVo memberAiderStatVo) {
        startPage();
        List<MemberAiderStatVo> list = memberInfoService.selectMemberAiderStatList(memberAiderStatVo);
        return getDataTable(list);
    }

    /**
     * 导出急救员统计列表
     */
    @PostMapping("/aiderStat/export")
    public void exportAiderStat(HttpServletResponse response, MemberAiderStatVo memberAiderStatVo) throws Exception {
        List<MemberAiderStatExportVo> list = memberInfoService.selectMemberAiderStatExportList(memberAiderStatVo);
        if (list == null || list.isEmpty()) {
            throw new ServiceException("没有可导出的数据");
        }
        FastExcel.write(response.getOutputStream(), MemberAiderStatExportVo.class)
                .sheet("急救员统计")
                .doWrite(list);
    }
}
