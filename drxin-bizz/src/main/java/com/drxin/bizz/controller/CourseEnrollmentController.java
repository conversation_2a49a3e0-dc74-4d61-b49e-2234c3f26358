package com.drxin.bizz.controller;

import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import cn.idev.excel.FastExcel;
import com.drxin.bizz.vo.CourseAttendExportVo;
import com.drxin.bizz.vo.CourseEnrollmentExportVo;
import com.drxin.bizz.vo.CourseEnrollmentVo;
import com.drxin.common.exception.ServiceException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.drxin.common.annotation.Log;
import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.common.enums.BusinessType;
import com.drxin.bizz.domain.CourseEnrollment;
import com.drxin.bizz.service.ICourseEnrollmentService;
import com.drxin.common.utils.poi.ExcelUtil;
import com.drxin.common.core.page.TableDataInfo;

/**
 * 课程报名记录Controller
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@RestController
@RequestMapping("/bizz/course_enrollment")
public class CourseEnrollmentController extends BaseController {
    @Autowired
    private ICourseEnrollmentService courseEnrollmentService;

    /**
     * 查询课程报名记录列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:course_enrollment:list')")
    @GetMapping("/list")
    public TableDataInfo list(CourseEnrollmentVo courseEnrollment){
        startPage();
        List<CourseEnrollmentVo> list = courseEnrollmentService.selectCourseEnrollmentList(courseEnrollment);
        return getDataTable(list);
    }

    /**
     * 导出课程报名记录列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:course_enrollment:export')")
    @Log(title = "课程报名记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CourseEnrollmentVo courseEnrollment) throws IOException {
        List<CourseEnrollmentExportVo> courseEnrollmentExportVos = courseEnrollmentService.selectCourseEnrollmentExportList(courseEnrollment);
        if (courseEnrollmentExportVos == null || courseEnrollmentExportVos.isEmpty()) {
            throw new ServiceException("没有可导出的数据");
        }
        FastExcel.write(response.getOutputStream(), CourseEnrollmentExportVo.class)
                .sheet("课程签到记录")
                .doWrite(courseEnrollmentExportVos);
    }

    /**
     * 获取课程报名记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('bizz:course_enrollment:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(courseEnrollmentService.selectCourseEnrollmentById(id));
    }

    /**
     * 新增课程报名记录
     */
    @PreAuthorize("@ss.hasPermi('bizz:course_enrollment:add')")
    @Log(title = "课程报名记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CourseEnrollment courseEnrollment) {
        return toAjax(courseEnrollmentService.insertCourseEnrollment(courseEnrollment));
    }

    /**
     * 修改课程报名记录
     */
    @PreAuthorize("@ss.hasPermi('bizz:course_enrollment:edit')")
    @Log(title = "课程报名记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CourseEnrollment courseEnrollment) {
        return toAjax(courseEnrollmentService.updateCourseEnrollment(courseEnrollment));
    }

    /**
     * 删除课程报名记录
     */
    @PreAuthorize("@ss.hasPermi('bizz:course_enrollment:remove')")
    @Log(title = "课程报名记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(courseEnrollmentService.deleteCourseEnrollmentByIds(ids));
    }
}
