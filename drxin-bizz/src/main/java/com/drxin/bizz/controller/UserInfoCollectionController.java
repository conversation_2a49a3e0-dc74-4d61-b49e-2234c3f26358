package com.drxin.bizz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.drxin.bizz.domain.UserInfoCollection;
import com.drxin.bizz.service.IUserInfoCollectionService;
import com.drxin.bizz.vo.UserInfoCollectionVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.drxin.common.annotation.Log;
import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.common.enums.BusinessType;
import com.drxin.common.utils.poi.ExcelUtil;
import com.drxin.common.core.page.TableDataInfo;

/**
 * 用户信息收集Controller
 *
 * <AUTHOR>
 * @date 2025-05-18
 */
@RestController
@RequestMapping("/bizz/user_info_collection")
public class UserInfoCollectionController extends BaseController {
    @Autowired
    private IUserInfoCollectionService userInfoCollectionService;

    /**
     * 查询用户信息收集列表
     */
    @GetMapping("/list")
    public TableDataInfo list(UserInfoCollectionVo userInfoCollection) {
        startPage();
        List<UserInfoCollection> list = userInfoCollectionService.selectUserInfoCollectionList(userInfoCollection);
        return getDataTable(list);
    }

    /**
     * 导出用户信息收集列表
     */
    @Log(title = "用户信息收集", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserInfoCollectionVo userInfoCollection) {
        List<UserInfoCollection> list = userInfoCollectionService.selectUserInfoCollectionList(userInfoCollection);
        ExcelUtil<UserInfoCollection> util = new ExcelUtil<UserInfoCollection>(UserInfoCollection.class);
        util.exportExcel(response, list, "用户信息收集数据");
    }

    /**
     * 获取用户信息收集详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(userInfoCollectionService.selectUserInfoCollectionById(id));
    }

    /**
     * 新增用户信息收集
     */
    @Log(title = "用户信息收集", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserInfoCollection userInfoCollection) {
        return toAjax(userInfoCollectionService.insertUserInfoCollection(userInfoCollection));
    }

    /**
     * 修改用户信息收集
     */
    @Log(title = "用户信息收集", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserInfoCollection userInfoCollection) {
        return toAjax(userInfoCollectionService.updateUserInfoCollection(userInfoCollection));
    }

    /**
     * 删除用户信息收集
     */
    @Log(title = "用户信息收集", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(userInfoCollectionService.deleteUserInfoCollectionByIds(ids));
    }
}
