package com.drxin.bizz.vo;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.drxin.common.annotation.DictTypeProperty;
import com.drxin.common.convert.DictTypeConvert;
import lombok.Data;

@Data
public class CretApplyExportVo {

    @ExcelProperty(value = "姓名", index = 0)
    private String name;

    @ExcelProperty(value = "身份证号", index = 1)
    private String idCard;

    @ExcelProperty(value = "手机号码", index = 2)
    private String phone;

    @ExcelProperty(value = "性别", index = 3, converter = DictTypeConvert.class)
    @DictTypeProperty(value = "sys_user_sex")
    private String sex;

    @ExcelProperty(value = "收货地址", index = 4)
    private String receiveAddress;

    @ExcelIgnore
    private String photoUrl;

    @ExcelProperty(value = "照片", index = 5)
    private String photoName;

}
