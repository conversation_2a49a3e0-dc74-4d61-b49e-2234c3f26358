package com.drxin.bizz.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 成员急救员统计VO
 * 
 * <AUTHOR>
 */
@Data
public class MemberAiderStatVo {
    
    /** 姓名（查询条件+返回结果） */
    private String realName;
    
    /** 开始时间（查询条件） */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date beginTime;
    
    /** 结束时间（查询条件） */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;
    
    /** 用户类型（返回结果） */
    private String userType;
    
    /** 急救员人数（返回结果） */
    private Integer aiderCount;
}
