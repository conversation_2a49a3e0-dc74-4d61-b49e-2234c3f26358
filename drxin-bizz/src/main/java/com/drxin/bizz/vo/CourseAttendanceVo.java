package com.drxin.bizz.vo;

import com.drxin.bizz.domain.CourseAttendance;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class CourseAttendanceVo extends CourseAttendance {
    private String courseName;
    private String nickName;
    private String userType;
    private String realName;
    private String phoneNumber;
    private String beginTime;
    private String endTime;
}
