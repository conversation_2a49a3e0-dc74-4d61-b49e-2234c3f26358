package com.drxin.bizz.vo;

import com.drxin.bizz.domain.RescueCaseAttachment;
import com.drxin.bizz.domain.RescueCaseRescuer;
import com.drxin.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class RescueCaseVo {

    /** 主键 */
    private Long id;

    /** 急救日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date rescueDate;

    /** 急救所在城市 */
    private String city;

    /** 详细地址 */
    private String address;

    /** 被救人姓名 */
    private String patientName;

    /** 被救人性别 */
    private String patientGender;

    /** 被救人年龄 */
    private String patientAge;

    /** 病症类型 */
    private String illnessType;

    /** 急救情况说明 */
    private String rescueDescription;

    /** 急救案例状态 */
    private String rescueStatus;

    /** 急救员列表 */
    private List<RescueCaseRescuer> rescuers;

    /** 附件列表 */
    private List<RescueCaseAttachment> attachments;

    private String createBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 远程指导员用户ID */
    @Excel(name = "远程指导员用户ID")
    private Long remoteGuideUserId;

    /** 远程指导员真实姓名 */
    @Excel(name = "远程指导员真实姓名")
    private String remoteGuideRealName;
}
