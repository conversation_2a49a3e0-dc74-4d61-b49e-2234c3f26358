package com.drxin.bizz.vo;

import cn.idev.excel.annotation.ExcelProperty;
import com.drxin.common.annotation.DictTypeProperty;
import com.drxin.common.convert.DictTypeConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

@Data
public class CourseAttendExportVo {
    @ExcelProperty(value = "课程名称", index = 0)
    private String courseName;
    @ExcelProperty(value = "学员昵称", index = 1)
    private String nickName;
    @ExcelProperty(value = "用户类型", index = 2, converter = DictTypeConvert.class)
    @DictTypeProperty(value = "sys_user_type")
    private String userType;
    @ExcelProperty(value = "真实姓名", index = 3)
    private String realName;
    @ExcelProperty(value = "手机号", index = 4)
    private String phoneNumber;
    @ExcelProperty(value = "签到时间", index = 5)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String attendTime;
}
