package com.drxin.bizz.vo;

import cn.idev.excel.annotation.ExcelProperty;
import com.drxin.common.annotation.DictTypeProperty;
import com.drxin.common.convert.DictTypeConvert;
import lombok.Data;

/**
 * 成员急救员统计导出VO
 * 
 * <AUTHOR>
 */
@Data
public class MemberAiderStatExportVo {
    
    @ExcelProperty(value = "姓名", index = 0)
    private String realName;
    
    @ExcelProperty(value = "用户类型", index = 1, converter = DictTypeConvert.class)
    @DictTypeProperty(value = "sys_user_type")
    private String userType;
    
    @ExcelProperty(value = "急救员人数", index = 2)
    private Integer aiderCount;
}
