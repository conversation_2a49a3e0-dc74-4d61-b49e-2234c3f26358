package com.drxin.bizz.vo;

import com.drxin.bizz.domain.ActivityReview;
import com.drxin.bizz.domain.ActivityReviewAttachment;
import com.drxin.bizz.domain.ActivityReviewUser;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 活动评审聚合视图对象
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ActivityReviewVo extends ActivityReview {

    /** 附件URL列表 */
    private List<String> attachmentUrls;
}
