<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.bizz.mapper.CourseMapper">
    
    <resultMap type="com.drxin.bizz.domain.Course" id="CourseResult">
        <result property="id"    column="id"    />
        <result property="courseName"    column="course_name"    />
        <result property="description"    column="description"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="location"    column="location"    />
        <result property="maxQuota"    column="max_quota"    />
        <result property="currentQuota"    column="current_quota"    />
        <result property="enrollDeadline"    column="enroll_deadline"    />
        <result property="infoQrUrl"    column="info_qr_url"    />
        <result property="checkinQrUrl"    column="checkin_qr_url"    />
        <result property="courseStatus"    column="course_status"    />
        <result property="coursePrice"    column="course_price"    />
        <result property="courseType"    column="course_type"    />
        <result property="allowedRoles"    column="allowed_roles"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCourseVo">
        select id, course_name, description, start_time, end_time, location, max_quota, current_quota, enroll_deadline, info_qr_url,
               checkin_qr_url, course_status, course_price, course_type,allowed_roles, create_by, create_time, update_by, update_time from course
    </sql>

    <select id="selectCourseList" parameterType="com.drxin.bizz.domain.Course" resultMap="CourseResult">
        <include refid="selectCourseVo"/>
        <where>  
            <if test="courseName != null  and courseName != ''"> and course_name like concat('%', #{courseName}, '%')</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="maxQuota != null "> and max_quota = #{maxQuota}</if>
            <if test="currentQuota != null "> and current_quota = #{currentQuota}</if>
            <if test="enrollDeadline != null "> and enroll_deadline = #{enrollDeadline}</if>
            <if test="courseStatus != null  and courseStatus != ''"> and course_status = #{courseStatus}</if>
        </where>
        order by course_status desc, start_time asc
    </select>
    
    <select id="selectCourseById" parameterType="Long" resultMap="CourseResult">
        <include refid="selectCourseVo"/>
        where id = #{id}
    </select>
    <select id="selectLatestCourse" resultMap="CourseResult">
        select id, course_name, description, start_time, end_time, location, max_quota, current_quota, enroll_deadline, info_qr_url,
               checkin_qr_url, course_status, course_price, course_type, allowed_roles, create_by, create_time, update_by, update_time
        from course
        where DATE_FORMAT(start_time,'%Y-%m-%d') >= DATE_FORMAT(now(),'%Y-%m-%d')
        order by start_time asc
        limit 3
    </select>
    <select id="selectCurrentMonthCourse" resultMap="CourseResult">
        select id, course_name, description, start_time, end_time, location, max_quota, current_quota, enroll_deadline, info_qr_url,
               checkin_qr_url, course_status, course_price, course_type, allowed_roles, create_by, create_time, update_by, update_time
        from course
        where DATE_FORMAT(start_time,'%Y-%m') = DATE_FORMAT(now(),'%Y-%m')
        order by start_time asc
    </select>

    <insert id="insertCourse" parameterType="com.drxin.bizz.domain.Course" useGeneratedKeys="true" keyProperty="id">
        insert into course
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="courseName != null and courseName != ''">course_name,</if>
            <if test="description != null and description != ''">description,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="location != null and location != ''">location,</if>
            <if test="maxQuota != null">max_quota,</if>
            <if test="currentQuota != null">current_quota,</if>
            <if test="enrollDeadline != null">enroll_deadline,</if>
            <if test="infoQrUrl != null">info_qr_url,</if>
            <if test="checkinQrUrl != null">checkin_qr_url,</if>
            <if test="courseStatus != null and courseStatus != ''">course_status,</if>
            <if test="coursePrice != null">course_price,</if>
            <if test="courseType != null">course_type,</if>
            <if test="allowedRoles != null and allowedRoles != ''">allowed_roles,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="courseName != null and courseName != ''">#{courseName},</if>
            <if test="description != null and description != ''">#{description},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="location != null and location != ''">#{location},</if>
            <if test="maxQuota != null">#{maxQuota},</if>
            <if test="currentQuota != null">#{currentQuota},</if>
            <if test="enrollDeadline != null">#{enrollDeadline},</if>
            <if test="infoQrUrl != null">#{infoQrUrl},</if>
            <if test="checkinQrUrl != null">#{checkinQrUrl},</if>
            <if test="courseStatus != null and courseStatus != ''">#{courseStatus},</if>
            <if test="coursePrice != null">#{coursePrice},</if>
            <if test="courseType != null">#{courseType},</if>
            <if test="allowedRoles != null and allowedRoles != ''">#{allowedRoles},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCourse" parameterType="Course">
        update course
        <trim prefix="SET" suffixOverrides=",">
            <if test="courseName != null and courseName != ''">course_name = #{courseName},</if>
            <if test="description != null and description != ''">description = #{description},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="location != null and location != ''">location = #{location},</if>
            <if test="maxQuota != null">max_quota = #{maxQuota},</if>
            <if test="currentQuota != null">current_quota = #{currentQuota},</if>
            <if test="enrollDeadline != null">enroll_deadline = #{enrollDeadline},</if>
            <if test="infoQrUrl != null">info_qr_url = #{infoQrUrl},</if>
            <if test="checkinQrUrl != null">checkin_qr_url = #{checkinQrUrl},</if>
            <if test="courseStatus != null and courseStatus != ''">course_status = #{courseStatus},</if>
            <if test="coursePrice != null">course_price = #{coursePrice},</if>
            <if test="courseType != null">course_type = #{courseType},</if>
            <if test="allowedRoles != null and allowedRoles != ''">allowed_roles = #{allowedRoles},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="cleanCourseStatus">
        update course
        set course_status = '0'
        where course_status = '1' and end_time &lt; now()
    </update>

    <delete id="deleteCourseById" parameterType="Long">
        delete from course where id = #{id}
    </delete>

    <delete id="deleteCourseByIds" parameterType="String">
        delete from course where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
