<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.bizz.mapper.UserInfoCollectionMapper">

    <resultMap type="com.drxin.bizz.domain.UserInfoCollection" id="UserInfoCollectionResult">
            <result property="id" column="id"/>
            <result property="name" column="name"/>
            <result property="sex" column="sex"/>
            <result property="phone" column="phone"/>
            <result property="idCard" column="id_card"/>
            <result property="address" column="address"/>
            <result property="companions" column="companions"/>
            <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectUserInfoCollectionVo">
        select id, name, sex, phone, id_card, address, companions, create_time
        from user_info_collection
    </sql>

    <select id="selectUserInfoCollectionList" parameterType="com.drxin.bizz.vo.UserInfoCollectionVo" resultMap="UserInfoCollectionResult">
        <include refid="selectUserInfoCollectionVo"/>
        <where>
                        <if test="name != null  and name != ''">
                            and name like concat('%', #{name}, '%')
                        </if>
                        <if test="sex != null  and sex != ''">
                            and sex = #{sex}
                        </if>
                        <if test="phone != null  and phone != ''">
                            and phone = #{phone}
                        </if>
                        <if test="idCard != null  and idCard != ''">
                            and id_card = #{idCard}
                        </if>
                        <if test="address != null  and address != ''">
                            and address = #{address}
                        </if>
                        <if test="companions != null  and companions != ''">
                            and companions like concat('%', #{companions}, '%')
                        </if>
                        <if test="beginTime != null  and beginTime != ''">
                            and date_format(create_time,'%Y%m%d') &gt;= date_format(#{beginTime},'%Y%m%d')
                        </if>
                        <if test="endTime != null  and endTime != ''">
                            and date_format(create_time,'%Y%m%d') &lt;= date_format(#{endTime},'%Y%m%d')
                        </if>
        </where>
        order by id desc
    </select>

    <select id="selectUserInfoCollectionById" parameterType="Long"
            resultMap="UserInfoCollectionResult">
            <include refid="selectUserInfoCollectionVo"/>
            where id = #{id}
    </select>

    <insert id="insertUserInfoCollection" parameterType="com.drxin.bizz.domain.UserInfoCollection" useGeneratedKeys="true"
            keyProperty="id">
        insert into user_info_collection
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="name != null and name != ''">name,
                    </if>
                    <if test="sex != null">sex,
                    </if>
                    <if test="phone != null and phone != ''">phone,
                    </if>
                    <if test="idCard != null and idCard != ''">id_card,
                    </if>
                    <if test="address != null and address != ''">address,
                    </if>
                    <if test="companions != null">companions,
                    </if>
                    <if test="createTime != null">create_time,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="name != null and name != ''">#{name},
                    </if>
                    <if test="sex != null">#{sex},
                    </if>
                    <if test="phone != null and phone != ''">#{phone},
                    </if>
                    <if test="idCard != null and idCard != ''">#{idCard},
                    </if>
                    <if test="address != null and address != ''">#{address},
                    </if>
                    <if test="companions != null">#{companions},
                    </if>
                    <if test="createTime != null">#{createTime},
                    </if>
        </trim>
    </insert>

    <update id="updateUserInfoCollection" parameterType="com.drxin.bizz.domain.UserInfoCollection">
        update user_info_collection
        <trim prefix="SET" suffixOverrides=",">
                    <if test="name != null and name != ''">name =
                        #{name},
                    </if>
                    <if test="sex != null">sex =
                        #{sex},
                    </if>
                    <if test="phone != null and phone != ''">phone =
                        #{phone},
                    </if>
                    <if test="idCard != null and idCard != ''">id_card =
                        #{idCard},
                    </if>
                    <if test="address != null and address != ''">address =
                        #{address},
                    </if>
                    <if test="companions != null">companions =
                        #{companions},
                    </if>
                    <if test="createTime != null">create_time =
                        #{createTime},
                    </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserInfoCollectionById" parameterType="Long">
        delete
        from user_info_collection where id = #{id}
    </delete>

    <delete id="deleteUserInfoCollectionByIds" parameterType="String">
        delete from user_info_collection where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
