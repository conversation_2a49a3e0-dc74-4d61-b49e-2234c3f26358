<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.bizz.mapper.QrCodeMapper">
    
    <resultMap type="QrCode" id="QrCodeResult">
        <result property="id"    column="id"    />
        <result property="content"    column="content"    />
        <result property="description"    column="description"    />
        <result property="logoFilename"    column="logo_filename"    />
        <result property="qrCodeBase64"    column="qr_code_base64"    />
        <result property="createBy"    column="create_by"    />
    </resultMap>

    <sql id="selectQrCodeVo">
        select id, content, description, logo_filename, qr_code_base64, create_by from qr_code
    </sql>

    <select id="selectQrCodeList" parameterType="QrCode" resultMap="QrCodeResult">
        <include refid="selectQrCodeVo"/>
        <where>  
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="logoFilename != null  and logoFilename != ''"> and logo_filename like concat('%', #{logoFilename}, '%')</if>
            <if test="qrCodeBase64 != null  and qrCodeBase64 != ''"> and qr_code_base64 = #{qrCodeBase64}</if>
        </where>
    </select>
    
    <select id="selectQrCodeById" parameterType="Long" resultMap="QrCodeResult">
        <include refid="selectQrCodeVo"/>
        where id = #{id}
    </select>

    <insert id="insertQrCode" parameterType="QrCode" useGeneratedKeys="true" keyProperty="id">
        insert into qr_code
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="content != null and content != ''">content,</if>
            <if test="description != null">description,</if>
            <if test="logoFilename != null">logo_filename,</if>
            <if test="qrCodeBase64 != null">qr_code_base64,</if>
            <if test="createBy != null">create_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="content != null and content != ''">#{content},</if>
            <if test="description != null">#{description},</if>
            <if test="logoFilename != null">#{logoFilename},</if>
            <if test="qrCodeBase64 != null">#{qrCodeBase64},</if>
            <if test="createBy != null">#{createBy},</if>
         </trim>
    </insert>

    <update id="updateQrCode" parameterType="QrCode">
        update qr_code
        <trim prefix="SET" suffixOverrides=",">
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="description != null">description = #{description},</if>
            <if test="logoFilename != null">logo_filename = #{logoFilename},</if>
            <if test="qrCodeBase64 != null">qr_code_base64 = #{qrCodeBase64},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQrCodeById" parameterType="Long">
        delete from qr_code where id = #{id}
    </delete>

    <delete id="deleteQrCodeByIds" parameterType="String">
        delete from qr_code where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
