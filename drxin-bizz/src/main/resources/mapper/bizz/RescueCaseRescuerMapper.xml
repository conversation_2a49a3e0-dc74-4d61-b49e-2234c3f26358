<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.bizz.mapper.RescueCaseRescuerMapper">
    
    <resultMap type="com.drxin.bizz.domain.RescueCaseRescuer" id="RescueCaseRescuerResult">
        <result property="id"    column="id"    />
        <result property="caseId"    column="case_id"    />
        <result property="rescuerId"    column="rescuer_id"    />
        <result property="rescuerName"    column="rescuer_name"    />
        <result property="rescuerPhone"    column="rescuer_phone"    />
        <result property="score"    column="score"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectRescueCaseRescuerVo">
        select id, case_id, rescuer_id, rescuer_name, rescuer_phone, score, create_by, create_time, update_by, update_time from rescue_case_rescuer
    </sql>

    <select id="selectRescueCaseRescuerList" parameterType="com.drxin.bizz.domain.RescueCaseRescuer" resultMap="RescueCaseRescuerResult">
        <include refid="selectRescueCaseRescuerVo"/>
        <where>
            case_id = #{caseId}
        </where>
    </select>
    
    <select id="selectRescueCaseRescuerById" parameterType="Long" resultMap="RescueCaseRescuerResult">
        <include refid="selectRescueCaseRescuerVo"/>
        where id = #{id}
    </select>

    <insert id="insertRescueCaseRescuer" parameterType="com.drxin.bizz.domain.RescueCaseRescuer" useGeneratedKeys="true" keyProperty="id">
        insert into rescue_case_rescuer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="caseId != null">case_id,</if>
            <if test="rescuerId != null">rescuer_id,</if>
            <if test="rescuerName != null and rescuerName != ''">rescuer_name,</if>
            <if test="rescuerPhone != null">rescuer_phone,</if>
            <if test="score != null and score != ''">score,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="caseId != null">#{caseId},</if>
            <if test="rescuerId != null">#{rescuerId},</if>
            <if test="rescuerName != null and rescuerName != ''">#{rescuerName},</if>
            <if test="rescuerPhone != null">#{rescuerPhone},</if>
            <if test="score != null and score != ''">#{score},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRescueCaseRescuer" parameterType="com.drxin.bizz.domain.RescueCaseRescuer">
        update rescue_case_rescuer
        <trim prefix="SET" suffixOverrides=",">
            <if test="caseId != null">case_id = #{caseId},</if>
            <if test="rescuerId != null">rescuer_id = #{rescuerId},</if>
            <if test="rescuerName != null and rescuerName != ''">rescuer_name = #{rescuerName},</if>
            <if test="rescuerPhone != null">rescuer_phone = #{rescuerPhone},</if>
            <if test="score != null and score != ''">score = #{score},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRescueCaseRescuerById" parameterType="Long">
        delete from rescue_case_rescuer where id = #{id}
    </delete>

    <delete id="deleteRescueCaseRescuerByIds" parameterType="String">
        delete from rescue_case_rescuer where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
