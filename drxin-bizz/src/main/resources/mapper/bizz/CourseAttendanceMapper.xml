<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.bizz.mapper.CourseAttendanceMapper">
    
    <resultMap type="com.drxin.bizz.domain.CourseAttendance" id="CourseAttendanceResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="attendTime" column="attend_time" />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap id="CourseAttendListVo" type="com.drxin.bizz.vo.CourseAttendanceVo">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="courseName" column="course_name" />
        <result property="nickName" column="nick_name" />
        <result property="userType" column="user_type" />
        <result property="realName" column="real_name" />
        <result property="phoneNumber" column="phone_number" />
        <result property="attendTime" column="attend_time" />
    </resultMap>
    <resultMap id="CourseAttendExportList" type="com.drxin.bizz.vo.CourseAttendExportVo">
        <result property="courseName" column="course_name" />
        <result property="nickName" column="nick_name" />
        <result property="userType" column="user_type" />
        <result property="realName" column="real_name" />
        <result property="phoneNumber" column="phone_number" />
        <result property="attendTime" column="attend_time" />
    </resultMap>

    <sql id="selectCourseAttendanceVo">
        select id, user_id, course_id, create_by, create_time, update_by, update_time from course_attendance
    </sql>

    <select id="selectCourseAttendanceList" parameterType="com.drxin.bizz.vo.CourseAttendanceVo" resultMap="CourseAttendListVo">
        select ca.id, ca.user_id, ca.course_id, c.course_name as course_name, u.nick_name, u.real_name, u.phonenumber as
        phone_number, u.user_type, ca.attend_time as attend_time
        from course_attendance ca
        left join course c on ca.course_id = c.id
        left join sys_user u on ca.user_id = u.user_id
        <where>
            1=1
            <if test="attendTime != null">and ca.attend_time = #{attendTime}</if>
            <if test="courseName != null and courseName != ''">and c.course_name like concat('%', #{courseName}, '%')</if>
            <if test="nickName != null and nickName != ''">and u.nick_name like concat('%', #{nickName}, '%')</if>
            <if test="realName != null and realName != ''">and u.real_name like concat('%', #{realName}, '%')</if>
            <if test="phoneNumber != null and phoneNumber != ''">and u.phonenumber like concat('%', #{phoneNumber},
                '%')
            </if>
            <if test="userType != null and userType != ''">and u.user_type like concat('%', #{userType}, '%')</if>
            <if test="beginTime != null  and beginTime != ''">
                and date_format(attend_time,'%Y%m%d') &gt;= date_format(#{beginTime},'%Y%m%d')
            </if>
            <if test="endTime != null  and endTime != ''">
                and date_format(attend_time,'%Y%m%d') &lt;= date_format(#{endTime},'%Y%m%d')
            </if>
        </where>
        order by ca.attend_time desc
    </select>
    
    <select id="selectCourseAttendanceById" parameterType="Long" resultMap="CourseAttendanceResult">
        <include refid="selectCourseAttendanceVo"/>
        where id = #{id}
    </select>
    <select id="selectCourseAttendanceExportList" resultType="com.drxin.bizz.vo.CourseAttendExportVo">
        select ca.id, ca.user_id, ca.course_id, c.course_name as course_name, u.nick_name, u.real_name, u.phonenumber as
        phone_number, u.user_type, ca.attend_time as attend_time
        from course_attendance ca
        left join course c on ca.course_id = c.id
        left join sys_user u on ca.user_id = u.user_id
        <where>
            1=1
            <if test="attendTime != null">and ca.attend_time = #{attendTime}</if>
            <if test="courseName != null and courseName != ''">and c.course_name like concat('%', #{courseName}, '%')</if>
            <if test="nickName != null and nickName != ''">and u.nick_name like concat('%', #{nickName}, '%')</if>
            <if test="realName != null and realName != ''">and u.real_name like concat('%', #{realName}, '%')</if>
            <if test="phoneNumber != null and phoneNumber != ''">and u.phonenumber like concat('%', #{phoneNumber},'%')</if>
            <if test="userType != null and userType != ''">and u.user_type like concat('%', #{userType}, '%')</if>
            <if test="beginTime != null  and beginTime != ''">
                and date_format(attend_time,'%Y%m%d') &gt;= date_format(#{beginTime},'%Y%m%d')
            </if>
            <if test="endTime != null  and endTime != ''">
                and date_format(attend_time,'%Y%m%d') &lt;= date_format(#{endTime},'%Y%m%d')
            </if>
        </where>
    </select>

    <insert id="insertCourseAttendance" parameterType="CourseAttendance" useGeneratedKeys="true" keyProperty="id">
        insert into course_attendance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCourseAttendance" parameterType="CourseAttendance">
        update course_attendance
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCourseAttendanceById" parameterType="Long">
        delete from course_attendance where id = #{id}
    </delete>

    <delete id="deleteCourseAttendanceByIds" parameterType="String">
        delete from course_attendance where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
