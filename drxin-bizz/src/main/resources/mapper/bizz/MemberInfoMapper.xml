<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.bizz.mapper.MemberInfoMapper">

    <resultMap type="com.drxin.bizz.domain.MemberInfo" id="MemberInfoResult">
        <id property="userId" column="user_id"/>
        <result property="realName" column="real_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="sex" column="sex"/>
        <result property="idCard" column="id_card"/>
        <result property="phoneNumber" column="phonenumber"/>
        <result property="userType" column="user_type"/>
        <result property="inviter" column="inviter"/>
        <result property="inviterId" column="inviter_id"/>
        <result property="dealInviter" column="deal_inviter"/>
        <result property="dealInviterId" column="deal_inviter_id"/>
        <result property="registerTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="selectMemberInfoList" parameterType="com.drxin.bizz.domain.MemberInfo" resultMap="MemberInfoResult">
        select u.user_id,u.nick_name, u.real_name, u.sex,u.id_card, u.phonenumber, u.user_type, i.real_name as inviter, u.inviter_id,
               di.real_name as deal_inviter, u.deal_inviter_id, u.create_time, u.update_time
        from sys_user u
        left join sys_user i on i.user_id = u.inviter_id
        left join sys_user di on di.user_id = u.deal_inviter_id
        <where>
            and u.del_flag = '0' and u.user_type is not null
            <if test="realName != null  and realName != ''"> and u.nick_name like concat('%', #{realName}, '%') or u.real_name like concat('%', #{realName}, '%')</if>
            <if test="sex != null  and sex != ''"> and u.sex = #{sex}</if>
            <if test="idCard != null  and idCard != ''"> and u.id_card = #{idCard}</if>
            <if test="phoneNumber != null  and phoneNumber != ''"> and u.phonenumber = #{phoneNumber}</if>
            <if test="userType != null  and userType != ''"> and u.user_type like concat('%', #{userType}, '%')</if>
            <if test="inviter != null  and inviter != ''"> and i.real_name = #{inviter}</if>
            <if test="dealInviter!= null  and dealInviter!= ''"> and di.real_name = #{dealInviter}</if>
            <if test="beginTime != null"> and date_format(u.create_time, '%Y-%m-%d')  &gt;= date_format(#{beginTime}, '%Y-%m-%d') </if>
            <if test="endTime != null"> and date_format(u.create_time, '%Y-%m-%d') &lt;= date_format(#{endTime}, '%Y-%m-%d') </if>
        </where>
        order by u.update_time is null asc , IFNULL(u.update_time, u.create_time) desc
    </select>
    <select id="selectMemberInfoById" resultType="com.drxin.bizz.domain.MemberInfo">
        select u.user_id, u.nick_name, u.sex, u.phonenumber, u.user_type, i.real_name as inviter, u.inviter_id, u.create_time
        from sys_user u
        left join sys_user i on i.user_id = u.inviter_id
        where u.user_id = #{id}
    </select>

    <select id="checkDealInviterCycle" resultType="Integer">
        <if test="newDealInviterId != null and userIds != null and userIds.size() > 0">
            WITH RECURSIVE deal_chain AS (
                SELECT user_id, deal_inviter_id, 1 as level
                FROM sys_user
                WHERE user_id = #{newDealInviterId}

                UNION ALL

                SELECT u.user_id, u.deal_inviter_id, dc.level + 1
                FROM sys_user u
                INNER JOIN deal_chain dc ON u.user_id = CAST(dc.deal_inviter_id AS UNSIGNED)
                WHERE dc.level &lt; 50
                  AND dc.deal_inviter_id IS NOT NULL
                  AND dc.deal_inviter_id != ''
                  AND dc.deal_inviter_id REGEXP '^[0-9]+$'
            )
            SELECT COUNT(*)
            FROM deal_chain
            WHERE user_id IN
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="newDealInviterId == null or userIds == null or userIds.size() == 0">
            SELECT 0
        </if>
    </select>

    <select id="checkInviterCycle" resultType="int">
        <if test="newInviterId != null and userIds != null and userIds.size() > 0">
            WITH RECURSIVE inviter_chain AS (
                SELECT user_id,
                       CAST(inviter_id AS UNSIGNED) as inviter_id,
                       1 as level,
                       CAST(user_id AS CHAR(1000)) as path
                FROM sys_user
                WHERE user_id = #{newInviterId}
                  AND inviter_id IS NOT NULL
                  AND inviter_id != ''
                  AND inviter_id REGEXP '^[0-9]+$'

                UNION ALL

                SELECT u.user_id,
                       CAST(u.inviter_id AS UNSIGNED) as inviter_id,
                       ic.level + 1,
                       CONCAT(ic.path, '->', u.user_id)
                FROM sys_user u
                INNER JOIN inviter_chain ic ON u.user_id = ic.inviter_id
                WHERE ic.level &lt; 50
                  AND u.inviter_id IS NOT NULL
                  AND u.inviter_id != ''
                  AND u.inviter_id REGEXP '^[0-9]+$'
                  AND FIND_IN_SET(u.user_id, REPLACE(ic.path, '->', ',')) = 0
            )
            SELECT COUNT(*)
            FROM inviter_chain
            WHERE user_id IN
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="newInviterId == null or userIds == null or userIds.size() == 0">
            SELECT 0
        </if>
    </select>

    <select id="selectMemberAiderStatList" parameterType="com.drxin.bizz.vo.MemberAiderStatVo" resultType="com.drxin.bizz.vo.MemberAiderStatVo">
        SELECT
            b.real_name as realName,
            b.user_type as userType,
            a.aider_count as aiderCount
        FROM (
            SELECT
                deal_inviter_id,
                COUNT(1) AS aider_count
            FROM sys_user
            WHERE user_type = 'aider'
              AND deal_inviter_id IS NOT NULL
              <if test="beginTime != null">
                  AND date_format(upgraded_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
              </if>
              <if test="endTime != null">
                  AND date_format(upgraded_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
              </if>
            GROUP BY deal_inviter_id
        ) a
        LEFT JOIN sys_user b ON b.user_id = a.deal_inviter_id
        <where>
            <if test="realName != null and realName != ''">
                AND b.real_name LIKE CONCAT('%', #{realName}, '%')
            </if>
        </where>
        ORDER BY a.aider_count DESC
    </select>

</mapper>
