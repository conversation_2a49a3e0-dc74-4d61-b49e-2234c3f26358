<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.bizz.mapper.TeamMapper">
    
    <resultMap type="com.drxin.bizz.domain.Team" id="TeamResult">
        <result property="teamId"    column="team_id"    />
        <result property="teamName"    column="team_name"    />
        <result property="leaderId"    column="leader_id"    />
        <result property="leaderName"    column="leader_name"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectTeamVo">
        select team_id, team_name, leader_id, leader_name, create_time from team
    </sql>

    <select id="selectTeamList" parameterType="com.drxin.bizz.domain.Team" resultMap="TeamResult">
        <include refid="selectTeamVo"/>
        <where>  
            <if test="teamName != null  and teamName != ''"> and team_name like concat('%', #{teamName}, '%')</if>
            <if test="leaderName != null  and leaderName != ''"> and leader_name like concat('%', #{leaderName}, '%')</if>
        </where>
    </select>
    
    <select id="selectTeamByTeamId" parameterType="Long" resultMap="TeamResult">
        <include refid="selectTeamVo"/>
        where team_id = #{teamId}
    </select>

    <insert id="insertTeam" parameterType="com.drxin.bizz.domain.Team" useGeneratedKeys="true" keyProperty="teamId">
        insert into team
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teamName != null and teamName != ''">team_name,</if>
            <if test="leaderId != null and leaderId != ''">leader_id,</if>
            <if test="leaderName != null and leaderName != ''">leader_name,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teamName != null and teamName != ''">#{teamName},</if>
            <if test="leaderId != null and leaderId != ''">#{leaderId},</if>
            <if test="leaderName != null and leaderName != ''">#{leaderName},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateTeam" parameterType="com.drxin.bizz.domain.Team">
        update team
        <trim prefix="SET" suffixOverrides=",">
            <if test="teamName != null and teamName != ''">team_name = #{teamName},</if>
            <if test="leaderId != null and leaderId != ''">leader_id = #{leaderId},</if>
            <if test="leaderName != null and leaderName != ''">leader_name = #{leaderName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where team_id = #{teamId}
    </update>

    <delete id="deleteTeamByTeamId" parameterType="Long">
        delete from team where team_id = #{teamId}
    </delete>

    <delete id="deleteTeamByTeamIds" parameterType="String">
        delete from team where team_id in 
        <foreach item="teamId" collection="array" open="(" separator="," close=")">
            #{teamId}
        </foreach>
    </delete>
</mapper>
