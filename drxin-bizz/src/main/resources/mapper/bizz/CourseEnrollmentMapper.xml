<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.bizz.mapper.CourseEnrollmentMapper">

    <resultMap type="CourseEnrollment" id="CourseEnrollmentResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="courseId" column="course_id"/>
        <result property="enrollStatus" column="enroll_status"/>
        <result property="enrollTime" column="enroll_time"/>
        <result property="cancelTime" column="cancel_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap id="CourseEnrollmentVoResult" type="com.drxin.bizz.vo.CourseEnrollmentVo">
        <result property="courseName" column="course_name"/>
        <result property="enrollTime" column="enroll_time"/>
        <result property="nickName" column="nick_name"/>
        <result property="userType" column="user_type"/>
        <result property="realName" column="real_name"/>
        <result property="phoneNumber" column="phone_number"/>
    </resultMap>

    <sql id="selectCourseEnrollmentVo">
        select id,
               user_id,
               course_id,
               enroll_status,
               enroll_time,
               cancel_time,
               create_by,
               create_time,
               update_by,
               update_time
        from course_enrollment
    </sql>

    <select id="selectCourseEnrollmentList" parameterType="com.drxin.bizz.vo.CourseEnrollmentVo" resultMap="CourseEnrollmentVoResult">
        select c.course_name as course_name, u.nick_name, u.real_name,
               u.phonenumber as phone_number, u.user_type, ce.enroll_time as enroll_time
        from course_enrollment ce
        left join course c on ce.course_id = c.id
        left join sys_user u on ce.user_id = u.user_id
        <where>
            enroll_status = '1'
            <if test="enrollTime != null">and ce.enroll_time = #{enrollTime}</if>
            <if test="nickName != null and nickName != ''">and u.nick_name like concat('%', #{nickNaume}, '%')</if>
            <if test="realName != null and realName != ''">and u.real_name like concat('%', #{realName}, '%')</if>
            <if test="phoneNumber != null and phoneNumber != ''">and u.phonenumber like concat('%',
                #{phoneNumber},'%')
            </if>
            <if test="userType != null and userType != ''">and u.user_type like concat('%', #{userType}, '%')</if>
            <if test="beginTime != null  and beginTime != ''">
                and date_format(enroll_time,'%Y%m%d') &gt;= date_format(#{beginTime},'%Y%m%d')
            </if>
            <if test="endTime != null  and endTime != ''">
                and date_format(enroll_time,'%Y%m%d') &lt;= date_format(#{endTime},'%Y%m%d')
            </if>
        </where>
    </select>

    <select id="selectCourseEnrollmentById" parameterType="Long" resultMap="CourseEnrollmentResult">
        <include refid="selectCourseEnrollmentVo"/>
        where id = #{id}
    </select>
    <select id="selectCourseEnrollmentExportList" resultType="com.drxin.bizz.vo.CourseEnrollmentExportVo">
        select c.course_name as course_name, u.nick_name, u.real_name,
               u.phonenumber as phone_number, u.user_type, ce.enroll_time as enroll_time
        from course_enrollment ce
        left join course c on ce.course_id = c.id
        left join sys_user u on ce.user_id = u.user_id
        <where>
            enroll_status = '1'
            <if test="enrollTime != null">and ce.enroll_time = #{enrollTime}</if>
            <if test="nickName != null and nickName != ''">and u.nick_name like concat('%', #{nickNaume}, '%')</if>
            <if test="realName != null and realName != ''">and u.real_name like concat('%', #{realName}, '%')</if>
            <if test="phoneNumber != null and phoneNumber != ''">and u.phonenumber like concat('%',
                #{phoneNumber},'%')
            </if>
            <if test="userType != null and userType != ''">and u.user_type like concat('%', #{userType}, '%')</if>
            <if test="beginTime != null  and beginTime != ''">
                and date_format(enrollTime,'%Y%m%d') &gt;= date_format(#{beginTime},'%Y%m%d')
            </if>
            <if test="endTime != null  and endTime != ''">
                and date_format(enrollTime,'%Y%m%d') &lt;= date_format(#{endTime},'%Y%m%d')
            </if>
        </where>
    </select>

    <insert id="insertCourseEnrollment" parameterType="CourseEnrollment" useGeneratedKeys="true" keyProperty="id">
        insert into course_enrollment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="enrollStatus != null">enroll_status,</if>
            <if test="enrollTime != null">enroll_time,</if>
            <if test="cancelTime != null">cancel_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="enrollStatus != null">#{enrollStatus},</if>
            <if test="enrollTime != null">#{enrollTime},</if>
            <if test="cancelTime != null">#{cancelTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateCourseEnrollment" parameterType="CourseEnrollment">
        update course_enrollment
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="enrollStatus != null">enroll_status = #{enrollStatus},</if>
            <if test="enrollTime != null">enroll_time = #{enrollTime},</if>
            <if test="cancelTime != null">cancel_time = #{cancelTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCourseEnrollmentById" parameterType="Long">
        delete
        from course_enrollment
        where id = #{id}
    </delete>

    <delete id="deleteCourseEnrollmentByIds" parameterType="String">
        delete from course_enrollment where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
