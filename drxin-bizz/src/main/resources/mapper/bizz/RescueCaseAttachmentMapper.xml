<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.bizz.mapper.RescueCaseAttachmentMapper">
    
    <resultMap type="RescueCaseAttachment" id="RescueCaseAttachmentResult">
        <result property="id"           column="id" />
        <result property="caseId"       column="case_id" />
        <result property="fileId"       column="file_id" />
        <result property="fileUrl"      column="file_url" />
        <result property="fileType"     column="file_type" />
        <result property="createBy"     column="create_by" />
        <result property="createTime"   column="create_time" />
        <result property="updateBy"     column="update_by" />
        <result property="updateTime"   column="update_time" />
    </resultMap>

    <sql id="selectRescueCaseAttachmentVo">
        select id, case_id, file_id, file_url, file_type, create_by, create_time, update_by, update_time from rescue_case_attachments
    </sql>

    <select id="selectAttachmentsByCaseId" parameterType="Long" resultMap="RescueCaseAttachmentResult">
        <include refid="selectRescueCaseAttachmentVo"/>
        where case_id = #{caseId}
        order by create_time desc
    </select>
        
    <insert id="insertRescueCaseAttachment" parameterType="RescueCaseAttachment" useGeneratedKeys="true" keyProperty="id">
        insert into rescue_case_attachments
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="caseId != null">case_id,</if>
            <if test="fileId != null and fileId != ''">file_id,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="fileType != null and fileType != ''">file_type,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="caseId != null">#{caseId},</if>
            <if test="fileId != null and fileId != ''">#{fileId},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="fileType != null and fileType != ''">#{fileType},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <delete id="deleteAttachmentsByCaseId" parameterType="Long">
        delete from rescue_case_attachments where case_id = #{caseId}
    </delete>
</mapper>

