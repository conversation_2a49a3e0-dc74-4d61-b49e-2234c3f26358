<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.bizz.mapper.ContributionEventLogMapper">
    
    <resultMap type="ContributionEventLog" id="ContributionEventLogResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="actionCode"    column="action_code"    />
        <result property="eventSource"    column="event_source"    />
        <result property="contextData"    column="context_data"    />
        <result property="processStatus"    column="process_status"    />
        <result property="processMessage"    column="process_message"    />
        <result property="processTime"    column="process_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectContributionEventLogVo">
        select id, user_id, action_code, event_source, context_data, process_status, process_message, process_time, create_time, update_time from contribution_event_log
    </sql>

    <select id="selectContributionEventLogList" parameterType="ContributionEventLog" resultMap="ContributionEventLogResult">
        <include refid="selectContributionEventLogVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="actionCode != null  and actionCode != ''"> and action_code = #{actionCode}</if>
            <if test="eventSource != null  and eventSource != ''"> and event_source = #{eventSource}</if>
            <if test="processStatus != null  and processStatus != ''"> and process_status = #{processStatus}</if>
            <if test="processMessage != null  and processMessage != ''"> and process_message like concat('%', #{processMessage}, '%')</if>
            <if test="processTime != null "> and process_time = #{processTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectContributionEventLogById" parameterType="Long" resultMap="ContributionEventLogResult">
        <include refid="selectContributionEventLogVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertContributionEventLog" parameterType="ContributionEventLog" useGeneratedKeys="true" keyProperty="id">
        insert into contribution_event_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="actionCode != null and actionCode != ''">action_code,</if>
            <if test="eventSource != null and eventSource != ''">event_source,</if>
            <if test="contextData != null">context_data,</if>
            <if test="processStatus != null and processStatus != ''">process_status,</if>
            <if test="processMessage != null">process_message,</if>
            <if test="processTime != null">process_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="actionCode != null and actionCode != ''">#{actionCode},</if>
            <if test="eventSource != null and eventSource != ''">#{eventSource},</if>
            <if test="contextData != null">#{contextData},</if>
            <if test="processStatus != null and processStatus != ''">#{processStatus},</if>
            <if test="processMessage != null">#{processMessage},</if>
            <if test="processTime != null">#{processTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateContributionEventLog" parameterType="ContributionEventLog">
        update contribution_event_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="actionCode != null and actionCode != ''">action_code = #{actionCode},</if>
            <if test="eventSource != null and eventSource != ''">event_source = #{eventSource},</if>
            <if test="contextData != null">context_data = #{contextData},</if>
            <if test="processStatus != null and processStatus != ''">process_status = #{processStatus},</if>
            <if test="processMessage != null">process_message = #{processMessage},</if>
            <if test="processTime != null">process_time = #{processTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteContributionEventLogById" parameterType="Long">
        delete from contribution_event_log where id = #{id}
    </delete>

    <delete id="deleteContributionEventLogByIds" parameterType="String">
        delete from contribution_event_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 查询待处理的事件日志 -->
    <select id="selectPendingEventLogs" resultMap="ContributionEventLogResult">
        <include refid="selectContributionEventLogVo"/>
        where process_status = '0'
        order by create_time asc
        <if test="limit > 0">
            limit #{limit}
        </if>
    </select>

    <!-- 查询处理失败的事件日志 -->
    <select id="selectFailedEventLogs" resultMap="ContributionEventLogResult">
        <include refid="selectContributionEventLogVo"/>
        where process_status = '2'
        order by create_time asc
        <if test="limit > 0">
            limit #{limit}
        </if>
    </select>

</mapper>
