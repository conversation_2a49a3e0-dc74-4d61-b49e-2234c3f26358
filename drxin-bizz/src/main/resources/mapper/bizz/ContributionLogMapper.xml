<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.bizz.mapper.ContributionLogMapper">
    
    <resultMap type="com.drxin.bizz.domain.ContributionLog" id="ContributionLogResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="real_name"    />
        <result property="actionCode"    column="action_code"    />
        <result property="actionRefId"    column="action_ref_id"    />
        <result property="score"    column="score"    />
        <result property="description"    column="description"    />
        <result property="sourceType"    column="source_type"    />
        <result property="logStatus"    column="log_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectContributionLogVo">
        select cl.id, cl.user_id, u.real_name, cl.action_code, cl.action_ref_id, cl.score, cl.description, cl.source_type, cl.log_status, cl.create_by, cl.create_time, cl.update_by, cl.update_time
        from contribution_log cl
        left join sys_user u on cl.user_id = u.user_id
    </sql>

    <select id="selectContributionLogList" parameterType="com.drxin.bizz.domain.ContributionLog" resultMap="ContributionLogResult">
        <include refid="selectContributionLogVo"/>
        <where>
            <if test="userName != null  and userName != ''"> and u.real_name like concat('%', #{userName}, '%')</if>
            <if test="actionCode != null  and actionCode != ''"> and cl.action_code = #{actionCode}</if>
        </where>
    </select>
    
    <select id="selectContributionLogById" parameterType="Long" resultMap="ContributionLogResult">
        <include refid="selectContributionLogVo"/>
        where cl.id = #{id}
    </select>

    <insert id="insertContributionLog" parameterType="com.drxin.bizz.domain.ContributionLog" useGeneratedKeys="true" keyProperty="id">
        insert into contribution_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="actionCode != null and actionCode != ''">action_code,</if>
            <if test="actionRefId != null">action_ref_id,</if>
            <if test="score != null">score,</if>
            <if test="description != null">description,</if>
            <if test="sourceType != null">source_type,</if>
            <if test="logStatus != null">log_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="actionCode != null and actionCode != ''">#{actionCode},</if>
            <if test="actionRefId != null">#{actionRefId},</if>
            <if test="score != null">#{score},</if>
            <if test="description != null">#{description},</if>
            <if test="sourceType != null">#{sourceType},</if>
            <if test="logStatus != null">#{logStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateContributionLog" parameterType="com.drxin.bizz.domain.ContributionLog">
        update contribution_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="actionCode != null and actionCode != ''">action_code = #{actionCode},</if>
            <if test="actionRefId != null">action_ref_id = #{actionRefId},</if>
            <if test="score != null">score = #{score},</if>
            <if test="description != null">description = #{description},</if>
            <if test="sourceType != null">source_type = #{sourceType},</if>
            <if test="logStatus != null">log_status = #{logStatus},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteContributionLogById" parameterType="Long">
        delete from contribution_log where id = #{id}
    </delete>

    <delete id="deleteContributionLogByIds" parameterType="String">
        delete from contribution_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
