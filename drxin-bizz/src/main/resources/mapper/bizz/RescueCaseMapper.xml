<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.bizz.mapper.RescueCaseMapper">
    
    <resultMap type="com.drxin.bizz.domain.RescueCase" id="RescueCaseResult">
        <result property="id"    column="id"    />
        <result property="rescueDate"    column="rescue_date"    />
        <result property="city"    column="city"    />
        <result property="address"    column="address"    />
        <result property="patientName"    column="patient_name"    />
        <result property="patientGender"    column="patient_gender"    />
        <result property="patientAge"    column="patient_age"    />
        <result property="illnessType"    column="illness_type"    />
        <result property="rescueDescription"    column="rescue_description"    />
        <result property="rescueStatus"    column="rescue_status"    />
        <result property="onlineFlag"    column="online_flag"    />
        <result property="remoteGuideUserId"    column="remote_guide_user_id"    />
        <result property="remoteGuideRealName"    column="remote_guide_real_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>
    <resultMap id="RescueCaseVoResult" type="com.drxin.bizz.vo.RescueCaseVo" >
        <result property="id"    column="id"    />
        <result property="rescueDate"    column="rescue_date"    />
        <result property="city"    column="city"    />
        <result property="address"    column="address"    />
        <result property="patientName"    column="patient_name"    />
        <result property="patientGender"    column="patient_gender"    />
        <result property="patientAge"    column="patient_age"    />
        <result property="illnessType"    column="illness_type"    />
        <result property="rescueDescription"    column="rescue_description"    />
        <result property="rescueStatus"    column="rescue_status"    />
        <result property="onlineFlag"    column="online_flag"    />
        <result property="remoteGuideUserId"    column="remote_guide_user_id"    />
        <result property="remoteGuideRealName"    column="remote_guide_real_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectRescueCaseVo">
        select id, rescue_date, city, address, patient_name, patient_gender, patient_age, illness_type, rescue_description, rescue_status, online_flag, remote_guide_user_id, remote_guide_real_name, create_by, create_time, update_by, update_time from rescue_case
    </sql>

    <select id="selectRescueCaseList" parameterType="com.drxin.bizz.domain.RescueCase" resultMap="RescueCaseVoResult">
        <include refid="selectRescueCaseVo"/>
        <where>  
            <if test="rescueDate != null "> and rescue_date = #{rescueDate}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="patientName != null  and patientName != ''"> and patient_name like concat('%', #{patientName}, '%')</if>
            <if test="patientGender != null  and patientGender != ''"> and patient_gender = #{patientGender}</if>
            <if test="patientAge != null  and patientAge != ''"> and patient_age = #{patientAge}</if>
            <if test="illnessType != null  and illnessType != ''"> and illness_type = #{illnessType}</if>
            <if test="rescueDescription != null  and rescueDescription != ''"> and rescue_description = #{rescueDescription}</if>
            <if test="rescueStatus != null  and rescueStatus != ''"> and rescue_status = #{rescueStatus}</if>
            <if test="onlineFlag != null  and onlineFlag != ''"> and online_flag = #{onlineFlag}</if>
            <if test="remoteGuideUserId != null"> and remote_guide_user_id = #{remoteGuideUserId}</if>
            <if test="remoteGuideRealName != null  and remoteGuideRealName != ''"> and remote_guide_real_name like concat('%', #{remoteGuideRealName}, '%')</if>
        </where>
        order by
            case rescue_status
                when '2' then 1  -- 审核中优先级最高
                when '4' then 2  -- 驳回其次
                when '3' then 3  -- 通过
                when '1' then 3  -- 新建
                else 4           -- 其他状态
            end,
            create_time desc
    </select>
    
    <select id="selectRescueCaseById" parameterType="Long" resultMap="RescueCaseResult">
        <include refid="selectRescueCaseVo"/>
        where id = #{id}
    </select>

    <insert id="insertRescueCase" parameterType="com.drxin.bizz.domain.RescueCase" useGeneratedKeys="true" keyProperty="id">
        insert into rescue_case
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rescueDate != null">rescue_date,</if>
            <if test="city != null and city != ''">city,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="patientName != null and patientName != ''">patient_name,</if>
            <if test="patientGender != null and patientGender != ''">patient_gender,</if>
            <if test="patientAge != null">patient_age,</if>
            <if test="illnessType != null">illness_type,</if>
            <if test="rescueDescription != null">rescue_description,</if>
            <if test="rescueStatus != null">rescue_status,</if>
            <if test="onlineFlag != null and onlineFlag != ''">online_flag,</if>
            <if test="remoteGuideUserId != null">remote_guide_user_id,</if>
            <if test="remoteGuideRealName != null and remoteGuideRealName != ''">remote_guide_real_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rescueDate != null">#{rescueDate},</if>
            <if test="city != null and city != ''">#{city},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="patientName != null and patientName != ''">#{patientName},</if>
            <if test="patientGender != null and patientGender != ''">#{patientGender},</if>
            <if test="patientAge != null">#{patientAge},</if>
            <if test="illnessType != null">#{illnessType},</if>
            <if test="rescueDescription != null">#{rescueDescription},</if>
            <if test="rescueStatus != null">#{rescueStatus},</if>
            <if test="onlineFlag != null and onlineFlag != ''">#{onlineFlag},</if>
            <if test="remoteGuideUserId != null">#{remoteGuideUserId},</if>
            <if test="remoteGuideRealName != null and remoteGuideRealName != ''">#{remoteGuideRealName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRescueCase" parameterType="com.drxin.bizz.domain.RescueCase">
        update rescue_case
        <trim prefix="SET" suffixOverrides=",">
            <if test="rescueDate != null">rescue_date = #{rescueDate},</if>
            <if test="city != null and city != ''">city = #{city},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="patientName != null and patientName != ''">patient_name = #{patientName},</if>
            <if test="patientGender != null and patientGender != ''">patient_gender = #{patientGender},</if>
            <if test="patientAge != null">patient_age = #{patientAge},</if>
            <if test="illnessType != null">illness_type = #{illnessType},</if>
            <if test="rescueDescription != null">rescue_description = #{rescueDescription},</if>
            <if test="rescueStatus != null">rescue_status = #{rescueStatus},</if>
            <if test="onlineFlag != null and onlineFlag != ''">online_flag = #{onlineFlag},</if>
            <if test="remoteGuideUserId != null">remote_guide_user_id = #{remoteGuideUserId},</if>
            <if test="remoteGuideRealName != null and remoteGuideRealName != ''">remote_guide_real_name = #{remoteGuideRealName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRescueCaseById" parameterType="Long">
        delete from rescue_case where id = #{id}
    </delete>

    <delete id="deleteRescueCaseByIds" parameterType="String">
        delete from rescue_case where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
