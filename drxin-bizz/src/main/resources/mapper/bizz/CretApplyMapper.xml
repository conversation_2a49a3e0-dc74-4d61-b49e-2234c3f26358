<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.bizz.mapper.CretApplyMapper">

    <resultMap type="com.drxin.bizz.domain.CretApply" id="CretApplyResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="cardType" column="card_type"/>
        <result property="phone" column="phone"/>
        <result property="sex" column="sex"/>
        <result property="education" column="education"/>
        <result property="company" column="company"/>
        <result property="photoUrl" column="photo_url"/>
        <result property="heimlichUrl" column="heimlich_url"/>
        <result property="paymentUrl" column="payment_url"/>
        <result property="examUrl" column="exam_url"/>
        <result property="applyType" column="apply_type"/>
        <result property="applyStatus" column="apply_status"/>
        <result property="receiveType" column="receive_type"/>
        <result property="addressId" column="address_id"/>
        <result property="receiveAddress" column="receive_address"/>
        <result property="practiceFlag" column="practice_flag"/>
        <result property="rejectReason" column="reject_reason"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap id="CretApplyExportVo" type="com.drxin.bizz.vo.CretApplyExportVo">
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="phone" column="phone"/>
        <result property="sex" column="sex"/>
        <result property="receiveAddress" column="receive_address"/>
        <result property="photoUrl" column="photo_url"/>
        <result property="photoName" column="photo_name"/>
    </resultMap>

    <sql id="selectCretApplyVo">
        select id, name, id_card, card_type, phone, sex, education, company, photo_url, heimlich_url, payment_url, exam_url,
               apply_type, apply_status, receive_type, address_id, receive_address, practice_flag, reject_reason, create_by,
               create_time, update_by, update_time from cret_apply
    </sql>
    <sql id="selectCretApplyExportVo">
        select name, id_card, phone, sex, receive_address, photo_url, CONCAT(IFNULL(name, ''), '_', IFNULL(id_card, ''), '.jpg') AS photo_name from cret_apply
    </sql>

    <select id="selectCretApplyList" parameterType="com.drxin.bizz.vo.CretApplyVo" resultMap="CretApplyResult">
        <include refid="selectCretApplyVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="idCard != null  and idCard != ''">and id_card = #{idCard}</if>
            <if test="sex != null  and sex != ''">and sex = #{sex}</if>
            <if test="applyType != null  and applyType != ''">and apply_type = #{applyType}</if>
            <if test="applyStatus != null ">and apply_status = #{applyStatus}</if>
            <if test="receiveType != null  and receiveType != ''">and receive_type = #{receiveType}</if>
            <if test="createBy != null  and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="beginTime != null  and beginTime != ''">
                and date_format(create_time,'%Y%m%d') &gt;= date_format(#{beginTime},'%Y%m%d')
            </if>
            <if test="endTime != null  and endTime != ''">
                and date_format(create_time,'%Y%m%d') &lt;= date_format(#{endTime},'%Y%m%d')
            </if>
        </where>
        order by
        case apply_status
        when 1 then 0
        when 2 then 1
        when 3 then 2
        else 3
        end,
        id desc
    </select>

    <select id="selectCretApplyById" parameterType="Long" resultMap="CretApplyResult">
        <include refid="selectCretApplyVo"/>
        where id = #{id}
    </select>

    <insert id="insertCretApply" parameterType="CretApply" useGeneratedKeys="true" keyProperty="id">
        insert into cret_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="idCard != null and idCard != ''">id_card,</if>
            <if test="cardType != null and cardType != ''">card_type,</if>
            <if test="phone != null">phone,</if>
            <if test="sex != null">sex,</if>
            <if test="education != null">education,</if>
            <if test="company != null">company,</if>
            <if test="photoUrl != null and photoUrl != ''">photo_url,</if>
            <if test="heimlichUrl != null and heimlichUrl != ''">heimlich_url,</if>
            <if test="paymentUrl != null and paymentUrl != ''">payment_url,</if>
            <if test="examUrl != null">exam_url,</if>
            <if test="applyType != null and applyType != ''">apply_type,</if>
            <if test="applyStatus != null">apply_status,</if>
            <if test="receiveType != null and receiveType != ''">receive_type,</if>
            <if test="addressId != null">address_id,</if>
            <if test="receiveAddress != null and receiveAddress != ''">receive_address,</if>
            <if test="practiceFlag!= null">practice_flag,</if>
            <if test="rejectReason != null and rejectReason != ''">reject_reason,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="idCard != null and idCard != ''">#{idCard},</if>
            <if test="cardType != null and cardType != ''">#{cardType},</if>
            <if test="phone != null">#{phone},</if>
            <if test="sex != null">#{sex},</if>
            <if test="education != null">#{education},</if>
            <if test="company != null">#{company},</if>
            <if test="photoUrl != null and photoUrl != ''">#{photoUrl},</if>
            <if test="heimlichUrl != null and heimlichUrl != ''">#{heimlichUrl},</if>
            <if test="paymentUrl != null and paymentUrl != ''">#{paymentUrl},</if>
            <if test="examUrl != null">#{examUrl},</if>
            <if test="applyType != null and applyType != ''">#{applyType},</if>
            <if test="applyStatus != null">#{applyStatus},</if>
            <if test="receiveType != null and receiveType != ''">#{receiveType},</if>
            <if test="addressId != null">#{addressId},</if>
            <if test="receiveAddress != null and receiveAddress != ''">#{receiveAddress},</if>
            <if test="practiceFlag!= null">#{practiceFlag},</if>
            <if test="rejectReason != null and rejectReason != ''">#{rejectReason},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateCretApply" parameterType="CretApply">
        update cret_apply
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="idCard != null and idCard != ''">id_card = #{idCard},</if>
            <if test="cardType != null and cardType != ''">card_type = #{cardType},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="education != null">education = #{education},</if>
            <if test="company != null">company = #{company},</if>
            <if test="photoUrl != null and photoUrl != ''">photo_url = #{photoUrl},</if>
            <if test="heimlichUrl != null and heimlichUrl != ''">heimlich_url = #{heimlichUrl},</if>
            <if test="paymentUrl != null and paymentUrl != ''">payment_url = #{paymentUrl},</if>
            <if test="examUrl != null">exam_url = #{examUrl},</if>
            <if test="applyType != null and applyType != ''">apply_type = #{applyType},</if>
            <if test="applyStatus != null">apply_status = #{applyStatus},</if>
            <if test="receiveType != null and receiveType != ''">receive_type = #{receiveType},</if>
            <if test="addressId != null">address_id = #{addressId},</if>
            <if test="receiveAddress != null and receiveAddress != ''">receive_address = #{receiveAddress},</if>
            <if test="practiceFlag!= null">practice_flag = #{practiceFlag},</if>
            <if test="rejectReason != null">reject_reason = #{rejectReason},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCretApplyById" parameterType="Long">
        delete
        from cret_apply
        where id = #{id}
    </delete>

    <delete id="deleteCretApplyByIds" parameterType="String">
        delete from cret_apply where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectCretApplyExportVoList" parameterType="com.drxin.bizz.vo.CretApplyVo" resultMap="CretApplyExportVo">
        <include refid="selectCretApplyExportVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="idCard != null  and idCard != ''">and id_card = #{idCard}</if>
            <if test="sex != null  and sex != ''">and sex = #{sex}</if>
            <if test="applyType != null  and applyType != ''">and apply_type = #{applyType}</if>
            <if test="applyStatus != null ">and apply_status = #{applyStatus}</if>
            <if test="receiveType != null  and receiveType != ''">and receive_type = #{receiveType}</if>
            <if test="beginTime != null  and beginTime != ''">
                and date_format(create_time,'%Y%m%d') &gt;= date_format(#{beginTime},'%Y%m%d')
            </if>
            <if test="endTime != null  and endTime != ''">
                and date_format(create_time,'%Y%m%d') &lt;= date_format(#{endTime},'%Y%m%d')
            </if>
        </where>
        order by id desc
    </select>


</mapper>
