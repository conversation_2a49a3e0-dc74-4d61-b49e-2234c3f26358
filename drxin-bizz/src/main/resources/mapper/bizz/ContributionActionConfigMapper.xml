<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.bizz.mapper.ContributionActionConfigMapper">
    
    <resultMap type="ContributionActionConfig" id="ContributionActionConfigResult">
        <result property="id"    column="id"    />
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="score"    column="score"    />
        <result property="averageFlag"    column="average_flag"    />
        <result property="remark"    column="remark"    />
        <result property="enabled"    column="enabled"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectContributionActionConfigVo">
        select id, code, name, score, average_flag, remark, enabled, create_by, create_time, update_by, update_time from contribution_action_config
    </sql>

    <select id="selectContributionActionConfigList" parameterType="ContributionActionConfig" resultMap="ContributionActionConfigResult">
        <include refid="selectContributionActionConfigVo"/>
        <where>  
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="averageFlag != null  and averageFlag != ''"> and average_flag = #{averageFlag}</if>
            <if test="enabled != null  and enabled != ''"> and enabled = #{enabled}</if>
        </where>
    </select>
    
    <select id="selectContributionActionConfigById" parameterType="Long" resultMap="ContributionActionConfigResult">
        <include refid="selectContributionActionConfigVo"/>
        where id = #{id}
    </select>

    <insert id="insertContributionActionConfig" parameterType="ContributionActionConfig" useGeneratedKeys="true" keyProperty="id">
        insert into contribution_action_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">code,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="score != null">score,</if>
            <if test="averageFlag != null">average_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="enabled != null">enabled,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">#{code},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="score != null">#{score},</if>
            <if test="averageFlag != null">#{averageFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="enabled != null">#{enabled},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateContributionActionConfig" parameterType="ContributionActionConfig">
        update contribution_action_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="score != null">score = #{score},</if>
            <if test="averageFlag != null">average_flag = #{averageFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="enabled != null">enabled = #{enabled},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteContributionActionConfigById" parameterType="Long">
        delete from contribution_action_config where id = #{id}
    </delete>

    <delete id="deleteContributionActionConfigByIds" parameterType="String">
        delete from contribution_action_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
