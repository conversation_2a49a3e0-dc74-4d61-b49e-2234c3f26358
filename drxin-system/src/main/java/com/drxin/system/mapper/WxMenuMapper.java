package com.drxin.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.drxin.system.domain.WxMenu;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 微信小程序菜单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-14
 */
public interface WxMenuMapper extends BaseMapper<WxMenu> {

    /**
     * 查询微信小程序菜单列表
     *
     * @param wxMenu 微信小程序菜单
     * @return 微信小程序菜单集合
     */
    List<WxMenu> selectWxMenuList(WxMenu wxMenu);

    /**
     * 根据菜单类型查询启用的菜单列表
     *
     * @param menuType 菜单类型
     * @return 菜单列表
     */
    List<WxMenu> selectEnabledMenusByType(@Param("menuType") String menuType);

    /**
     * 根据ID查询菜单
     *
     * @param id 菜单ID
     * @return 菜单对象
     */
    WxMenu selectWxMenuById(Integer id);

    /**
     * 新增微信小程序菜单
     *
     * @param wxMenu 微信小程序菜单
     * @return 结果
     */
    int insertWxMenu(WxMenu wxMenu);

    /**
     * 修改微信小程序菜单
     *
     * @param wxMenu 微信小程序菜单
     * @return 结果
     */
    int updateWxMenu(WxMenu wxMenu);

    /**
     * 删除微信小程序菜单
     *
     * @param id 微信小程序菜单主键
     * @return 结果
     */
    int deleteWxMenuById(Integer id);

    /**
     * 批量删除微信小程序菜单
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteWxMenuByIds(Integer[] ids);
}
