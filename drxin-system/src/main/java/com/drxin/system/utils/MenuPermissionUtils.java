package com.drxin.system.utils;

import com.drxin.common.core.domain.entity.SysRole;
import com.drxin.common.utils.StringUtils;
import com.drxin.system.domain.WxMenu;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 菜单权限工具类
 *
 * <AUTHOR>
 * @date 2024-07-14
 */
public class MenuPermissionUtils {

    /**
     * 检查用户是否有权限访问指定菜单
     *
     * @param menu 菜单对象
     * @param userRoles 用户角色列表
     * @return 是否有权限
     */
    public static boolean hasPermission(WxMenu menu, List<SysRole> userRoles) {
        if (menu == null || menu.getStatus() != 1) {
            return false;
        }

        String showFor = menu.getShowFor();
        if (StringUtils.isEmpty(showFor)) {
            return false;
        }

        // 如果包含 "all"，则所有登录用户都可以访问
        if (showFor.contains("all")) {
            return true;
        }

        if (userRoles == null || userRoles.isEmpty()) {
            return false;
        }

        // 获取用户的角色键列表
        List<String> userRoleKeys = userRoles.stream()
                .map(SysRole::getRoleKey)
                .collect(Collectors.toList());

        // 解析菜单的显示角色
        List<String> menuRoles = Arrays.asList(showFor.split(","));

        // 检查用户角色是否与菜单要求的角色有交集
        return userRoleKeys.stream().anyMatch(menuRoles::contains);
    }

    /**
     * 过滤用户可访问的菜单列表
     *
     * @param menus 菜单列表
     * @param userRoles 用户角色列表
     * @return 过滤后的菜单列表
     */
    public static List<WxMenu> filterMenusByPermission(List<WxMenu> menus, List<SysRole> userRoles) {
        return menus.stream()
                .filter(menu -> hasPermission(menu, userRoles))
                .collect(Collectors.toList());
    }
}
