package com.drxin.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.drxin.common.annotation.Excel;
import com.drxin.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 微信小程序菜单对象 wx_menus
 *
 * <AUTHOR>
 * @date 2024-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wx_menus")
public class WxMenu extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 菜单ID */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** 菜单名称 */
    @Excel(name = "菜单名称")
    @NotBlank(message = "菜单名称不能为空")
    @Size(max = 50, message = "菜单名称长度不能超过50个字符")
    private String title;

    /** 图标名称 */
    @Excel(name = "图标名称")
    @NotBlank(message = "图标名称不能为空")
    @Size(max = 50, message = "图标名称长度不能超过50个字符")
    private String icon;

    /** 页面路径 */
    @Excel(name = "页面路径")
    @NotBlank(message = "页面路径不能为空")
    @Size(max = 200, message = "页面路径长度不能超过200个字符")
    private String path;

    /** 菜单类型 */
    @Excel(name = "菜单类型", readConverterExp = "personal=个人设置,function=功能菜单")
    @NotBlank(message = "菜单类型不能为空")
    @TableField("menu_type")
    private String menuType;

    /** 显示给哪些角色 */
    @Excel(name = "显示角色")
    @TableField("show_for")
    private String showFor;

    /** 排序权重 */
    @Excel(name = "排序权重")
    @TableField("sort_order")
    private Integer sortOrder;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "0=禁用,1=启用")
    private Integer status;

    /** 菜单描述 */
    @Excel(name = "菜单描述")
    private String description;

    /** 创建时间 */
    @TableField("created_at")
    private java.util.Date createdAt;

    /** 更新时间 */
    @TableField("updated_at")
    private java.util.Date updatedAt;
}
