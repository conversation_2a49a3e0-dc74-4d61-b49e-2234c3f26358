package com.drxin.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

@Data
@Builder
@TableName("upload_record")
public class UploadRecord {

    @TableField("id")
    @TableId(type = IdType.AUTO)
    private Long id;
    @TableField("original_file_name")
    private String originalFileName;
    @TableField("file_size")
    private Long fileSize;
    @TableField("file_extension")
    private String fileExtension;
    @TableField("storage_type")
    private String storageType;
    @TableField("storage_path")
    private String storagePath;
    @TableField("upload_user_id")
    private Long uploadUserId;
    @TableField("upload_status")
    private Integer uploadStatus;
    @TableField("create_by")
    private String createBy;
    @TableField("create_time")
    private Date createTime;
    @TableField("update_by")
    private String updateBy;
    @TableField("update_time")
    private Date updateTime;
}
