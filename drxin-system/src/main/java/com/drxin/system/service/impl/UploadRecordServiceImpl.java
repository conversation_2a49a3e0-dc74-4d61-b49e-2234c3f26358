package com.drxin.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.drxin.common.constant.UploadStatusConstants;
import com.drxin.system.domain.UploadRecord;
import com.drxin.system.mapper.UploadRecordMapper;
import com.drxin.system.service.IUploadRecordService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class UploadRecordServiceImpl extends ServiceImpl<UploadRecordMapper, UploadRecord> implements IUploadRecordService {
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveUploadRecord(UploadRecord uploadRecord) {
        return baseMapper.insert(uploadRecord);
    }

    @Override
    public UploadRecord getUploadRecordById(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public int deleteUploadRecordById(Long id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public int updateUploadRecordStatus(Long id, String status) {
        UpdateWrapper<UploadRecord> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id);
        updateWrapper.set("upload_status", status);
        return baseMapper.update(null, updateWrapper);
    }

    @Override
    public void updateUploadRecordStatusToDelete(Long oldId, Long newId) {
        // 判断 oldId 跟 newId 是否相同
        if (!newId.equals(oldId)) {
            // 如果不相同，执行更新操作
            UpdateWrapper<UploadRecord> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", oldId);
            updateWrapper.set("upload_status", UploadStatusConstants.WAIT_DELETE.getCode());
            baseMapper.update(null, updateWrapper);

            // 更新 newId 的状态为 "已上传"
            UpdateWrapper<UploadRecord> newUpdateWrapper = new UpdateWrapper<>();
            newUpdateWrapper.eq("id", newId);
            newUpdateWrapper.set("upload_status", UploadStatusConstants.USED.getCode());
            baseMapper.update(null, newUpdateWrapper);
        }
    }
}
