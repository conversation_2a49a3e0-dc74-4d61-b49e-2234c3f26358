package com.drxin.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.drxin.common.core.domain.entity.SysRole;
import com.drxin.common.utils.bean.BeanUtils;
import com.drxin.system.domain.WxMenu;
import com.drxin.system.domain.vo.WxMenuVo;
import com.drxin.system.mapper.WxMenuMapper;
import com.drxin.system.service.IWxMenuService;
import com.drxin.system.utils.MenuPermissionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 微信小程序菜单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-14
 */
@Service
public class WxMenuServiceImpl extends ServiceImpl<WxMenuMapper, WxMenu> implements IWxMenuService {

    @Autowired
    private WxMenuMapper wxMenuMapper;

    /**
     * 查询微信小程序菜单列表
     *
     * @param wxMenu 微信小程序菜单
     * @return 微信小程序菜单
     */
    @Override
    public List<WxMenu> selectWxMenuList(WxMenu wxMenu) {
        return wxMenuMapper.selectWxMenuList(wxMenu);
    }

    /**
     * 根据ID查询微信小程序菜单
     *
     * @param id 微信小程序菜单主键
     * @return 微信小程序菜单
     */
    @Override
    public WxMenu selectWxMenuById(Integer id) {
        return wxMenuMapper.selectWxMenuById(id);
    }

    /**
     * 新增微信小程序菜单
     *
     * @param wxMenu 微信小程序菜单
     * @return 结果
     */
    @Override
    public int insertWxMenu(WxMenu wxMenu) {
        return wxMenuMapper.insertWxMenu(wxMenu);
    }

    /**
     * 修改微信小程序菜单
     *
     * @param wxMenu 微信小程序菜单
     * @return 结果
     */
    @Override
    public int updateWxMenu(WxMenu wxMenu) {
        return wxMenuMapper.updateWxMenu(wxMenu);
    }

    /**
     * 批量删除微信小程序菜单
     *
     * @param ids 需要删除的微信小程序菜单主键
     * @return 结果
     */
    @Override
    public int deleteWxMenuByIds(Integer[] ids) {
        return wxMenuMapper.deleteWxMenuByIds(ids);
    }

    /**
     * 删除微信小程序菜单信息
     *
     * @param id 微信小程序菜单主键
     * @return 结果
     */
    @Override
    public int deleteWxMenuById(Integer id) {
        return wxMenuMapper.deleteWxMenuById(id);
    }

    /**
     * 检查用户是否有权限访问指定菜单
     *
     * @param menuId 菜单ID
     * @param userRoles 用户角色列表
     * @return 是否有权限
     */
    @Override
    public boolean checkMenuPermission(Integer menuId, List<SysRole> userRoles) {
        WxMenu menu = wxMenuMapper.selectWxMenuById(menuId);
        return MenuPermissionUtils.hasPermission(menu, userRoles);
    }

    /**
     * 根据菜单类型和用户角色获取菜单列表
     *
     * @param menuType 菜单类型
     * @param userRoles 用户角色列表
     * @return 菜单列表
     */
    @Override
    public List<WxMenuVo> getMenusByType(String menuType, List<SysRole> userRoles) {
        if (!"personal".equals(menuType) && !"function".equals(menuType)) {
            throw new IllegalArgumentException("不支持的菜单类型: " + menuType);
        }
        
        List<WxMenu> menus = wxMenuMapper.selectEnabledMenusByType(menuType);
        List<WxMenu> filteredMenus = MenuPermissionUtils.filterMenusByPermission(menus, userRoles);
        return convertToVoList(filteredMenus);
    }

    /**
     * 将菜单实体列表转换为VO列表
     *
     * @param menus 菜单实体列表
     * @return VO列表
     */
    private List<WxMenuVo> convertToVoList(List<WxMenu> menus) {
        return menus.stream()
                .sorted((m1, m2) -> Integer.compare(m1.getSortOrder(), m2.getSortOrder()))
                .map(this::convertToVo)
                .collect(Collectors.toList());
    }

    /**
     * 将菜单实体转换为VO
     *
     * @param menu 菜单实体
     * @return VO对象
     */
    private WxMenuVo convertToVo(WxMenu menu) {
        WxMenuVo vo = new WxMenuVo();
        BeanUtils.copyBeanProp(vo, menu);
        return vo;
    }
}
