package com.drxin.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.drxin.system.domain.UploadRecord;

public interface IUploadRecordService extends IService<UploadRecord> {

    /**
     * 保存上传记录
     *
     * @param uploadRecord 上传记录
     * @return 上传记录
     */
    int saveUploadRecord(UploadRecord uploadRecord);

    /**
     * 根据ID查询上传记录
     *
     * @param id 上传记录ID
     * @return 上传记录
     */
    UploadRecord getUploadRecordById(Long id);

    /**
     * 根据ID删除上传记录
     *
     * @param id 上传记录ID
     */
    int deleteUploadRecordById(Long id);

    // 修改上传记录状态
    int updateUploadRecordStatus(Long id, String status);

    // 修改上传记录状态为待删除
    void updateUploadRecordStatusToDelete(Long oldId, Long newId);
}
