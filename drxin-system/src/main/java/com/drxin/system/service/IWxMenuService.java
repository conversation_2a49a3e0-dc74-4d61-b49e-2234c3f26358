package com.drxin.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.drxin.common.core.domain.entity.SysRole;
import com.drxin.system.domain.WxMenu;
import com.drxin.system.domain.vo.WxMenuVo;

import java.util.List;

/**
 * 微信小程序菜单Service接口
 *
 * <AUTHOR>
 * @date 2024-07-14
 */
public interface IWxMenuService extends IService<WxMenu> {

    /**
     * 查询微信小程序菜单列表
     *
     * @param wxMenu 微信小程序菜单
     * @return 微信小程序菜单集合
     */
    List<WxMenu> selectWxMenuList(WxMenu wxMenu);

    /**
     * 根据ID查询微信小程序菜单
     *
     * @param id 微信小程序菜单主键
     * @return 微信小程序菜单
     */
    WxMenu selectWxMenuById(Integer id);

    /**
     * 新增微信小程序菜单
     *
     * @param wxMenu 微信小程序菜单
     * @return 结果
     */
    int insertWxMenu(WxMenu wxMenu);

    /**
     * 修改微信小程序菜单
     *
     * @param wxMenu 微信小程序菜单
     * @return 结果
     */
    int updateWxMenu(WxMenu wxMenu);

    /**
     * 批量删除微信小程序菜单
     *
     * @param ids 需要删除的微信小程序菜单主键集合
     * @return 结果
     */
    int deleteWxMenuByIds(Integer[] ids);

    /**
     * 删除微信小程序菜单信息
     *
     * @param id 微信小程序菜单主键
     * @return 结果
     */
    int deleteWxMenuById(Integer id);

    /**
     * 检查用户是否有权限访问指定菜单
     *
     * @param menuId 菜单ID
     * @param userRoles 用户角色列表
     * @return 是否有权限
     */
    boolean checkMenuPermission(Integer menuId, List<SysRole> userRoles);

    /**
     * 根据菜单类型和用户角色获取菜单列表
     *
     * @param menuType 菜单类型
     * @param userRoles 用户角色列表
     * @return 菜单列表
     */
    List<WxMenuVo> getMenusByType(String menuType, List<SysRole> userRoles);
}
