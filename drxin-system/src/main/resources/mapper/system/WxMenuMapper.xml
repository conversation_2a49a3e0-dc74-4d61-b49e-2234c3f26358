<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.system.mapper.WxMenuMapper">
    
    <resultMap type="com.drxin.system.domain.WxMenu" id="WxMenuResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="icon"    column="icon"    />
        <result property="path"    column="path"    />
        <result property="menuType"    column="menu_type"    />
        <result property="showFor"    column="show_for"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="description"    column="description"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectWxMenuVo">
        select id, title, icon, path, menu_type, show_for, sort_order, status, description, create_by, create_time, update_by, update_time from wx_menus
    </sql>

    <select id="selectWxMenuList" parameterType="com.drxin.system.domain.WxMenu" resultMap="WxMenuResult">
        <include refid="selectWxMenuVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="icon != null  and icon != ''"> and icon = #{icon}</if>
            <if test="path != null  and path != ''"> and path like concat('%', #{path}, '%')</if>
            <if test="menuType != null  and menuType != ''"> and menu_type = #{menuType}</if>
            <if test="showFor != null  and showFor != ''"> and show_for like concat('%', #{showFor}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
        order by menu_type, sort_order
    </select>
    
    <select id="selectWxMenuById" parameterType="Integer" resultMap="WxMenuResult">
        <include refid="selectWxMenuVo"/>
        where id = #{id}
    </select>

    <select id="selectEnabledMenusByType" parameterType="String" resultMap="WxMenuResult">
        <include refid="selectWxMenuVo"/>
        where status = 1 and menu_type = #{menuType}
        order by sort_order
    </select>
        
    <insert id="insertWxMenu" parameterType="com.drxin.system.domain.WxMenu" useGeneratedKeys="true" keyProperty="id">
        insert into wx_menus
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="icon != null and icon != ''">icon,</if>
            <if test="path != null and path != ''">path,</if>
            <if test="menuType != null and menuType != ''">menu_type,</if>
            <if test="showFor != null">show_for,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="description != null">description,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="icon != null and icon != ''">#{icon},</if>
            <if test="path != null and path != ''">#{path},</if>
            <if test="menuType != null and menuType != ''">#{menuType},</if>
            <if test="showFor != null">#{showFor},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="description != null">#{description},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWxMenu" parameterType="com.drxin.system.domain.WxMenu">
        update wx_menus
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="icon != null and icon != ''">icon = #{icon},</if>
            <if test="path != null and path != ''">path = #{path},</if>
            <if test="menuType != null and menuType != ''">menu_type = #{menuType},</if>
            <if test="showFor != null">show_for = #{showFor},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="description != null">description = #{description},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime}</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWxMenuById" parameterType="Integer">
        delete from wx_menus where id = #{id}
    </delete>

    <delete id="deleteWxMenuByIds" parameterType="String">
        delete from wx_menus where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
