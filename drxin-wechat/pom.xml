<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.drxin</groupId>
        <artifactId>drxin</artifactId>
        <version>3.8.9</version>
    </parent>

    <artifactId>drxin-wechat</artifactId>

    <dependencies>
        <!-- 通用工具-->
        <dependency>
            <groupId>com.drxin</groupId>
            <artifactId>drxin-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.drxin</groupId>
            <artifactId>drxin-system</artifactId>
        </dependency>
        <dependency>
            <groupId>com.drxin</groupId>
            <artifactId>drxin-framework</artifactId>
        </dependency>
    </dependencies>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

</project>