package com.drxin.wechat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.drxin.wechat.domain.WxCourseAttendance;

import java.util.List;

public interface IWxCourseAttendanceService extends IService<WxCourseAttendance> {

    /**
     * 记录课程考勤
     *
     * @param attendance 考勤记录
     * @return 是否成功
     */
    int recordAttendance(WxCourseAttendance attendance);

    /**
     * 获取用户的课程考勤记录
     *
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 考勤记录列表
     */
    WxCourseAttendance checkCourseAttendStatus(Long userId, Long courseId);
}
