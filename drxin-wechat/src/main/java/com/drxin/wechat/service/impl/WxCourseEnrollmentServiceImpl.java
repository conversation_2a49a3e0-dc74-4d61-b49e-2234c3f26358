package com.drxin.wechat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.drxin.common.exception.ServiceException;
import com.drxin.wechat.domain.WxCourse;
import com.drxin.wechat.domain.WxCourseEnrollment;
import com.drxin.wechat.mapper.WxCourseEnrollmentMapper;
import com.drxin.wechat.service.IWxCourseEnrollmentService;
import com.drxin.wechat.service.IWxCourseService;
import com.drxin.wechat.vo.WxEnrollCourseVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
public class WxCourseEnrollmentServiceImpl extends ServiceImpl<WxCourseEnrollmentMapper, WxCourseEnrollment> implements IWxCourseEnrollmentService {

    @Resource
    private IWxCourseService wxCourseService;

    @Override
    public int insertCourseEnrollmentRecord(WxCourseEnrollment courseEnrollment) {
        WxCourseEnrollment existingRecord = getWxCourseEnrollment(courseEnrollment);
        if (existingRecord != null && "1".equals(existingRecord.getEnrollStatus())) {
            // 如果已存在报名记录且状态为已报名，则不允许重复报名
            throw new ServiceException("您已报名该课程，请勿重复报名");
        }else if(existingRecord != null) {
            // 如果存在报名记录但状态不是已报名，则更新状态
            existingRecord.setEnrollStatus("1"); // 设置为已报名状态
            return baseMapper.updateById(existingRecord);
        }
        // 如果不存在报名记录或状态不是已报名，则插入新的报名记录
        courseEnrollment.setEnrollStatus("1"); // 设置报名状态为已报名
        return baseMapper.insertCourseEnrollmentRecord(courseEnrollment);
    }

    private WxCourseEnrollment getWxCourseEnrollment(WxCourseEnrollment courseEnrollment) {
        // 检查课程是否下架
        if(wxCourseService.isCourseOffShelf(courseEnrollment.getCourseId())) {
            throw new ServiceException("该课程已下架，无法报名");
        }

        // 检查是否存在报名记录
        QueryWrapper<WxCourseEnrollment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", courseEnrollment.getUserId())
                    .eq("course_id", courseEnrollment.getCourseId())
                .select("id", "user_id", "course_id", "enroll_status");
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public boolean checkEnrollmentExists(Long userId, Long courseId) {
        QueryWrapper<WxCourseEnrollment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                    .eq("course_id", courseId)
                    .eq("enroll_status", "1")
                .select("id", "user_id", "course_id"); // 检查是否已报名
        WxCourseEnrollment existingRecord = baseMapper.selectOne(queryWrapper);
        // 如果存在已报名记录
        return existingRecord != null;
    }

    @Override
    public List<WxEnrollCourseVo> enrollCourseList(Long userId) {
        return baseMapper.enrollCourseList(userId);
    }

    @Override
    public int cancelEnrollment(WxCourseEnrollment enrollment) {
        // 检查课程是否下架
        WxCourseEnrollment existingRecord = getWxCourseEnrollment(enrollment);
        if (existingRecord == null) {
            // 如果不存在报名记录，则抛出异常
            throw new ServiceException("您尚未报名该课程，无法取消报名");
        }
        // 如果存在报名记录，则更新状态为未报名
        existingRecord.setCancelTime(new Date());
        existingRecord.setEnrollStatus("0"); // 设置为未报名状态
        // 设置更新字段
        UpdateWrapper<WxCourseEnrollment> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("user_id", enrollment.getUserId())
                  .eq("course_id", enrollment.getCourseId())
              .set("cancel_time", new Date())
              .set("enroll_status", "0"); // 设置为未报名状态
        return baseMapper.update(null, updateWrapper);
    }


}
