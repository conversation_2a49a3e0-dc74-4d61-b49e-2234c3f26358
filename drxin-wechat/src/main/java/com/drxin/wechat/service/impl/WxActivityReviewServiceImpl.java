package com.drxin.wechat.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.drxin.common.core.domain.entity.SysUser;
import com.drxin.common.core.domain.model.LoginUser;
import com.drxin.common.exception.ServiceException;
import com.drxin.common.utils.DateUtils;
import com.drxin.common.utils.SecurityUtils;
import com.drxin.wechat.domain.WxActivityReview;
import com.drxin.wechat.domain.WxActivityReviewAttachment;
import com.drxin.wechat.domain.WxActivityReviewUser;
import com.drxin.wechat.mapper.WxActivityReviewMapper;
import com.drxin.wechat.mapper.WxActivityReviewAttachmentMapper;
import com.drxin.wechat.mapper.WxActivityReviewUserMapper;
import com.drxin.wechat.service.IWxActivityReviewService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 微信活动评审Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Service
public class WxActivityReviewServiceImpl extends ServiceImpl<WxActivityReviewMapper, WxActivityReview> implements IWxActivityReviewService {

    @Resource
    private WxActivityReviewMapper wxActivityReviewMapper;

    @Resource
    private WxActivityReviewAttachmentMapper wxActivityReviewAttachmentMapper;

    @Resource
    private WxActivityReviewUserMapper wxActivityReviewUserMapper;

    /**
     * 查询活动评审列表
     *
     * @param activityReview 活动评审查询条件
     * @return 活动评审
     */
    @Override
    public List<WxActivityReview> selectActivityReviewList(WxActivityReview activityReview) {
        // 当前用户是否为管理员或运营
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser currentUser = loginUser.getUser();

        // 根据用户权限设置查询条件
        if (!currentUser.isAdmin() && !currentUser.isOperator()) {
            // 普通用户只能查看自己创建的活动
            activityReview.setCreateBy(SecurityUtils.getUsername());
        }

        // 查询活动列表
        List<WxActivityReview> activityReviews = wxActivityReviewMapper.selectActivityReviewList(activityReview);
        
        // 加载主办人和协办人信息
        loadUsersInfo(activityReviews);

        return activityReviews;
    }

    /**
     * 保存活动评审
     * 
     * @param activityReview 活动评审
     * @return 结果
     */
    @Override
    @Transactional
    public int saveActivityReview(WxActivityReview activityReview) {
        // 设置默认状态为新建
        if (activityReview.getReviewStatus() == null) {
            activityReview.setReviewStatus("1");
        }

        // 如果要提交（状态为2），需要验证必填字段
        if ("2".equals(activityReview.getReviewStatus())) {
            validateActivityReviewForSubmit(activityReview);
        }

        activityReview.setCreateTime(DateUtils.getNowDate());
        activityReview.setCreateBy(SecurityUtils.getUsername());
        
        int result = wxActivityReviewMapper.insertActivityReview(activityReview);

        // 保存附件信息
        if (result > 0 && activityReview.getAttachments() != null && !activityReview.getAttachments().isEmpty()) {
            for (WxActivityReviewAttachment attachment : activityReview.getAttachments()) {
                attachment.setActivityId(activityReview.getId());
                attachment.setCreateTime(DateUtils.getNowDate());
                attachment.setCreateBy(SecurityUtils.getUsername());
            }
            wxActivityReviewAttachmentMapper.insertBatch(activityReview.getAttachments());
        }

        // 保存主办人和协办人信息
        if (result > 0) {
            List<WxActivityReviewUser> allUsers = new ArrayList<>();

            // 添加主办人
            if (activityReview.getOrganizers() != null && !activityReview.getOrganizers().isEmpty()) {
                activityReview.getOrganizers().forEach(organizer -> {
                    organizer.setActivityId(activityReview.getId());
                    organizer.setRoleType(0); // 主办人
                    organizer.setCreateTime(DateUtils.getNowDate());
                    organizer.setCreateBy(SecurityUtils.getUsername());
                });
                allUsers.addAll(activityReview.getOrganizers());
            }

            // 添加协办人
            if (activityReview.getAssistants() != null && !activityReview.getAssistants().isEmpty()) {
                activityReview.getAssistants().forEach(assistant -> {
                    assistant.setActivityId(activityReview.getId());
                    assistant.setRoleType(1); // 协办人
                    assistant.setCreateTime(DateUtils.getNowDate());
                    assistant.setCreateBy(SecurityUtils.getUsername());
                });
                allUsers.addAll(activityReview.getAssistants());
            }

            // 批量保存
            if (!allUsers.isEmpty()) {
                wxActivityReviewUserMapper.insertBatch(allUsers);
            }
        }

        return result;
    }

    /**
     * 修改活动评审
     * 
     * @param activityReview 活动评审
     * @return 结果
     */
    @Override
    @Transactional
    public int updateActivityReview(WxActivityReview activityReview) {
        // 检查活动是否存在且属于当前用户
        WxActivityReview existingActivity = wxActivityReviewMapper.getActivityReviewInfo(activityReview.getId());
        if (existingActivity == null) {
            throw new ServiceException("活动不存在");
        }
        
        String currentUser = SecurityUtils.getUsername();
        if (!existingActivity.getCreateBy().equals(currentUser)) {
            throw new ServiceException("只能修改自己创建的活动");
        }
        
        // 只有草稿状态或者驳回的活动才能修改
        if (!"1".equals(existingActivity.getReviewStatus()) && !"4".equals(existingActivity.getReviewStatus())) {
            throw new ServiceException("只有草稿状态的活动才能修改");
        }

        // 如果要提交（状态为2），需要验证必填字段
        if ("2".equals(activityReview.getReviewStatus())) {
            validateActivityReviewForSubmit(activityReview);
        }

        activityReview.setUpdateTime(DateUtils.getNowDate());
        activityReview.setUpdateBy(SecurityUtils.getUsername());
        
        int result = wxActivityReviewMapper.updateActivityReview(activityReview);

        // 更新附件信息
        if (result > 0 && activityReview.getAttachments() != null) {
            // 先删除原有的附件记录
            wxActivityReviewAttachmentMapper.deleteAttachmentsByActivityId(activityReview.getId());

            // 重新插入附件记录
            if (!activityReview.getAttachments().isEmpty()) {
                for (WxActivityReviewAttachment attachment : activityReview.getAttachments()) {
                    attachment.setActivityId(activityReview.getId());
                    attachment.setCreateTime(DateUtils.getNowDate());
                    attachment.setCreateBy(SecurityUtils.getUsername());
                }
                wxActivityReviewAttachmentMapper.insertBatch(activityReview.getAttachments());
            }
        }

        // 更新人员信息
        if (result > 0) {
            // 先删除原有的人员关联记录
            wxActivityReviewUserMapper.deleteUsersByActivityId(activityReview.getId());

            List<WxActivityReviewUser> allUsers = new ArrayList<>();

            // 添加主办人
            if (activityReview.getOrganizers() != null && !activityReview.getOrganizers().isEmpty()) {
                activityReview.getOrganizers().forEach(organizer -> {
                    organizer.setActivityId(activityReview.getId());
                    organizer.setRoleType(0); // 主办人
                    organizer.setCreateTime(DateUtils.getNowDate());
                    organizer.setCreateBy(SecurityUtils.getUsername());
                });
                allUsers.addAll(activityReview.getOrganizers());
            }

            // 添加协办人
            if (activityReview.getAssistants() != null && !activityReview.getAssistants().isEmpty()) {
                activityReview.getAssistants().forEach(assistant -> {
                    assistant.setActivityId(activityReview.getId());
                    assistant.setRoleType(1); // 协办人
                    assistant.setCreateTime(DateUtils.getNowDate());
                    assistant.setCreateBy(SecurityUtils.getUsername());
                });
                allUsers.addAll(activityReview.getAssistants());
            }

            // 批量保存
            if (!allUsers.isEmpty()) {
                wxActivityReviewUserMapper.insertBatch(allUsers);
            }
        }

        return result;
    }

    /**
     * 根据ID查询活动评审详情
     * 
     * @param id 活动评审ID
     * @return 活动评审
     */
    @Override
    public WxActivityReview getActivityReviewInfo(Long id) {
        WxActivityReview activityReview = wxActivityReviewMapper.getActivityReviewInfo(id);
        if (activityReview == null) {
            return null;
        }

        // 检查权限：管理员和运营可以查看所有，普通用户只能查看自己的
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser currentUser = loginUser.getUser();
        if (!currentUser.isAdmin() && !currentUser.isOperator()) {
            String currentUsername = SecurityUtils.getUsername();
            if (!activityReview.getCreateBy().equals(currentUsername)) {
                throw new ServiceException("无权限查看此活动");
            }
        }

        // 加载附件列表和人员列表
        loadAttachmentsAndUsers(Arrays.asList(activityReview));

        return activityReview;
    }

    /**
     * 批量删除活动评审
     * 
     * @param ids 需要删除的活动评审ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteActivityReviewByIds(Long[] ids) {
        // 检查权限和状态
        String currentUser = SecurityUtils.getUsername();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        
        for (Long id : ids) {
            WxActivityReview existingActivity = wxActivityReviewMapper.getActivityReviewInfo(id);
            if (existingActivity == null) {
                continue;
            }
            
            // 非管理员只能删除自己的草稿状态活动
            if (!user.isAdmin() && !user.isOperator()) {
                if (!existingActivity.getCreateBy().equals(currentUser)) {
                    throw new ServiceException("只能删除自己创建的活动");
                }
                if (!"1".equals(existingActivity.getReviewStatus())) {
                    throw new ServiceException("只能删除草稿状态的活动");
                }
            }
        }

        // 删除相关的附件和人员关联记录
        for (Long id : ids) {
            wxActivityReviewAttachmentMapper.deleteAttachmentsByActivityId(id);
            wxActivityReviewUserMapper.deleteUsersByActivityId(id);
        }

        return wxActivityReviewMapper.deleteActivityReviewByIds(ids);
    }

    /**
     * 验证活动提交时的必填字段
     */
    private void validateActivityReviewForSubmit(WxActivityReview activityReview) {
        if (activityReview.getActivityTitle() == null || activityReview.getActivityTitle().trim().isEmpty()) {
            throw new ServiceException("活动主题不能为空");
        }
        if (activityReview.getStartDate() == null) {
            throw new ServiceException("开始时间不能为空");
        }
        if (activityReview.getEndDate() == null) {
            throw new ServiceException("结束时间不能为空");
        }
        if (activityReview.getLecturerName() == null || activityReview.getLecturerName().trim().isEmpty()) {
            throw new ServiceException("讲师姓名不能为空");
        }
        if (activityReview.getAttendeeCount() == null || activityReview.getAttendeeCount() <= 0) {
            throw new ServiceException("参与人数必须大于0");
        }
    }

    /**
     * 加载主办人和协办人信息（列表查询用）
     */
    private void loadUsersInfo(List<WxActivityReview> activityReviews) {
        for (WxActivityReview activity : activityReviews) {
            // 查询所有人员，然后按角色类型分组
            List<WxActivityReviewUser> allUsers = wxActivityReviewUserMapper.selectUsersByActivityId(activity.getId());

            // 使用Stream按角色类型分组
            List<WxActivityReviewUser> organizers = allUsers.stream()
                    .filter(user -> user.getRoleType() == 0)
                    .collect(Collectors.toList());
            List<WxActivityReviewUser> assistants = allUsers.stream()
                    .filter(user -> user.getRoleType() == 1)
                    .collect(Collectors.toList());

            activity.setOrganizers(organizers);
            activity.setAssistants(assistants);
        }
    }

    /**
     * 加载附件和人员信息（详情查询用）
     */
    private void loadAttachmentsAndUsers(List<WxActivityReview> activityReviews) {
        for (WxActivityReview activity : activityReviews) {
            // 查询附件
            List<WxActivityReviewAttachment> attachments = wxActivityReviewAttachmentMapper.selectAttachmentsByActivityId(activity.getId());
            activity.setAttachments(attachments);

            // 查询所有人员，然后按角色类型分组
            List<WxActivityReviewUser> allUsers = wxActivityReviewUserMapper.selectUsersByActivityId(activity.getId());

            // 使用Stream按角色类型分组
            List<WxActivityReviewUser> organizers = allUsers.stream()
                    .filter(user -> user.getRoleType() == 0)
                    .collect(Collectors.toList());
            List<WxActivityReviewUser> assistants = allUsers.stream()
                    .filter(user -> user.getRoleType() == 1)
                    .collect(Collectors.toList());

            activity.setOrganizers(organizers);
            activity.setAssistants(assistants);
        }
    }
}
