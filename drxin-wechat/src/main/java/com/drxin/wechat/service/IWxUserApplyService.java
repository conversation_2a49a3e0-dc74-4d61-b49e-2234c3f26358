package com.drxin.wechat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.drxin.wechat.domain.WxUserApply;

public interface IWxUserApplyService extends IService<WxUserApply> {

    /**
     * 根据用户ID和用户类型查询用户申请信息
     *
     * @param userId 用户ID
     * @param userType 用户类型
     * @return 用户申请信息
     */
    WxUserApply selectUserApplyByUserIdAndType(Long userId, String userType);

    /**
     * 新增用户身份申请
     *
     * @param wxUserApply 用户身份申请
     * @return 结果
     */
    int insertUserApply(WxUserApply wxUserApply);

    /**
     * 修改用户身份申请
     *
     * @param wxUserApply 用户身份申请
     * @return 结果
     */
    int updateUserApply(WxUserApply wxUserApply);

    /**
     * 检查用户是否已有申请记录
     *
     * @param userId 用户ID
     * @return 是否存在申请记录
     */
    boolean hasUserApply(Long userId);

    /**
     * 检查用户是否已有审核中的申请记录
     *
     * @param userId 用户ID
     * @return 是否存在申请记录
     */
    boolean hasPendingApply(Long userId);

    /**
     * 更新申请状态
     *
     * @param id 申请ID
     * @param status 申请状态
     * @return 结果
     */
    int updateApplyStatus(Long id, String status);

}
