package com.drxin.wechat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.drxin.wechat.domain.WxCourse;

import java.util.List;

public interface IWxCourseService extends IService<WxCourse> {

    // 定义课程相关的服务方法，例如查询课程列表、获取课程详情等
    // 例如：
     List<WxCourse> selectWxCourseList(WxCourse wxCourse);
     WxCourse getWxCourseById(Long id);

    // 查询课程详情，并判断课程是否下架
    boolean isCourseOffShelf(Long courseId);
}
