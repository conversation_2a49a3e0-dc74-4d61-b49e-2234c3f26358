package com.drxin.wechat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.drxin.wechat.domain.WxCourse;
import com.drxin.wechat.domain.WxCourseEnrollment;
import com.drxin.wechat.vo.WxEnrollCourseVo;

import java.util.List;

public interface IWxCourseEnrollmentService extends IService<WxCourseEnrollment> {

    /**
     * 插入课程报名记录
     *
     * @param courseEnrollment 课程报名记录
     * @return 插入结果
     */
    int insertCourseEnrollmentRecord(WxCourseEnrollment courseEnrollment);

    boolean checkEnrollmentExists(Long userId, Long courseId);

    List<WxEnrollCourseVo> enrollCourseList(Long userId);

    int cancelEnrollment(WxCourseEnrollment enrollment);
}
