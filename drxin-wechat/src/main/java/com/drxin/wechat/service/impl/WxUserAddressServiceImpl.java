package com.drxin.wechat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.drxin.common.exception.ServiceException;
import com.drxin.common.utils.DateUtils;
import com.drxin.common.utils.SecurityUtils;
import com.drxin.wechat.domain.WxUserAddress;
import com.drxin.wechat.mapper.WxUserAddressMapper;
import com.drxin.wechat.service.IWxUserAddressService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户地址管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-02
 */
@Service
public class WxUserAddressServiceImpl extends ServiceImpl<WxUserAddressMapper, WxUserAddress>  implements IWxUserAddressService {

    /**
     * 查询用户地址管理
     * 
     * @param id 用户地址管理主键
     * @return 用户地址管理
     */
    @Override
    public WxUserAddress selectUserAddressById(String id) {
        return baseMapper.selectUserAddressById(id);
    }

    /**
     * 查询用户地址管理列表
     * 
     * @param userAddress 用户地址管理
     * @return 用户地址管理
     */
    @Override
    public List<WxUserAddress> selectUserAddressList(WxUserAddress userAddress) {
        userAddress.setUserId(SecurityUtils.getUserId());
        return baseMapper.selectUserAddressList(userAddress);
    }

    /**
     * 新增用户地址管理
     * 
     * @param userAddress 用户地址管理
     * @return 结果
     */
    @Override
    public int insertUserAddress(WxUserAddress userAddress) {
        Long userId = SecurityUtils.getUserId();
        checkUserDefault(userAddress, userId);
        userAddress.setUserId(userId);
        return baseMapper.insertUserAddress(userAddress);
    }

    private void checkUserDefault(WxUserAddress userAddress, Long userId) {
        // 判断新增地址是否是默认地址
        if("Y".equals(userAddress.getDefaultFlag())){
            // 校验库中是否有默认地址
            QueryWrapper<WxUserAddress> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            queryWrapper.eq("default_flag", "Y");
            Long count = baseMapper.selectCount(queryWrapper);
            if( count > 0){
                throw new ServiceException("用户已有默认地址，不能重复设置");
            }
        }
    }

    /**
     * 修改用户地址管理
     * 
     * @param userAddress 用户地址管理
     * @return 结果
     */
    @Override
    public int updateUserAddress(WxUserAddress userAddress) {
        checkUserDefault(userAddress, userAddress.getUserId());
        return baseMapper.updateUserAddress(userAddress);
    }

    /**
     * 批量删除用户地址管理
     * 
     * @param ids 需要删除的用户地址管理主键
     * @return 结果
     */
    @Override
    public int deleteUserAddressByIds(String[] ids) {
        return baseMapper.deleteUserAddressByIds(ids);
    }

    /**
     * 删除用户地址管理信息
     * 
     * @param id 用户地址管理主键
     * @return 结果
     */
    @Override
    public int deleteUserAddressById(String id) {
        return baseMapper.deleteUserAddressById(id);
    }
}
