package com.drxin.wechat.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.drxin.common.core.domain.entity.SysUser;
import com.drxin.common.core.domain.model.LoginUser;
import com.drxin.common.exception.ServiceException;
import com.drxin.common.utils.DateUtils;
import com.drxin.common.utils.SecurityUtils;
import com.drxin.wechat.domain.WxRescueCase;
import com.drxin.wechat.domain.WxRescueCaseAttachment;
import com.drxin.wechat.domain.WxRescueCaseRescuer;
import com.drxin.wechat.mapper.WxRescueCaseAttachmentMapper;
import com.drxin.wechat.mapper.WxRescueCaseMapper;
import com.drxin.wechat.mapper.WxRescueCaseRescuerMapper;
import com.drxin.wechat.service.IWxRescueCaseService;
import com.drxin.wechat.vo.WxRescueCaseVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 微信急救案例Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-05
 */
@Service
public class WxRescueCaseServiceImpl extends ServiceImpl<WxRescueCaseMapper, WxRescueCase> implements IWxRescueCaseService {

    @Resource
    private WxRescueCaseMapper wxRescueCaseMapper;

    @Resource
    private WxRescueCaseRescuerMapper wxRescueCaseRescuerMapper;

    @Resource
    private WxRescueCaseAttachmentMapper wxRescueCaseAttachmentMapper;

    /**
     * 查询急救案例列表
     *
     * @param rescueCaseVo 急救案例查询条件
     * @return 急救案例
     */
    @Override
    public List<WxRescueCase> selectRescueCaseList(WxRescueCaseVo rescueCaseVo) {
        // 当前用户是否为管理员或运营
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser currentUser = loginUser.getUser();

        // 根据用户权限设置查询条件
        if (!currentUser.isAdmin() && !currentUser.isOperator()) {
            // 普通用户只能查看自己创建的案例
            rescueCaseVo.setCreateBy(SecurityUtils.getUsername());
        }

        // 微信模块列表查询不需要加载急救员和附件信息，提高查询性能
        return wxRescueCaseMapper.selectRescueCaseList(rescueCaseVo);
    }

    /**
     * 保存急救案例
     * 
     * @param rescueCase 急救案例
     * @return 结果
     */
    @Override
    @Transactional
    public int saveRescueCase(WxRescueCase rescueCase) {
        rescueCase.setCreateBy(SecurityUtils.getUsername());
        rescueCase.setCreateTime(DateUtils.getNowDate());

        // 如果没有设置状态，默认为草稿状态
        if (rescueCase.getRescueStatus() == null || rescueCase.getRescueStatus().trim().isEmpty()) {
            rescueCase.setRescueStatus("1"); // 1-草稿状态
        }

        // 如果要提交（状态为2），需要验证必填字段
        if ("2".equals(rescueCase.getRescueStatus())) {
            validateRescueCaseForSubmit(rescueCase);
        }

        int result = wxRescueCaseMapper.insertRescueCase(rescueCase);

        // 批量保存急救员信息和附件信息
        if (result > 0) {
            String currentUser = SecurityUtils.getUsername();
            Date currentTime = DateUtils.getNowDate();

            batchSaveRescuers(rescueCase.getId(), rescueCase.getRescuers(), currentUser, currentTime);
            batchSaveAttachments(rescueCase.getId(), rescueCase.getAttachments(), currentUser, currentTime);
        }

        return result;
    }

    /**
     * 修改急救案例
     * 
     * @param rescueCase 急救案例
     * @return 结果
     */
    @Override
    @Transactional
    public int updateRescueCase(WxRescueCase rescueCase) {
        // 检查案例是否存在且属于当前用户
        WxRescueCase existingCase = wxRescueCaseMapper.getRescueCaseInfo(rescueCase.getId());
        if (existingCase == null) {
            throw new ServiceException("急救案例不存在");
        }
        
        String currentUser = SecurityUtils.getUsername();
        if (!existingCase.getCreateBy().equals(currentUser)) {
            throw new ServiceException("只能修改自己创建的急救案例");
        }
        
        // 只有草稿状态或者驳回的案例才能修改
        if (!"1".equals(existingCase.getRescueStatus()) && !"4".equals(existingCase.getRescueStatus())) {
            throw new ServiceException("只有草稿状态的案例才能修改");
        }

        // 如果要提交（状态为2），需要验证必填字段
        if ("2".equals(rescueCase.getRescueStatus())) {
            validateRescueCaseForSubmit(rescueCase);
        }

        rescueCase.setUpdateBy(currentUser);
        rescueCase.setUpdateTime(DateUtils.getNowDate());
        int result = wxRescueCaseMapper.updateRescueCase(rescueCase);

        // 批量更新急救员信息和附件信息
        if (result > 0) {
            Date currentTime = DateUtils.getNowDate();

            // 更新急救员信息
            if (rescueCase.getRescuers() != null) {
                wxRescueCaseRescuerMapper.deleteRescuersByCaseId(rescueCase.getId());
                batchSaveRescuers(rescueCase.getId(), rescueCase.getRescuers(), currentUser, currentTime);
            }

            // 更新附件信息
            if (rescueCase.getAttachments() != null) {
                wxRescueCaseAttachmentMapper.deleteAttachmentsByCaseId(rescueCase.getId());
                batchSaveAttachments(rescueCase.getId(), rescueCase.getAttachments(), currentUser, currentTime);
            }
        }

        return result;
    }

    /**
     * 验证急救案例提交时的必填字段
     *
     * @param rescueCase 急救案例
     */
    private void validateRescueCaseForSubmit(WxRescueCase rescueCase) {
        if (rescueCase.getRescueDate() == null) {
            throw new ServiceException("急救日期不能为空");
        }
        if (rescueCase.getCity() == null || rescueCase.getCity().trim().isEmpty()) {
            throw new ServiceException("急救所在城市不能为空");
        }
        if (rescueCase.getAddress() == null || rescueCase.getAddress().trim().isEmpty()) {
            throw new ServiceException("详细地址不能为空");
        }
        if (rescueCase.getPatientName() == null || rescueCase.getPatientName().trim().isEmpty()) {
            throw new ServiceException("被救人姓名不能为空");
        }
    }

    /**
     * 根据ID查询急救案例详情
     * 
     * @param id 急救案例ID
     * @return 急救案例
     */
    @Override
    public WxRescueCase getRescueCaseInfo(Long id) {
        WxRescueCase rescueCase = wxRescueCaseMapper.getRescueCaseInfo(id);
        if (rescueCase == null) {
            return null;
        }

        // 检查权限：管理员和运营可以查看所有，普通用户只能查看自己的
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser currentUser = loginUser.getUser();
        if (!currentUser.isAdmin() && !currentUser.isOperator()) {
            String currentUsername = SecurityUtils.getUsername();
            if (!rescueCase.getCreateBy().equals(currentUsername)) {
                throw new ServiceException("无权限查看此急救案例");
            }
        }

        // 加载急救员列表和附件列表
        loadRescuersAndAttachments(Arrays.asList(rescueCase));

        return rescueCase;
    }

    /**
     * 批量删除急救案例
     * 
     * @param ids 需要删除的急救案例ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteRescueCaseByIds(Long[] ids) {
        // 检查权限和状态
        String currentUser = SecurityUtils.getUsername();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        
        for (Long id : ids) {
            WxRescueCase existingCase = wxRescueCaseMapper.getRescueCaseInfo(id);
            if (existingCase == null) {
                continue;
            }
            
            // 非管理员只能删除自己的草稿状态案例
            if (!user.isAdmin() && !user.isOperator()) {
                if (!existingCase.getCreateBy().equals(currentUser)) {
                    throw new ServiceException("只能删除自己创建的急救案例");
                }
                if (!"1".equals(existingCase.getRescueStatus())) {
                    throw new ServiceException("只能删除草稿状态的案例");
                }
            }
        }
        
        // 先删除相关的急救员记录和附件记录
        wxRescueCaseRescuerMapper.delete(new LambdaUpdateWrapper<WxRescueCaseRescuer>()
                .in(WxRescueCaseRescuer::getCaseId, Arrays.asList(ids)));
        wxRescueCaseAttachmentMapper.delete(new LambdaUpdateWrapper<WxRescueCaseAttachment>()
                .in(WxRescueCaseAttachment::getCaseId, Arrays.asList(ids)));

        return wxRescueCaseMapper.deleteRescueCaseByIds(ids);
    }

    /**
     * 批量加载急救员列表和附件列表
     *
     * @param rescueCases 急救案例列表
     */
    private void loadRescuersAndAttachments(List<WxRescueCase> rescueCases) {
        for (WxRescueCase rescueCase : rescueCases) {
            // 查询急救员列表
            List<WxRescueCaseRescuer> rescuers = wxRescueCaseRescuerMapper.selectRescuersByCaseId(rescueCase.getId());
            rescueCase.setRescuers(rescuers);

            // 查询附件列表
            List<WxRescueCaseAttachment> attachments = wxRescueCaseAttachmentMapper.selectAttachmentsByCaseId(rescueCase.getId());
            rescueCase.setAttachments(attachments);
        }
    }

    /**
     * 批量保存急救员信息
     *
     * @param caseId 案例ID
     * @param rescuers 急救员列表
     * @param createBy 创建者
     * @param createTime 创建时间
     */
    private void batchSaveRescuers(Long caseId, List<WxRescueCaseRescuer> rescuers, String createBy, Date createTime) {
        if (rescuers != null && !rescuers.isEmpty()) {
            rescuers.forEach(rescuer -> {
                rescuer.setCaseId(caseId);
                rescuer.setCreateBy(createBy);
                rescuer.setCreateTime(createTime);
            });
            wxRescueCaseRescuerMapper.insertBatch(rescuers);
        }
    }

    /**
     * 批量保存附件信息
     *
     * @param caseId 案例ID
     * @param attachments 附件列表
     * @param createBy 创建者
     * @param createTime 创建时间
     */
    private void batchSaveAttachments(Long caseId, List<WxRescueCaseAttachment> attachments, String createBy, Date createTime) {
        if (attachments != null && !attachments.isEmpty()) {
            attachments.forEach(attachment -> {
                attachment.setCaseId(caseId);
                attachment.setCreateBy(createBy);
                attachment.setCreateTime(createTime);
            });
            wxRescueCaseAttachmentMapper.insertBatch(attachments);
        }
    }
}
