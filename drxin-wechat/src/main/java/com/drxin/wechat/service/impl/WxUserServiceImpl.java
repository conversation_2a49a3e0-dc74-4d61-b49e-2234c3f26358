package com.drxin.wechat.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.drxin.common.exception.ServiceException;
import com.drxin.common.utils.Base64Util;
import com.drxin.common.utils.QrCodeUtils;
import com.drxin.common.utils.SecurityUtils;
import com.drxin.common.utils.WeChatMiniProgramUtil;
import com.drxin.common.utils.file.TencentOssUtils;
import com.drxin.system.service.IUploadRecordService;
import com.drxin.wechat.domain.WxUser;
import com.drxin.wechat.mapper.WxUserMapper;
import com.drxin.wechat.service.IWxUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.util.*;

@Service
public class WxUserServiceImpl extends ServiceImpl<WxUserMapper, WxUser> implements IWxUserService {

    @Resource
    private WeChatMiniProgramUtil weChatMiniProgramUtil;

    @Resource
    private IUploadRecordService uploadRecordService;

    /**
     * 更新用户资料
     *
     * @param wxUser wechatUser
     * @return 更新条数
     */
    @Override
    public int updateUserProfile(WxUser wxUser) {
        if (wxUser.getUserId() == null) {
            throw new ServiceException("用户ID不能为空");
        }
        if (wxUser.getNickName() == null) {
            throw new ServiceException("昵称不能为空");
        }
        if (wxUser.getAvatar() == null) {
            throw new ServiceException("头像不能为空");
        }
        baseMapper.updateUserProfile(wxUser);
        return 0;
    }

    /**
     * 获取用户个人信息
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    @Override
    public WxUser getPersonalInfo(String userId) {
        QueryWrapper<WxUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.select("user_id", "sex", "real_name", "id_card", "education", "company_name", "photo_url",
                "phonenumber", "photo_id", "contribution", "inviter_code", "deal_inviter_id");
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updatePersonalInfo(WxUser wxUser) {
        QueryWrapper<WxUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", wxUser.getUserId());
        queryWrapper.select("photo_id", "user_id");
//        Long oldPhotoId = baseMapper.selectOne(queryWrapper).getPhotoId();
//        uploadRecordService.updateUploadRecordStatusToDelete(oldPhotoId, wxUser.getPhotoId());
        UpdateWrapper<WxUser> wrapper = new UpdateWrapper<>();
        wrapper.eq("user_id", wxUser.getUserId())
                .set("real_name", wxUser.getRealName())
                .set("id_card", wxUser.getIdCard())
                .set("sex", wxUser.getSex())
                .set("education", wxUser.getEducation())
                .set("phonenumber", wxUser.getPhonenumber())
                .set("company_name", wxUser.getCompanyName())
                .set("photo_url", wxUser.getPhotoUrl())
                .set("photo_id", wxUser.getPhotoId())
                .set("update_time", new Date());
        return baseMapper.update(null, wrapper);
    }

    @Override
    public String selectPersonalQrCode(String userId) {
        QueryWrapper<WxUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.select("user_id", "inviter_code", "openid");
        WxUser wxUser = baseMapper.selectOne(queryWrapper);
        if (wxUser == null) {
            throw new ServiceException("用户不存在");
        }
        if (wxUser.getInviterCode() == null) {
            // 如果没有邀请码，则生成一个新的邀请码
            String courseQRUrl = getInviterUrl(userId, wxUser);
            // 上传到OSS
            wxUser.setInviterCode(courseQRUrl);
            baseMapper.updateById(wxUser);
            return courseQRUrl;
        }
        return wxUser.getInviterCode();
    }

    @Override
    public JSONObject getIDCardInfo(MultipartFile file) {
        File tempFile = null;
        try {
            // 1. 创建临时文件（前缀至少3位，后缀根据实际文件类型写，比如 .jpg）
            String originalFilename = file.getOriginalFilename();
            String suffix = originalFilename != null && originalFilename.contains(".")
                    ? originalFilename.substring(originalFilename.lastIndexOf("."))
                    : ".tmp";
            tempFile = File.createTempFile("idcard_", suffix);
            // 2. 将 MultipartFile 内容写入临时文件
            file.transferTo(tempFile);
            // 3. 调用工具方法
            return weChatMiniProgramUtil.getIDCardInfo(tempFile);
        } catch (IOException e) {
            log.error("获取身份证信息失败", e);
            throw new ServiceException("获取身份证信息失败");
        } finally {
            // 4. 删除临时文件（保险起见延迟删除）
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete(); // 立即删除
            }
        }
    }

    @Override
    public String regenerateInviterCode(String userId) {
        QueryWrapper<WxUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.select("user_id", "openid", "inviter_code");
        WxUser wxUser = baseMapper.selectOne(queryWrapper);
        if (wxUser == null) {
            throw new ServiceException("用户不存在");
        }
        if (wxUser.getInviterCode() != null) {
            // 如果已经有邀请码，则先删除旧的
            String oldCode = wxUser.getInviterCode();
            if (!oldCode.isEmpty()) {
                try {
                    TencentOssUtils.deleteFile(oldCode);
                } catch (Exception e) {
                    log.error("删除旧邀请码失败", e);
                }
            }
        }
        String inviterUrl = getInviterUrl(userId, wxUser);
        // 更新用户的邀请二维码
        wxUser.setInviterCode(inviterUrl);
        baseMapper.updateById(wxUser);
        return inviterUrl;
    }

    @Override
    public List<WxUser> getRecommendList(WxUser wxUser) {
        QueryWrapper <WxUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deal_inviter_id", SecurityUtils.getUserId());
        if (wxUser.getRealName() != null) {
            queryWrapper.like("real_name", wxUser.getRealName());
        }
        queryWrapper.select("real_name", "user_type", "upgraded_time");
        queryWrapper.orderByDesc("upgraded_time");
        return baseMapper.selectList(queryWrapper);
    }

    private static String getInviterUrl(String userId, WxUser wxUser) {
        String pagePath = "pageA/invited_register/index";
        String scene = "inviterId=" + userId;
        String fileName = userId + "_inviter_code" + wxUser.getOpenid() + ".png";
        return QrCodeUtils.generateAndUploadWxQrCode(pagePath, scene, fileName);
    }
}