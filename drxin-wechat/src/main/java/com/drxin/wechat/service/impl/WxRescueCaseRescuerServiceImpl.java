package com.drxin.wechat.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.drxin.common.utils.DateUtils;
import com.drxin.common.utils.SecurityUtils;
import com.drxin.wechat.domain.WxRescueCaseRescuer;
import com.drxin.wechat.mapper.WxRescueCaseRescuerMapper;
import com.drxin.wechat.service.IWxRescueCaseRescuerService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 微信急救案例急救成员Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-05
 */
@Service
public class WxRescueCaseRescuerServiceImpl extends ServiceImpl<WxRescueCaseRescuerMapper, WxRescueCaseRescuer> implements IWxRescueCaseRescuerService {

    @Resource
    private WxRescueCaseRescuerMapper wxRescueCaseRescuerMapper;

    /**
     * 查询急救案例急救成员列表
     * 
     * @param rescueCaseRescuer 急救案例急救成员
     * @return 急救案例急救成员
     */
    @Override
    public List<WxRescueCaseRescuer> selectRescueCaseRescuerList(WxRescueCaseRescuer rescueCaseRescuer) {
        return wxRescueCaseRescuerMapper.selectRescueCaseRescuerList(rescueCaseRescuer);
    }

    /**
     * 根据案例ID查询急救成员列表
     * 
     * @param caseId 急救案例ID
     * @return 急救成员集合
     */
    @Override
    public List<WxRescueCaseRescuer> selectRescuersByCaseId(Long caseId) {
        return wxRescueCaseRescuerMapper.selectRescuersByCaseId(caseId);
    }

    /**
     * 新增急救案例急救成员
     * 
     * @param rescueCaseRescuer 急救案例急救成员
     * @return 结果
     */
    @Override
    @Transactional
    public int insertRescueCaseRescuer(WxRescueCaseRescuer rescueCaseRescuer) {
        rescueCaseRescuer.setCreateBy(SecurityUtils.getUsername());
        rescueCaseRescuer.setCreateTime(DateUtils.getNowDate());
        return wxRescueCaseRescuerMapper.insertRescueCaseRescuer(rescueCaseRescuer);
    }

    /**
     * 修改急救案例急救成员
     * 
     * @param rescueCaseRescuer 急救案例急救成员
     * @return 结果
     */
    @Override
    @Transactional
    public int updateRescueCaseRescuer(WxRescueCaseRescuer rescueCaseRescuer) {
        rescueCaseRescuer.setUpdateBy(SecurityUtils.getUsername());
        rescueCaseRescuer.setUpdateTime(DateUtils.getNowDate());
        return wxRescueCaseRescuerMapper.updateRescueCaseRescuer(rescueCaseRescuer);
    }

    /**
     * 根据案例ID删除急救成员
     * 
     * @param caseId 急救案例ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteRescuersByCaseId(Long caseId) {
        return wxRescueCaseRescuerMapper.deleteRescuersByCaseId(caseId);
    }

    /**
     * 批量删除急救案例急救成员
     * 
     * @param ids 需要删除的急救成员ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteRescueCaseRescuerByIds(Long[] ids) {
        return wxRescueCaseRescuerMapper.deleteRescueCaseRescuerByIds(ids);
    }
}
