package com.drxin.wechat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.drxin.common.exception.ServiceException;
import com.drxin.common.utils.SecurityUtils;
import com.drxin.framework.event.ContributionActionEvent;
import com.drxin.wechat.domain.WxCourse;
import com.drxin.wechat.domain.WxCourseAttendance;
import com.drxin.wechat.mapper.WxCourseAttendanceMapper;
import com.drxin.wechat.service.IWxCourseAttendanceService;
import com.drxin.wechat.service.IWxCourseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WxCourseAttendanceServiceImpl extends ServiceImpl<WxCourseAttendanceMapper, WxCourseAttendance> implements IWxCourseAttendanceService {

    @Resource
    private IWxCourseService wxCourseService;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Override
    public int recordAttendance(WxCourseAttendance attendance) {
        // 判断课程是否下架
        if(wxCourseService.isCourseOffShelf(attendance.getCourseId())) {
            throw new ServiceException("该课程已下架，无法签到");
        }
        // 判断考勤记录是否已存在
        // 一天内只能记录一次考勤，可以根据courseId和userId来判断是否已存在记录
        if (attendance.getCourseId() == null || attendance.getUserId() == null) {
            throw new ServiceException("考勤记录不能为空");
        }
        QueryWrapper<WxCourse> courseQueryWrapper = new QueryWrapper<>();
        courseQueryWrapper.eq("id", attendance.getCourseId()).select("id", "allowed_roles");

        WxCourse course = wxCourseService.getBaseMapper().selectOne(courseQueryWrapper);
        String allowedRolesStr = course != null ? course.getAllowedRoles() : null;

        // 如果未配置 allowed_roles，默认不允许签到
        if (allowedRolesStr == null || allowedRolesStr.trim().isEmpty()) {
            throw new ServiceException("该课程未开放签到权限");
        }

        // 转换为 Set，提高匹配效率
        Set<String> allowedRoleSet = Arrays.stream(allowedRolesStr.split(","))
                .map(String::trim)
                .collect(Collectors.toSet());

        // 获取当前用户所有角色ID字符串
        List<String> userRoleIds = SecurityUtils.getLoginUser()
                .getUser()
                .getRoles()
                .stream()
                .map(role -> role.getRoleId().toString())
                .collect(Collectors.toList());

        // 判断是否至少有一个角色允许
        boolean hasPermission = userRoleIds.stream().anyMatch(allowedRoleSet::contains);

        if (!hasPermission) {
            throw new ServiceException("您没有权限签到此课程");
        }
        LocalDate now = LocalDate.now();// 获取当前日期
        String nowStr = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));// 格式化日期为字符串
        // 检查是否已存在当天的考勤记录 // 使用QueryWrapper查询是否存在相同的courseId和userId，并且考勤时间在当天
        QueryWrapper<WxCourseAttendance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("course_id", attendance.getCourseId())
                .eq("user_id", attendance.getUserId())
                .apply("DATE_FORMAT(attend_time, '%Y-%m-%d') = {0}", nowStr);
        Long count = baseMapper.selectCount(queryWrapper); // 查询当天的考勤记录数量
        if (count > 0) {
            throw new ServiceException("今天已经记录过考勤了，请勿重复记录");
        }
        attendance.setAttendTime(new Date());
        // 直接调用父类的save方法保存考勤记录
        boolean saveResult = this.save(attendance);

        // 签到成功后发布贡献值事件
        if (saveResult) {
            publishContributionEvent(attendance);
        }

        return saveResult ? 1 : 0; // 返回1表示成功，0表示失败
    }

    @Override
    public WxCourseAttendance checkCourseAttendStatus(Long userId, Long courseId) {
        if (userId == null || courseId == null) {
            throw new ServiceException("用户ID和课程ID不能为空");
        }
        // 查询指定用户和课程的考勤记录
        LocalDate now = LocalDate.now();// 获取当前日期
        String nowStr = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));// 格式化日期为字符串
        // 检查是否已存在当天的考勤记录 // 使用QueryWrapper查询是否存在相同的courseId和userId，并且考勤时间在当天
        QueryWrapper<WxCourseAttendance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("course_id", courseId)
                .eq("user_id", userId)
                .apply("DATE_FORMAT(attend_time, '%Y-%m-%d') = {0}", nowStr);
        return baseMapper.selectOne(queryWrapper); // 返回第一个记录或null
    }

    /**
     * 发布签到贡献值事件
     * @param attendance 签到记录
     */
    private void publishContributionEvent(WxCourseAttendance attendance) {
        try {
            log.info("发布课程签到贡献值事件: userId={}, courseId={}, attendanceId={}",
                attendance.getUserId(), attendance.getCourseId(), attendance.getId());

            // 构建事件上下文
            Map<String, Object> context = new HashMap<>();
            context.put("courseId", attendance.getCourseId());
            context.put("attendanceId", attendance.getId());
            context.put("attendTime", attendance.getAttendTime());

            // 创建贡献值事件
            ContributionActionEvent event = new ContributionActionEvent(
                this,
                attendance.getUserId(),
                "COURSE_CHECKIN",
                context
            );

            // 发布事件，由监听器异步处理
            eventPublisher.publishEvent(event);

            log.info("课程签到贡献值事件发布成功: userId={}, courseId={}",
                attendance.getUserId(), attendance.getCourseId());

        } catch (Exception e) {
            // 记录日志但不影响主流程
            log.error("发布签到贡献值事件失败: userId={}, courseId={}",
                attendance.getUserId(), attendance.getCourseId(), e);
        }
    }
}
