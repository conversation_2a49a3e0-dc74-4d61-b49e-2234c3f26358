package com.drxin.wechat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.drxin.common.exception.ServiceException;
import com.drxin.common.utils.StringUtils;
import com.drxin.wechat.domain.WxTeam;
import com.drxin.wechat.domain.WxTeamMember;
import com.drxin.wechat.mapper.WxTeamMapper;
import com.drxin.wechat.mapper.WxTeamMemberMapper;
import com.drxin.wechat.service.IWxTeamService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 微信团队管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class WxTeamServiceImpl extends ServiceImpl<WxTeamMapper, WxTeam> implements IWxTeamService {

    @Resource
    private WxTeamMapper wxTeamMapper;

    @Resource
    private WxTeamMemberMapper wxTeamMemberMapper;

    /**
     * 获取我的团队信息
     * 
     * @param userId 用户ID
     * @return 团队信息
     */
    @Override
    public WxTeam getMyTeamInfo(String userId) {
        if (userId == null || userId.trim().isEmpty()) {
            return null;
        }
        return wxTeamMapper.selectTeamByUserId(userId);
    }

    /**
     * 获取我的团队成员列表
     *
     * @param teamId 用户ID
     * @return 团队成员列表
     */
    @Override
    public List<WxTeamMember> getMyTeamMembers(String teamId, String userName) {
        if (teamId == null || teamId.trim().isEmpty()) {
            return null;
        }
        LambdaQueryWrapper<WxTeamMember> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WxTeamMember::getTeamId, teamId);
        if(userName != null && !userName.trim().isEmpty()){
            queryWrapper.like(WxTeamMember::getMemberName, userName);
        }
        return wxTeamMemberMapper.selectList(queryWrapper);
    }

    /**
     * 更新团队名称
     *
     * @param teamId 团队ID
     * @param teamName 新的团队名称
     * @param userId 当前用户ID
     * @return 更新结果
     */
    @Override
    public int updateTeamName(Long teamId, String teamName, String userId) {
        // 参数验证
        if (teamId == null || StringUtils.isEmpty(teamName) || StringUtils.isEmpty(userId)) {
            throw new ServiceException("参数不能为空");
        }

        // 验证团队是否存在且当前用户是团队长
        WxTeam team = wxTeamMapper.selectTeamByTeamId(teamId.toString());
        if (team == null) {
            throw new ServiceException("团队不存在");
        }

        if (!userId.equals(team.getLeaderId())) {
            throw new ServiceException("只有团队长可以修改团队名称");
        }

        // 如果新名称与当前名称相同，直接返回成功
        if (teamName.equals(team.getTeamName())) {
            return 1;
        }

        // 验证团队名称唯一性
        WxTeam existingTeam = wxTeamMapper.checkTeamNameUnique(teamName);
        if (existingTeam != null && !existingTeam.getTeamId().equals(teamId)) {
            throw new ServiceException("团队名称已存在");
        }

        // 使用 UpdateWrapper 更新
        UpdateWrapper<WxTeam> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("team_id", teamId)
                    .set("team_name", teamName);

        return baseMapper.update(null, updateWrapper);
    }
}
