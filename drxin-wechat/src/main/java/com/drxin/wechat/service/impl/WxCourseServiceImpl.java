package com.drxin.wechat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.drxin.wechat.domain.WxCourse;
import com.drxin.wechat.mapper.WxCourseMapper;
import com.drxin.wechat.service.IWxCourseService;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class WxCourseServiceImpl extends ServiceImpl<WxCourseMapper, WxCourse> implements IWxCourseService {
    @Override
    public List<WxCourse> selectWxCourseList(WxCourse wxCourse) {
        return baseMapper.selectWxCourseList(wxCourse);
    }

    @Override
    public WxCourse getWxCourseById(Long id) {
        return baseMapper.selectWxCourseById(id);
    }

    // 查询课程详情，并判断课程是否下架
    @Override
    public boolean isCourseOffShelf(Long courseId) {
        LocalDateTime now = LocalDateTime.now();// 获取当前日期
        String nowStr = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));// 格式化日期为字符串
        QueryWrapper<WxCourse> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", courseId)
                .apply("DATE_FORMAT(end_time, '%Y-%m-%d %H:%i:%s') >= {0}", nowStr) // 检查结束时间是否小于当前日期
                .select("id", "course_status");
        WxCourse course = baseMapper.selectOne(queryWrapper);
        // 如果查询结果不为空，且课程状态为下架，则返回true
        return course == null;
    }
}
