package com.drxin.wechat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.drxin.wechat.domain.WxTeam;
import com.drxin.wechat.domain.WxTeamMember;

import java.util.List;

/**
 * 微信团队管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface IWxTeamService extends IService<WxTeam> {

    /**
     * 获取我的团队信息
     * 
     * @param userId 用户ID
     * @return 团队信息
     */
    WxTeam getMyTeamInfo(String userId);

    /**
     * 获取我的团队成员列表
     *
     * @param teamId 团队ID
     * @return 团队成员列表
     */
    List<WxTeamMember> getMyTeamMembers(String teamId, String userName);

    /**
     * 更新团队名称
     *
     * @param teamId 团队ID
     * @param teamName 新的团队名称
     * @param userId 当前用户ID
     * @return 更新结果
     */
    int updateTeamName(Long teamId, String teamName, String userId);
}
