package com.drxin.wechat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.drxin.wechat.domain.WxActivityReview;

import java.util.List;

/**
 * 微信活动评审Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface IWxActivityReviewService extends IService<WxActivityReview> {

    /**
     * 查询活动评审列表
     *
     * @param activityReview 活动评审查询条件
     * @return 活动评审集合
     */
    List<WxActivityReview> selectActivityReviewList(WxActivityReview activityReview);

    /**
     * 保存活动评审
     * 
     * @param activityReview 活动评审
     * @return 结果
     */
    int saveActivityReview(WxActivityReview activityReview);

    /**
     * 修改活动评审
     *
     * @param activityReview 活动评审
     * @return 结果
     */
    int updateActivityReview(WxActivityReview activityReview);

    /**
     * 根据ID查询活动评审详情
     *
     * @param id 活动评审ID
     * @return 活动评审
     */
    WxActivityReview getActivityReviewInfo(Long id);

    /**
     * 批量删除活动评审
     * 
     * @param ids 需要删除的活动评审ID
     * @return 结果
     */
    int deleteActivityReviewByIds(Long[] ids);
}
