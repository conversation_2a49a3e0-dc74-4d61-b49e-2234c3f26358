package com.drxin.wechat.service;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.drxin.wechat.domain.WxUser;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface IWxUserService extends IService<WxUser> {
    /**
     * 更新用户资料
     * @param wxUser wechatUser
     * @return 更新条数
     */
    int updateUserProfile(WxUser wxUser);

    WxUser getPersonalInfo(String userId);

    int updatePersonalInfo(WxUser wxUser);

    String selectPersonalQrCode(String userId);

    JSONObject getIDCardInfo(MultipartFile file);

    String regenerateInviterCode(String userId);

    List<WxUser> getRecommendList(WxUser wxUser);
}
