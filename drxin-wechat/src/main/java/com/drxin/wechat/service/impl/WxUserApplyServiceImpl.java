package com.drxin.wechat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.drxin.common.core.domain.entity.SysUser;
import com.drxin.common.exception.ServiceException;
import com.drxin.common.utils.IdCardValidationUtils;
import com.drxin.system.mapper.SysUserMapper;
import com.drxin.wechat.domain.WxUserApply;
import com.drxin.wechat.mapper.WxUserApplyMapper;
import com.drxin.wechat.service.IWxUserApplyService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户身份申请Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
@Service
public class WxUserApplyServiceImpl extends ServiceImpl<WxUserApplyMapper, WxUserApply> implements IWxUserApplyService {

    @Resource
    private WxUserApplyMapper wxUserApplyMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    /**
     * 根据用户ID和用户类型查询用户申请信息
     *
     * @param userId 用户ID
     * @param userType 用户类型
     * @return 用户申请信息
     */
    @Override
    public WxUserApply selectUserApplyByUserIdAndType(Long userId, String userType) {
        if (userId == null) {
            throw new ServiceException("用户ID不能为空");
        }
        if (userType == null || userType.trim().isEmpty()) {
            throw new ServiceException("用户类型不能为空");
        }
        return wxUserApplyMapper.selectUserApplyByUserIdAndType(userId, userType.trim());
    }

    /**
     * 新增用户身份申请
     *
     * @param wxUserApply 用户身份申请
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertUserApply(WxUserApply wxUserApply) {
        // 参数校验
        validateUserApply(wxUserApply);

        // 检查用户是否已有审核中的申请记录
        WxUserApply existingApply = wxUserApplyMapper.selectUserApplyByUserIdAndStatus(
            wxUserApply.getUserId(), "2");
        if (existingApply != null) {
            throw new ServiceException("您已有申请正在审核中，请勿重复申请");
        }

        // 如果用户没有申请过，则判断用户身份证是否已存在用户表中且user_id不是当前的userId
        WxUserApply userAnyApply = wxUserApplyMapper.selectUserApplyByUserId(wxUserApply.getUserId());
        if (userAnyApply == null && wxUserApply.getIdCard() != null && !wxUserApply.getIdCard().trim().isEmpty()) {
            SysUser existingUser = sysUserMapper.checkIdCardUnique(wxUserApply.getIdCard().trim());
            if (existingUser != null && !existingUser.getUserId().equals(wxUserApply.getUserId())) {
                throw new ServiceException("该身份证号已被其他用户使用，请检查后重新填写");
            }
        }

        return wxUserApplyMapper.insertUserApply(wxUserApply);
    }

    /**
     * 修改用户身份申请
     *
     * @param wxUserApply 用户身份申请
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateUserApply(WxUserApply wxUserApply) {
        // 参数校验
        if (wxUserApply.getId() == null) {
            throw new ServiceException("申请ID不能为空");
        }

        validateUserApply(wxUserApply);

        return wxUserApplyMapper.updateUserApply(wxUserApply);
    }

    /**
     * 校验用户申请信息
     *
     * @param wxUserApply 用户申请信息
     */
    private void validateUserApply(WxUserApply wxUserApply) {
        if (wxUserApply == null) {
            throw new ServiceException("申请信息不能为空");
        }
        if (wxUserApply.getUserId() == null) {
            throw new ServiceException("用户ID不能为空");
        }
        if (wxUserApply.getRealName() == null || wxUserApply.getRealName().trim().isEmpty()) {
            throw new ServiceException("真实姓名不能为空");
        }
        if (wxUserApply.getIdCard() == null || wxUserApply.getIdCard().trim().isEmpty()) {
            throw new ServiceException("证件号码不能为空");
        }
        if (wxUserApply.getPhoneNumber() == null || wxUserApply.getPhoneNumber().trim().isEmpty()) {
            throw new ServiceException("手机号码不能为空");
        }

        // 证件号码格式校验
        String idCard = wxUserApply.getIdCard().trim();
        String cardType = wxUserApply.getCardType();
        IdCardValidationUtils.validateIdCard(idCard, cardType);

        // 手机号码格式校验
        String phoneNumber = wxUserApply.getPhoneNumber().trim();
        IdCardValidationUtils.validatePhoneNumber(phoneNumber);
    }

    /**
     * 检查用户是否已有申请记录
     *
     * @param userId 用户ID
     * @return 是否存在申请记录
     */
    @Override
    public boolean hasUserApply(Long userId) {
        if (userId == null) {
            return false;
        }
        WxUserApply userApply = wxUserApplyMapper.selectUserApplyByUserId(userId);
        return userApply != null;
    }

    /**
     * 检查用户是否已有审核中的申请记录
     *
     * @param userId 用户ID
     * @return 是否存在申请记录
     */
    @Override
    public boolean hasPendingApply(Long userId) {
        if (userId == null) {
            return false;
        }
        WxUserApply userApply = wxUserApplyMapper.selectUserApplyByUserIdAndStatus(userId, "2");
        return userApply != null;
    }

    /**
     * 更新申请状态
     *
     * @param id 申请ID
     * @param status 申请状态
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateApplyStatus(Long id, String status) {
        if (id == null) {
            throw new ServiceException("申请ID不能为空");
        }
        if (status == null || status.trim().isEmpty()) {
            throw new ServiceException("申请状态不能为空");
        }

        WxUserApply wxUserApply = new WxUserApply();
        wxUserApply.setId(id);
        wxUserApply.setApplyStatus(status.trim());

        return wxUserApplyMapper.updateUserApply(wxUserApply);
    }
}
