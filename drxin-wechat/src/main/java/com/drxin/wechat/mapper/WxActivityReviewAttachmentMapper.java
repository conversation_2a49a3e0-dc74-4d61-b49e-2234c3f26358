package com.drxin.wechat.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.drxin.wechat.domain.WxActivityReviewAttachment;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 微信活动评审附件Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Mapper
public interface WxActivityReviewAttachmentMapper extends BaseMapper<WxActivityReviewAttachment> {

    /**
     * 根据活动ID查询附件列表
     * 
     * @param activityId 活动ID
     * @return 附件集合
     */
    List<WxActivityReviewAttachment> selectAttachmentsByActivityId(Long activityId);

    /**
     * 新增活动评审附件
     * 
     * @param activityReviewAttachment 活动评审附件
     * @return 结果
     */
    int insertActivityReviewAttachment(WxActivityReviewAttachment activityReviewAttachment);

    /**
     * 根据活动ID删除附件
     *
     * @param activityId 活动ID
     * @return 结果
     */
    int deleteAttachmentsByActivityId(Long activityId);

    /**
     * 批量插入附件
     *
     * @param attachments 附件列表
     * @return 结果
     */
    int insertBatch(List<WxActivityReviewAttachment> attachments);
}
