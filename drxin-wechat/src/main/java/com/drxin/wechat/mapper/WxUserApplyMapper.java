package com.drxin.wechat.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.drxin.wechat.domain.WxUserApply;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户身份申请Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
@Mapper
public interface WxUserApplyMapper extends BaseMapper<WxUserApply> {

    /**
     * 根据用户ID查询用户申请信息
     *
     * @param userId 用户ID
     * @return 用户申请信息
     */
    WxUserApply selectUserApplyByUserId(Long userId);

    /**
     * 根据用户ID和用户类型查询用户申请信息
     *
     * @param userId 用户ID
     * @param userType 用户类型
     * @return 用户申请信息
     */
    WxUserApply selectUserApplyByUserIdAndType(@Param("userId") Long userId, @Param("userType") String userType);

    /**
     * 根据用户ID和申请状态查询用户申请信息
     *
     * @param userId 用户ID
     * @param applyStatus 申请状态
     * @return 用户申请信息
     */
    WxUserApply selectUserApplyByUserIdAndStatus(@Param("userId") Long userId, @Param("applyStatus") String applyStatus);

    /**
     * 新增用户身份申请
     * 
     * @param wxUserApply 用户身份申请
     * @return 结果
     */
    int insertUserApply(WxUserApply wxUserApply);

    /**
     * 修改用户身份申请
     * 
     * @param wxUserApply 用户身份申请
     * @return 结果
     */
    int updateUserApply(WxUserApply wxUserApply);


}
