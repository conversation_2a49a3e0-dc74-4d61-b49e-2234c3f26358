package com.drxin.wechat.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.drxin.wechat.domain.WxRescueCaseAttachment;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 微信急救案例附件Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
@Mapper
public interface WxRescueCaseAttachmentMapper extends BaseMapper<WxRescueCaseAttachment> {

    /**
     * 根据案例ID查询附件列表
     * 
     * @param caseId 急救案例ID
     * @return 附件集合
     */
    List<WxRescueCaseAttachment> selectAttachmentsByCaseId(Long caseId);

    /**
     * 新增急救案例附件
     * 
     * @param rescueCaseAttachment 急救案例附件
     * @return 结果
     */
    int insertRescueCaseAttachment(WxRescueCaseAttachment rescueCaseAttachment);

    /**
     * 根据案例ID删除附件
     *
     * @param caseId 急救案例ID
     * @return 结果
     */
    int deleteAttachmentsByCaseId(Long caseId);

    /**
     * 批量插入附件
     *
     * @param attachments 附件列表
     * @return 结果
     */
    int insertBatch(List<WxRescueCaseAttachment> attachments);
}
