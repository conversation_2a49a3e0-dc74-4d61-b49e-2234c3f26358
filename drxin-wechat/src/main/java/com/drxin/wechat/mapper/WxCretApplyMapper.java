package com.drxin.wechat.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.drxin.wechat.domain.WxCretApply;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface WxCretApplyMapper extends BaseMapper<WxCretApply> {

    List<WxCretApply> selectCretApplyList(WxCretApply apply);

    int insertCretApply(WxCretApply apply);

    int updateCretApply(WxCretApply apply);

    WxCretApply getApplyInfo(Long id);

    int deleteCretApplyByIds(Long[] ids);
}
