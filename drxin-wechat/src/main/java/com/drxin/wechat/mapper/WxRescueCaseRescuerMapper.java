package com.drxin.wechat.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.drxin.wechat.domain.WxRescueCaseRescuer;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 微信急救案例急救成员Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-05
 */
@Mapper
public interface WxRescueCaseRescuerMapper extends BaseMapper<WxRescueCaseRescuer> {

    /**
     * 查询急救案例急救成员列表
     * 
     * @param rescueCaseRescuer 急救案例急救成员
     * @return 急救案例急救成员集合
     */
    List<WxRescueCaseRescuer> selectRescueCaseRescuerList(WxRescueCaseRescuer rescueCaseRescuer);

    /**
     * 根据案例ID查询急救成员列表
     * 
     * @param caseId 急救案例ID
     * @return 急救成员集合
     */
    List<WxRescueCaseRescuer> selectRescuersByCaseId(Long caseId);

    /**
     * 新增急救案例急救成员
     * 
     * @param rescueCaseRescuer 急救案例急救成员
     * @return 结果
     */
    int insertRescueCaseRescuer(WxRescueCaseRescuer rescueCaseRescuer);

    /**
     * 修改急救案例急救成员
     * 
     * @param rescueCaseRescuer 急救案例急救成员
     * @return 结果
     */
    int updateRescueCaseRescuer(WxRescueCaseRescuer rescueCaseRescuer);

    /**
     * 根据案例ID删除急救成员
     * 
     * @param caseId 急救案例ID
     * @return 结果
     */
    int deleteRescuersByCaseId(Long caseId);

    /**
     * 批量删除急救案例急救成员
     *
     * @param ids 需要删除的急救成员ID
     * @return 结果
     */
    int deleteRescueCaseRescuerByIds(Long[] ids);

    /**
     * 批量插入急救成员
     *
     * @param rescuers 急救成员列表
     * @return 结果
     */
    int insertBatch(List<WxRescueCaseRescuer> rescuers);
}
