package com.drxin.wechat.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.drxin.wechat.domain.WxCourse;
import com.drxin.wechat.domain.WxCourseEnrollment;
import com.drxin.wechat.vo.WxEnrollCourseVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface WxCourseEnrollmentMapper extends BaseMapper<WxCourseEnrollment> {


    int insertCourseEnrollmentRecord(WxCourseEnrollment courseEnrollment);

    List<WxEnrollCourseVo> enrollCourseList(Long userId);
}
