package com.drxin.wechat.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.drxin.wechat.domain.WxRescueCase;
import com.drxin.wechat.vo.WxRescueCaseVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 微信急救案例Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-05
 */
@Mapper
public interface WxRescueCaseMapper extends BaseMapper<WxRescueCase> {

    /**
     * 查询急救案例列表
     *
     * @param rescueCaseVo 急救案例查询条件
     * @return 急救案例集合
     */
    List<WxRescueCase> selectRescueCaseList(WxRescueCaseVo rescueCaseVo);

    /**
     * 新增急救案例
     * 
     * @param rescueCase 急救案例
     * @return 结果
     */
    int insertRescueCase(WxRescueCase rescueCase);

    /**
     * 修改急救案例
     * 
     * @param rescueCase 急救案例
     * @return 结果
     */
    int updateRescueCase(WxRescueCase rescueCase);

    /**
     * 根据ID查询急救案例详情
     * 
     * @param id 急救案例ID
     * @return 急救案例
     */
    WxRescueCase getRescueCaseInfo(Long id);

    /**
     * 批量删除急救案例
     * 
     * @param ids 需要删除的急救案例ID
     * @return 结果
     */
    int deleteRescueCaseByIds(Long[] ids);
}
