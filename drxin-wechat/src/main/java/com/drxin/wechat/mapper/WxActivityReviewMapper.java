package com.drxin.wechat.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.drxin.wechat.domain.WxActivityReview;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 微信活动评审Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Mapper
public interface WxActivityReviewMapper extends BaseMapper<WxActivityReview> {

    /**
     * 查询活动评审详情
     * 
     * @param id 活动评审主键
     * @return 活动评审
     */
    WxActivityReview getActivityReviewInfo(Long id);

    /**
     * 查询活动评审列表
     * 
     * @param activityReview 活动评审
     * @return 活动评审集合
     */
    List<WxActivityReview> selectActivityReviewList(WxActivityReview activityReview);

    /**
     * 新增活动评审
     * 
     * @param activityReview 活动评审
     * @return 结果
     */
    int insertActivityReview(WxActivityReview activityReview);

    /**
     * 修改活动评审
     * 
     * @param activityReview 活动评审
     * @return 结果
     */
    int updateActivityReview(WxActivityReview activityReview);

    /**
     * 删除活动评审
     * 
     * @param id 活动评审主键
     * @return 结果
     */
    int deleteActivityReviewById(Long id);

    /**
     * 批量删除活动评审
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteActivityReviewByIds(Long[] ids);
}
