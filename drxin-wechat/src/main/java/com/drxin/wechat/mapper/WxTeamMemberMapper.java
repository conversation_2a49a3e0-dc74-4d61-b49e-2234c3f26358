package com.drxin.wechat.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.drxin.wechat.domain.WxTeamMember;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 微信团队成员Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Mapper
public interface WxTeamMemberMapper extends BaseMapper<WxTeamMember> {
    
    /**
     * 根据用户ID查询团队成员列表（该用户所在团队的所有成员）
     * 
     * @param userId 用户ID
     * @return 团队成员列表
     */
    List<WxTeamMember> selectMembersByUserId(String userId);

    /**
     * 根据团队ID查询成员列表
     * 
     * @param teamId 团队ID
     * @return 团队成员列表
     */
    List<WxTeamMember> selectMembersByTeamId(String teamId);
}
