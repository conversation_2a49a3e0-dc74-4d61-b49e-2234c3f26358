package com.drxin.wechat.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.drxin.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 微信急救案例急救成员对象 rescue_case_rescuer
 * 
 * <AUTHOR>
 * @date 2025-07-05
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("rescue_case_rescuer")
public class WxRescueCaseRescuer extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId
    private Long id;

    /** 急救案例ID */
    private Long caseId;

    /** 急救员id */
    private Long rescuerId;

    /** 急救员姓名 */
    private String rescuerName;

    /** 急救员手机号 */
    private String rescuerPhone;

    /** 急救员身份证号 */
    private String rescuerIdCard;

    /** 分配到的贡献值 */
    private String score;
}
