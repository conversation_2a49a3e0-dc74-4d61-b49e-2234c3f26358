package com.drxin.wechat.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.drxin.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("course_enrollment")
public class WxCourseEnrollment extends BaseEntity {
    private Long id;
    private Long userId;
    private Long courseId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date enrollTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cancelTime;
    private String enrollStatus;

}
