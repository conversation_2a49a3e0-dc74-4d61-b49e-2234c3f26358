package com.drxin.wechat.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.drxin.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 微信活动评审附件对象 activity_review_attachments
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("activity_review_attachments")
public class WxActivityReviewAttachment extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId
    private Long id;

    /** 关联活动ID */
    private Long activityId;

    /** 附件编号 */
    private String attachmentId;

    /** 附件URL */
    private String attachmentUrl;

    /** 附件类型 */
    private String attachmentType;
}
