package com.drxin.wechat.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.drxin.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("course")
public class WxCourse extends BaseEntity {

    private String id;
    private String courseName;
    private String description;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    private String location;
    private Long maxQuota;
    private Long currentQuota;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date enrollDeadline;
    private String courseStatus;
    private BigDecimal coursePrice;
    private String allowedRoles;


}
