package com.drxin.wechat.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.drxin.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 微信活动评审人员关联对象 activity_review_user
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("activity_review_user")
public class WxActivityReviewUser extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId
    private Long id;

    /** 关联活动ID */
    private Long activityId;

    /** 用户ID */
    private Long userId;

    /** 角色类型 0-主办人 1-协办人 */
    private Integer roleType;

    /** 用户名 */
    @TableField(exist = false)
    private String userName;
}
