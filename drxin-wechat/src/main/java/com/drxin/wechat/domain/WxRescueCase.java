package com.drxin.wechat.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.drxin.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 微信急救案例对象 rescue_case
 * 
 * <AUTHOR>
 * @date 2025-07-05
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("rescue_case")
public class WxRescueCase extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId
    private Long id;

    /** 急救日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date rescueDate;

    /** 急救所在城市 */
    private String city;

    /** 详细地址 */
    private String address;

    /** 被救人姓名 */
    private String patientName;

    /** 被救人性别 */
    private String patientGender;

    /** 被救人年龄 */
    private String patientAge;

    /** 病症类型 */
    private String illnessType;

    /** 急救情况说明 */
    private String rescueDescription;

    /** 急救案例状态 */
    private String rescueStatus;

    /** 在线标识 */
    private String onlineFlag;

    /** 远程指导员用户ID */
    private Long remoteGuideUserId;

    /** 远程指导员真实姓名 */
    private String remoteGuideRealName;

    /** 急救员列表 */
    @TableField(exist = false)
    private List<WxRescueCaseRescuer> rescuers;

    /** 附件列表 */
    @TableField(exist = false)
    private List<WxRescueCaseAttachment> attachments;
}
