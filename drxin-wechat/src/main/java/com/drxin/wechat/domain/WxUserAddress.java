package com.drxin.wechat.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.drxin.common.annotation.Excel;
import com.drxin.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 用户地址管理对象 user_address
 * 
 * <AUTHOR>
 * @date 2025-06-02
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("user_address")
public class WxUserAddress extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** 地址ID */
    @TableId
    private String id;

    /** 所属用户ID */
    @Excel(name = "所属用户ID")
    private Long userId;

    /** 收件人姓名 */
    @Excel(name = "收件人姓名")
    private String name;

    /** 收件人手机号 */
    @Excel(name = "收件人手机号")
    private String phone;

    /** 性别（0=男，1=女） */
    @Excel(name = "性别", readConverterExp = "0=男,1=女")
    private String sex;

    /** 省份 */
    @Excel(name = "省份")
    private String province;

    /** 城市 */
    @Excel(name = "城市")
    private String city;

    /** 区/县 */
    @Excel(name = "区/县")
    private String district;

    /** 详细地址 */
    @Excel(name = "详细地址")
    private String detail;

    /** 完整地址 */
    @Excel(name = "完整地址")
    private String address;

    /** 是否默认地址 Y=是 N=否 */
    private String defaultFlag;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("name", getName())
            .append("phone", getPhone())
            .append("sex", getSex())
            .append("province", getProvince())
            .append("city", getCity())
            .append("district", getDistrict())
            .append("detail", getDetail())
            .append("address", getAddress())
            .append("isDefault", getDefaultFlag())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
