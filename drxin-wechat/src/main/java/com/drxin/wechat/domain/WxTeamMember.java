package com.drxin.wechat.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.drxin.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.naming.Name;

/**
 * 微信团队成员对象 team_member
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("team_member")
public class WxTeamMember extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 成员ID（即 user_id） */
    @TableField("member_id")
    private String memberId;

    /** 团队ID（真实团队ID 或 虚拟团队ID） */
    @TableField("team_id")
    private String teamId;

    /** 成员姓名 */
    @TableField("member_name")
    private String memberName;

    /** 成员身份类型（急救员/导师/弟子） */
    @TableField("member_type")
    private String memberType;

    /** 身份升级时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date upgradedTime;
}
