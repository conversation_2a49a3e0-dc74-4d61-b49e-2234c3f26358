package com.drxin.wechat.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

@Data
public class WxUserApply implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 申请状态
     */
    private String applyStatus;

    /**
     * 用户身份
     */
    private String userType;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 证件类型
     */
    private String cardType;

    /**
     * 证件号码
     */
    private String idCard;

    /**
     * 性别
     */
    private String sex;

    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     * 邀请人ID
     */
    private Long inviterId;

    private String inviterName;

    /**
     * 照片id
     */
    private Long photoId;

    /**
     * 照片地址
     */
    private String photoUrl;

    /**
     * 驳回原因
     */
    private String rejectReason;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("userId", getUserId())
                .append("applyStatus", getApplyStatus())
                .append("userType", getUserType())
                .append("realName", getRealName())
                .append("cardType", getCardType())
                .append("idCard", getIdCard())
                .append("sex", getSex())
                .append("phoneNumber", getPhoneNumber())
                .append("inviterId", getInviterId())
                .append("inviterName", getInviterName())
                .append("photoId", getPhotoId())
                .append("photoUrl", getPhotoUrl())
                .append("rejectReason", getRejectReason())
                .toString();
    }
}
