package com.drxin.wechat.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
@TableName("course_attendance")
public class WxCourseAttendance {
    @TableId(type=IdType.AUTO)
    private Long id; // 主键ID
    private Long courseId; // 课程ID
    private Long userId; // 用户ID
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date attendTime; // 考勤时间
}
