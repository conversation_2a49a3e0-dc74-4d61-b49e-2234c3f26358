package com.drxin.wechat.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@TableName("sys_user")
@Data
public class WxUser {

    @TableId
    private Long userId;
    private String sex;
    private String userName;
    private String nickName;
    private String deptName;
    private String avatar;
    private String email;
    private String phonenumber;
    @TableField("real_name")
    private String realName;
    private String idCard;
    private String education;
    private String companyName;
    private String photoUrl;
    private Long photoId;
    private String inviterCode;
    private String contribution;
    private String inviterId;
    private String inviterName;
    private String dealInviterId;
    private String dealInviterName;
    private String openid;
    @TableField("upgraded_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date upgradedTime;
    private String userType;
}
