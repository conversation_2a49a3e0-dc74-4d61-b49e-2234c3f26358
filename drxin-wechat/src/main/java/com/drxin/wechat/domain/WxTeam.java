package com.drxin.wechat.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.drxin.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 微信团队管理对象 team
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("team")
public class WxTeam extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 团队ID，自增主键 */
    @TableId
    private Long teamId;

    /** 团队名称（唯一） */
    private String teamName;

    /** 团队长用户ID */
    private String leaderId;

    /** 团队长姓名 */
    private String leaderName;
}
