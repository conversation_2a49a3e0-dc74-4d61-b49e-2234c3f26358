package com.drxin.wechat.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.drxin.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 微信急救案例附件对象 rescue_case_attachments
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("rescue_case_attachments")
public class WxRescueCaseAttachment extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId
    private Long id;

    /** 关联案例ID */
    private Long caseId;

    /** 文件ID */
    private String fileId;

    /** 文件URL */
    private String fileUrl;

    /** 文件类型 */
    private String fileType;
}
