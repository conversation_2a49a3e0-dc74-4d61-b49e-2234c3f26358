package com.drxin.wechat.controller;

import com.drxin.common.annotation.Log;
import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.common.core.page.TableDataInfo;
import com.drxin.common.enums.BusinessType;
import com.drxin.wechat.domain.WxUserAddress;
import com.drxin.wechat.service.IWxUserAddressService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户地址管理Controller
 * 
 * <AUTHOR>
 * @date 2025-06-02
 */
@RestController
@RequestMapping("/wx/user_address")
public class WxUserAddressController extends BaseController {
    @Resource
    private IWxUserAddressService wxUserAddressService;

    /**
     * 查询用户地址管理列表
     */
    @GetMapping("/list")
    public TableDataInfo list(WxUserAddress userAddress){
        startPage();
        List<WxUserAddress> list = wxUserAddressService.selectUserAddressList(userAddress);
        return getDataTable(list);
    }

    /**
     * 获取用户地址管理详细信息
     */
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(wxUserAddressService.selectUserAddressById(id));
    }

    /**
     * 新增用户地址管理
     */
    @Log(title = "用户地址管理", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public AjaxResult add(@RequestBody WxUserAddress userAddress) {
        wxUserAddressService.insertUserAddress(userAddress);
        return success(userAddress.getId());
    }

    /**
     * 修改用户地址管理
     */
    @Log(title = "用户地址管理", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public AjaxResult edit(@RequestBody WxUserAddress userAddress) {
        return toAjax(wxUserAddressService.updateUserAddress(userAddress));
    }

    /**
     * 删除用户地址管理
     */
    @Log(title = "用户地址管理", businessType = BusinessType.DELETE)
	@GetMapping("/delete/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(wxUserAddressService.deleteUserAddressByIds(ids));
    }
}
