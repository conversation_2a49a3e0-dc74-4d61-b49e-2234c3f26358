package com.drxin.wechat.controller;

import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.R;
import com.drxin.wechat.domain.WxTeam;
import com.drxin.wechat.domain.WxTeamMember;
import com.drxin.wechat.service.IWxTeamService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 微信团队管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/wx/my-team-info")
public class WxTeamController extends BaseController {

    @Resource
    private IWxTeamService wxTeamService;

    /**
     * 获取我的团队信息
     */
    @GetMapping("/{userId}")
    public R<WxTeam> getMyTeamInfo(@PathVariable("userId") String userId) {
        WxTeam team = wxTeamService.getMyTeamInfo(userId);
        if (team != null) {
            return R.ok(team);
        } else {
            return R.fail("未找到团队信息");
        }
    }



    /**
     * 获取我的团队成员列表
     */
    @GetMapping("/team-members")
    public R<Object> getMyTeamMembers(@RequestParam("teamId") String teamId,@RequestParam("memberName") String memberName) {
        startPage();
        List<WxTeamMember> members = wxTeamService.getMyTeamMembers(teamId, memberName);
        if (members != null && !members.isEmpty()) {
            return R.ok(getDataTable(members));
        } else {
            return R.fail("未找到团队成员");
        }
    }

    /**
     * 更新团队名称
     */
    @GetMapping("/updateTeamName")
    public R<String> updateTeamName(@RequestParam("teamId") Long teamId,
                                   @RequestParam("teamName") String teamName,
                                   @RequestParam("leaderId") String userId) {
        try {
            int result = wxTeamService.updateTeamName(teamId, teamName, userId);
            if (result > 0) {
                return R.ok("团队名称更新成功");
            } else {
                return R.fail("团队名称更新失败");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
