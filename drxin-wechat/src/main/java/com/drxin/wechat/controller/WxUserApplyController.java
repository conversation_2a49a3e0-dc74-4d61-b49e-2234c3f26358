package com.drxin.wechat.controller;

import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.R;
import com.drxin.wechat.domain.WxUser;
import com.drxin.wechat.domain.WxUserApply;
import com.drxin.wechat.service.IWxUserApplyService;
import com.drxin.wechat.service.IWxUserService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 用户身份申请Controller
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@RestController
@RequestMapping("/wx/user_apply")
public class WxUserApplyController extends BaseController {

    @Resource
    private IWxUserApplyService wxUserApplyService;

    @Resource
    private IWxUserService wxUserService;

    /**
     * 根据用户ID和用户类型查询用户申请信息
     *
     * @param userId 用户ID
     * @param userType 用户类型
     * @return 用户申请信息
     */
    @GetMapping("/getByUserId")
    public R<WxUserApply> getByUserId(@RequestParam Long userId, @RequestParam String userType) {
        WxUserApply userApply = wxUserApplyService.selectUserApplyByUserIdAndType(userId, userType);
        return R.ok(userApply);
    }

    /**
     * 新增用户身份申请
     *
     * @param wxUserApply 用户身份申请
     * @return 结果
     */
    @PostMapping("/save")
    public R<Long> save(@RequestBody WxUserApply wxUserApply) {
        int result = wxUserApplyService.insertUserApply(wxUserApply);
        if (result > 0) {
            return R.ok(wxUserApply.getId(), "申请提交成功");
        }
        return R.fail("申请提交失败");
    }

    /**
     * 修改用户身份申请
     *
     * @param wxUserApply 用户身份申请
     * @return 结果
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody WxUserApply wxUserApply) {
        int result = wxUserApplyService.updateUserApply(wxUserApply);
        if (result > 0) {
            return R.ok(null, "更新成功");
        }
        return R.fail("更新失败");
    }

    /**
     * 检查用户是否已有申请记录
     *
     * @param userId 用户ID
     * @return 是否存在申请记录
     */
    @GetMapping("/hasApply")
    public R<Boolean> hasApply(@RequestParam Long userId) {
        boolean hasApply = wxUserApplyService.hasUserApply(userId);
        return R.ok(hasApply);
    }

    /**
     * 检查用户是否已有审核中的申请记录
     *
     * @param userId 用户ID
     * @return 是否存在申请记录
     */
    @GetMapping("/hasPendingApply")
    public R<Boolean> hasPendingApply(@RequestParam Long userId) {
        boolean hasApply = wxUserApplyService.hasPendingApply(userId);
        return R.ok(hasApply);
    }

    /**
     * 更新申请状态
     *
     * @param id 申请ID
     * @param status 申请状态
     * @return 结果
     */
    @PostMapping("/updateStatus")
    public R<Void> updateStatus(@RequestParam Long id, @RequestParam String status) {
        int result = wxUserApplyService.updateApplyStatus(id, status);
        if (result > 0) {
            return R.ok(null, "状态更新成功");
        }
        return R.fail("状态更新失败");
    }

}
