package com.drxin.wechat.controller;

import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.common.core.domain.entity.SysRole;
import com.drxin.common.core.domain.entity.SysUser;
import com.drxin.common.core.domain.model.LoginUser;
import com.drxin.common.utils.SecurityUtils;
import com.drxin.system.domain.vo.WxMenuVo;
import com.drxin.system.service.ISysRoleService;
import com.drxin.system.service.IWxMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 微信小程序菜单Controller
 *
 * <AUTHOR>
 * @date 2024-07-14
 */
@RestController
@RequestMapping("/wx/menu")
public class WxMenuController extends BaseController {

    @Autowired
    private IWxMenuService wxMenuService;



    @Autowired
    private ISysRoleService sysRoleService;

    /**
     * 根据菜单类型获取菜单列表
     * @param menuType 菜单类型：personal-个人设置菜单，function-功能菜单
     */
    @GetMapping("/list")
    public AjaxResult getMenusByType(@RequestParam("menuType") String menuType) {
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            SysUser user = loginUser.getUser();
            List<SysRole> roles = sysRoleService.getRolesByUserId(user.getUserId());
            List<WxMenuVo> menus = wxMenuService.getMenusByType(menuType, roles);
            return AjaxResult.success(menus);
        } catch (IllegalArgumentException e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 检查用户是否有权限访问指定菜单
     */
    @GetMapping("/check/{menuId}")
    public AjaxResult checkMenuPermission(@PathVariable("menuId") Integer menuId) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        
        boolean hasPermission = wxMenuService.checkMenuPermission(menuId, user.getRoles());
        

        
        return AjaxResult.success(hasPermission);
    }
}
