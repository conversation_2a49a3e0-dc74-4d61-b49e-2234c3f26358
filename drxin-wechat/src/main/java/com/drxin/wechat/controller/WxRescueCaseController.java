package com.drxin.wechat.controller;

import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.wechat.domain.WxRescueCase;
import com.drxin.wechat.service.IWxRescueCaseService;
import com.drxin.wechat.vo.WxRescueCaseVo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 微信急救案例Controller
 * 
 * <AUTHOR>
 * @date 2025-07-05
 */
@RestController
@RequestMapping("/wx/rescue-case")
public class WxRescueCaseController extends BaseController {

    @Resource
    private IWxRescueCaseService wxRescueCaseService;

    /**
     * 查询急救案例列表
     */
    @GetMapping("/list")
    public AjaxResult list(WxRescueCaseVo rescueCaseVo) {
        startPage();
        List<WxRescueCase> list = wxRescueCaseService.selectRescueCaseList(rescueCaseVo);
        return AjaxResult.success(getDataTable(list));
    }

    /**
     * 获取急救案例详细信息
     */
    @GetMapping("/getRescueCaseInfo/{id}")
    public AjaxResult getRescueCaseInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(wxRescueCaseService.getRescueCaseInfo(id));
    }

    /**
     * 保存急救案例
     */
    @PostMapping("/save")
    public AjaxResult save(@RequestBody WxRescueCase rescueCase) {
        int result = wxRescueCaseService.saveRescueCase(rescueCase);
        if (result > 0) {
            String message = "2".equals(rescueCase.getRescueStatus()) ? "提交成功" : "保存成功";
            return AjaxResult.success(message, rescueCase.getId());
        }
        String message = "2".equals(rescueCase.getRescueStatus()) ? "提交失败" : "保存失败";
        return AjaxResult.error(message);
    }

    /**
     * 修改急救案例
     */
    @PostMapping("/update")
    public AjaxResult update(@RequestBody WxRescueCase rescueCase) {
        int result = wxRescueCaseService.updateRescueCase(rescueCase);
        if (result > 0) {
            String message = "2".equals(rescueCase.getRescueStatus()) ? "提交成功" : "修改成功";
            return AjaxResult.success(message);
        }
        String message = "2".equals(rescueCase.getRescueStatus()) ? "提交失败" : "修改失败";
        return AjaxResult.error(message);
    }

    /**
     * 删除急救案例
     */
    @PostMapping("/delete/{ids}")
    public AjaxResult delete(@PathVariable("ids") Long[] ids) {
        int result = wxRescueCaseService.deleteRescueCaseByIds(ids);
        if (result > 0) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.error("删除失败");
    }


}
