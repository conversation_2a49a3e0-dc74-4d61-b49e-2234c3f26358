package com.drxin.wechat.controller;

import com.alibaba.fastjson2.JSONObject;
import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.common.core.domain.R;
import com.drxin.common.core.page.TableDataInfo;
import com.drxin.wechat.domain.WxUser;
import com.drxin.wechat.service.IWxUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/wx/user")
public class WxUserController extends BaseController {

    @Autowired
    private IWxUserService wxUserService;

    // 更新用户资料
    @RequestMapping("/updateUserProfile")
    public R<Integer> updateUserProfile(@RequestBody WxUser wxUser) {

        wxUserService.updateUserProfile(wxUser);
        return R.ok();
    }

    // 获取用户个人信息
    @RequestMapping("/getPersonalInfo")
    public R<WxUser> getPersonalInfo(String userId) {
        WxUser wxUser = wxUserService.getPersonalInfo(userId);
        return R.ok(wxUser);
    }

    // 更新用户个人资料
    @RequestMapping("/updatePersonalInfo")
    public R<Integer> updatePersonalInfo(@RequestBody WxUser wxUser) {
        wxUserService.updatePersonalInfo(wxUser);
        return R.ok();
    }

    // 获取用户个人二维码
    @GetMapping("/personalQrCode")
    public R<String> personalQrCode(String userId) {
        String qrCode = wxUserService.selectPersonalQrCode(userId);
        return R.ok(qrCode);
    }

    // 身份证信息识别
    @PostMapping("/idCardInfo")
    public R<JSONObject> idCardInfo(MultipartFile file) {
        JSONObject idCardInfo = wxUserService.getIDCardInfo(file);
        return R.ok(idCardInfo);
    }

    @GetMapping("/regenerateInviterCode")
    public R<String> regenerateInviterCode(String userId) {
        String inviterCode = wxUserService.regenerateInviterCode(userId);
        return R.ok(inviterCode);
    }

    @GetMapping("/getRecommendList")
    public AjaxResult getRecommendList(WxUser wxUser) {
        startPage();
        return AjaxResult.success(getDataTable(wxUserService.getRecommendList(wxUser)));
    }
}
