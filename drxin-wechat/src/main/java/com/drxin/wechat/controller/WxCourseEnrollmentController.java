package com.drxin.wechat.controller;

import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.R;
import com.drxin.common.core.page.TableDataInfo;
import com.drxin.wechat.domain.WxCourseEnrollment;
import com.drxin.wechat.service.IWxCourseEnrollmentService;
import com.drxin.wechat.vo.WxEnrollCourseVo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/wx/course_enrollment")
public class WxCourseEnrollmentController extends BaseController {

    @Resource
    private IWxCourseEnrollmentService wxCourseEnrollmentService;

    @GetMapping("/enrollCourseList")
    public TableDataInfo enrollCourseList(Long userId) {
        startPage();
        List<WxEnrollCourseVo> wxCourses = wxCourseEnrollmentService.enrollCourseList(userId);
        return getDataTable(wxCourses);
    }

    /**
     * 报名课程
     */
    @PostMapping("/enroll")
    public R<String> enrollCourse(@RequestBody WxCourseEnrollment enrollment) {
        wxCourseEnrollmentService.insertCourseEnrollmentRecord(enrollment);
        return R.ok("报名成功");
    }

    /**
     * 取消报名
     */
    @PostMapping("/cancelEnrollment")
    public R<String> cancelEnrollment(@RequestBody WxCourseEnrollment enrollment) {
        wxCourseEnrollmentService.cancelEnrollment(enrollment);
        return R.ok("取消报名成功");
    }

}
