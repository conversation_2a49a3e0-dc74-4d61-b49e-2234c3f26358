package com.drxin.wechat.controller;

import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.R;
import com.drxin.wechat.domain.WxCourseAttendance;
import com.drxin.wechat.service.IWxCourseAttendanceService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


@RestController
@RequestMapping("/wx/course_attendance")
public class WxCourseAttendanceController extends BaseController {
    @Resource
    private IWxCourseAttendanceService wxCourseAttendanceService;

    @PostMapping("/checkin")
    public R<WxCourseAttendance> recordAttendance(@RequestBody WxCourseAttendance attendance) {
        wxCourseAttendanceService.recordAttendance(attendance);
        return R.ok(attendance);
    }

    @GetMapping("/check-status")
    public R<WxCourseAttendance> checkCourseAttendStatus(@RequestParam("userId") Long userId, @RequestParam("courseId") Long courseId) {
        WxCourseAttendance status = wxCourseAttendanceService.checkCourseAttendStatus(userId, courseId);
        if (status != null) {
            return R.ok(status);
        } else {
            return R.fail("未找到考勤记录");
        }
    }


}
