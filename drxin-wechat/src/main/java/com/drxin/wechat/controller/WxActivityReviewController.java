package com.drxin.wechat.controller;

import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.common.core.page.TableDataInfo;
import com.drxin.wechat.domain.WxActivityReview;
import com.drxin.wechat.service.IWxActivityReviewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 微信活动评审Controller
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/wechat/activity_review")
public class WxActivityReviewController extends BaseController {

    @Autowired
    private IWxActivityReviewService wxActivityReviewService;

    /**
     * 查询活动评审列表
     */
    @GetMapping("/list")
    public TableDataInfo list(WxActivityReview activityReview) {
        startPage();
        List<WxActivityReview> list = wxActivityReviewService.selectActivityReviewList(activityReview);
        return getDataTable(list);
    }

    /**
     * 获取活动评审详细信息
     */
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        WxActivityReview activityReview = wxActivityReviewService.getActivityReviewInfo(id);
        if (activityReview == null) {
            return AjaxResult.error("活动不存在");
        }
        return AjaxResult.success(activityReview);
    }

    /**
     * 新增活动评审
     */
    @PostMapping("/save")
    public AjaxResult save(@RequestBody WxActivityReview activityReview) {
        int result = wxActivityReviewService.saveActivityReview(activityReview);
        if (result > 0) {
            String message = "2".equals(activityReview.getReviewStatus()) ? "提交成功" : "保存成功";
            return AjaxResult.success(message);
        }
        String message = "2".equals(activityReview.getReviewStatus()) ? "提交失败" : "保存失败";
        return AjaxResult.error(message);
    }

    /**
     * 修改活动评审
     */
    @PostMapping("/update")
    public AjaxResult update(@RequestBody WxActivityReview activityReview) {
        int result = wxActivityReviewService.updateActivityReview(activityReview);
        if (result > 0) {
            String message = "2".equals(activityReview.getReviewStatus()) ? "提交成功" : "修改成功";
            return AjaxResult.success(message);
        }
        String message = "2".equals(activityReview.getReviewStatus()) ? "提交失败" : "修改失败";
        return AjaxResult.error(message);
    }

    /**
     * 删除活动评审
     */
    @PostMapping("/delete/{ids}")
    public AjaxResult delete(@PathVariable("ids") Long[] ids) {
        int result = wxActivityReviewService.deleteActivityReviewByIds(ids);
        if (result > 0) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.error("删除失败");
    }
}
