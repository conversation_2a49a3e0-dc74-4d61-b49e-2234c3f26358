package com.drxin.wechat.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.wechat.domain.WxCretApply;
import com.drxin.wechat.service.IWxCretApplyService;
import com.drxin.wechat.vo.WxCretApplyVo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/wx/cret-apply")
public class WxCretApplyController extends BaseController {

    @Resource
    private IWxCretApplyService iCretApplyService;

    // 列表
    @GetMapping("/list")
    public AjaxResult list(WxCretApplyVo apply) {
        startPage();
        List<WxCretApply> list = iCretApplyService.selectCretApplyList(apply);
        return AjaxResult.success(getDataTable(list));
    }

    // 查询
    @GetMapping("/getApplyInfo/{id}")
    public AjaxResult getApplyInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(iCretApplyService.getApplyInfo(id));
    }

    // 保存
    @PostMapping("/save")
    public AjaxResult save(@RequestBody WxCretApply apply) {
        iCretApplyService.insertCretApply(apply);
        return AjaxResult.success(apply.getId());
    }

    // 提交
    @PostMapping("/submit/{id}")
    public AjaxResult submit(@PathVariable("id") Long id) {
        iCretApplyService.submitCretApply(id);
        return AjaxResult.success();
    }

    // 更新
    @PostMapping("/update")
    public AjaxResult update(@RequestBody WxCretApply apply) {
        iCretApplyService.updateCretApply(apply);
        return AjaxResult.success();
    }

    // 删除
    @PostMapping("/delete/{ids}")
    public AjaxResult delete(@PathVariable("ids") Long[] ids) {
        iCretApplyService.deleteCretApplyByIds(ids);
        return AjaxResult.success();
    }
}
