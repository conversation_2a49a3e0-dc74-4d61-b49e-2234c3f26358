<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.wechat.mapper.WxRescueCaseMapper">

    <resultMap type="com.drxin.wechat.domain.WxRescueCase" id="WxRescueCaseResult">
        <id property="id" column="id"/>
        <result property="rescueDate" column="rescue_date"/>
        <result property="city" column="city"/>
        <result property="address" column="address"/>
        <result property="patientName" column="patient_name"/>
        <result property="patientGender" column="patient_gender"/>
        <result property="patientAge" column="patient_age"/>
        <result property="illnessType" column="illness_type"/>
        <result property="rescueDescription" column="rescue_description"/>
        <result property="rescueStatus" column="rescue_status"/>
        <result property="onlineFlag" column="online_flag"/>
        <result property="remoteGuideUserId" column="remote_guide_user_id"/>
        <result property="remoteGuideRealName" column="remote_guide_real_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectWxRescueCaseVo">
        select id, rescue_date, city, address, patient_name, patient_gender, patient_age, 
               illness_type, rescue_description, rescue_status, online_flag, remote_guide_user_id, 
               remote_guide_real_name, create_by, create_time, update_by, update_time 
        from rescue_case
    </sql>

    <select id="selectRescueCaseList" parameterType="com.drxin.wechat.vo.WxRescueCaseVo" resultMap="WxRescueCaseResult">
        <include refid="selectWxRescueCaseVo"/>
        <where>
            <if test="rescueDate != null">
                and rescue_date = #{rescueDate}
            </if>
            <if test="city != null and city != ''">
                and city like concat('%', #{city}, '%')
            </if>
            <if test="address != null and address != ''">
                and address like concat('%', #{address}, '%')
            </if>
            <if test="patientName != null and patientName != ''">
                and patient_name like concat('%', #{patientName}, '%')
            </if>
            <if test="patientGender != null and patientGender != ''">
                and patient_gender = #{patientGender}
            </if>
            <if test="patientAge != null and patientAge != ''">
                and patient_age = #{patientAge}
            </if>
            <if test="illnessType != null and illnessType != ''">
                and illness_type like concat('%', #{illnessType}, '%')
            </if>
            <if test="rescueStatus != null and rescueStatus != ''">
                and rescue_status = #{rescueStatus}
            </if>
            <if test="onlineFlag != null and onlineFlag != ''">
                and online_flag = #{onlineFlag}
            </if>
            <if test="remoteGuideUserId != null">
                and remote_guide_user_id = #{remoteGuideUserId}
            </if>
            <if test="remoteGuideRealName != null and remoteGuideRealName != ''">
                and remote_guide_real_name like concat('%', #{remoteGuideRealName}, '%')
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="beginTime != null and beginTime != ''">
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''">
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="getRescueCaseInfo" parameterType="Long" resultMap="WxRescueCaseResult">
        <include refid="selectWxRescueCaseVo"/>
        where id = #{id}
    </select>

    <insert id="insertRescueCase" parameterType="com.drxin.wechat.domain.WxRescueCase" useGeneratedKeys="true" keyProperty="id">
        insert into rescue_case
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rescueDate != null">rescue_date,</if>
            <if test="city != null and city != ''">city,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="patientName != null and patientName != ''">patient_name,</if>
            <if test="patientGender != null and patientGender != ''">patient_gender,</if>
            <if test="patientAge != null and patientAge != ''">patient_age,</if>
            <if test="illnessType != null and illnessType != ''">illness_type,</if>
            <if test="rescueDescription != null and rescueDescription != ''">rescue_description,</if>
            <if test="rescueStatus != null and rescueStatus != ''">rescue_status,</if>
            <if test="onlineFlag != null and onlineFlag != ''">online_flag,</if>
            <if test="remoteGuideUserId != null">remote_guide_user_id,</if>
            <if test="remoteGuideRealName != null and remoteGuideRealName != ''">remote_guide_real_name,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rescueDate != null">#{rescueDate},</if>
            <if test="city != null and city != ''">#{city},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="patientName != null and patientName != ''">#{patientName},</if>
            <if test="patientGender != null and patientGender != ''">#{patientGender},</if>
            <if test="patientAge != null and patientAge != ''">#{patientAge},</if>
            <if test="illnessType != null and illnessType != ''">#{illnessType},</if>
            <if test="rescueDescription != null and rescueDescription != ''">#{rescueDescription},</if>
            <if test="rescueStatus != null and rescueStatus != ''">#{rescueStatus},</if>
            <if test="onlineFlag != null and onlineFlag != ''">#{onlineFlag},</if>
            <if test="remoteGuideUserId != null">#{remoteGuideUserId},</if>
            <if test="remoteGuideRealName != null and remoteGuideRealName != ''">#{remoteGuideRealName},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateRescueCase" parameterType="com.drxin.wechat.domain.WxRescueCase">
        update rescue_case
        <trim prefix="SET" suffixOverrides=",">
            <if test="rescueDate != null">rescue_date = #{rescueDate},</if>
            <if test="city != null and city != ''">city = #{city},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="patientName != null and patientName != ''">patient_name = #{patientName},</if>
            <if test="patientGender != null and patientGender != ''">patient_gender = #{patientGender},</if>
            <if test="patientAge != null and patientAge != ''">patient_age = #{patientAge},</if>
            <if test="illnessType != null and illnessType != ''">illness_type = #{illnessType},</if>
            <if test="rescueDescription != null and rescueDescription != ''">rescue_description = #{rescueDescription},</if>
            <if test="rescueStatus != null and rescueStatus != ''">rescue_status = #{rescueStatus},</if>
            <if test="onlineFlag != null and onlineFlag != ''">online_flag = #{onlineFlag},</if>
            <if test="remoteGuideUserId != null">remote_guide_user_id = #{remoteGuideUserId},</if>
            <if test="remoteGuideRealName != null and remoteGuideRealName != ''">remote_guide_real_name = #{remoteGuideRealName},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRescueCaseByIds" parameterType="Long">
        delete from rescue_case where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
