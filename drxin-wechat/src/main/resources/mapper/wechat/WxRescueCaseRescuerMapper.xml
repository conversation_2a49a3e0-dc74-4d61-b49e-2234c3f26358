<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.wechat.mapper.WxRescueCaseRescuerMapper">

    <resultMap type="com.drxin.wechat.domain.WxRescueCaseRescuer" id="WxRescueCaseRescuerResult">
        <id property="id" column="id"/>
        <result property="caseId" column="case_id"/>
        <result property="rescuerId" column="rescuer_id"/>
        <result property="rescuerName" column="rescuer_name"/>
        <result property="rescuerPhone" column="rescuer_phone"/>
        <result property="rescuerIdCard" column="rescuer_id_card"/>
        <result property="score" column="score"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectWxRescueCaseRescuerVo">
        select id, case_id, rescuer_id, rescuer_name, rescuer_phone, rescuer_id_card, score,
               create_by, create_time, update_by, update_time
        from rescue_case_rescuer
    </sql>

    <select id="selectRescueCaseRescuerList" parameterType="com.drxin.wechat.domain.WxRescueCaseRescuer" resultMap="WxRescueCaseRescuerResult">
        <include refid="selectWxRescueCaseRescuerVo"/>
        <where>
            <if test="caseId != null">
                and case_id = #{caseId}
            </if>
            <if test="rescuerId != null">
                and rescuer_id = #{rescuerId}
            </if>
            <if test="rescuerName != null and rescuerName != ''">
                and rescuer_name like concat('%', #{rescuerName}, '%')
            </if>
            <if test="rescuerPhone != null and rescuerPhone != ''">
                and rescuer_phone = #{rescuerPhone}
            </if>
            <if test="rescuerIdCard != null and rescuerIdCard != ''">
                and rescuer_id_card = #{rescuerIdCard}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectRescuersByCaseId" parameterType="Long" resultMap="WxRescueCaseRescuerResult">
        <include refid="selectWxRescueCaseRescuerVo"/>
        where case_id = #{caseId}
        order by create_time desc
    </select>

    <insert id="insertRescueCaseRescuer" parameterType="com.drxin.wechat.domain.WxRescueCaseRescuer" useGeneratedKeys="true" keyProperty="id">
        insert into rescue_case_rescuer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="caseId != null">case_id,</if>
            <if test="rescuerId != null">rescuer_id,</if>
            <if test="rescuerName != null and rescuerName != ''">rescuer_name,</if>
            <if test="rescuerPhone != null and rescuerPhone != ''">rescuer_phone,</if>
            <if test="rescuerIdCard != null and rescuerIdCard != ''">rescuer_id_card,</if>
            <if test="score != null and score != ''">score,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="caseId != null">#{caseId},</if>
            <if test="rescuerId != null">#{rescuerId},</if>
            <if test="rescuerName != null and rescuerName != ''">#{rescuerName},</if>
            <if test="rescuerPhone != null and rescuerPhone != ''">#{rescuerPhone},</if>
            <if test="rescuerIdCard != null and rescuerIdCard != ''">#{rescuerIdCard},</if>
            <if test="score != null and score != ''">#{score},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateRescueCaseRescuer" parameterType="com.drxin.wechat.domain.WxRescueCaseRescuer">
        update rescue_case_rescuer
        <trim prefix="SET" suffixOverrides=",">
            <if test="caseId != null">case_id = #{caseId},</if>
            <if test="rescuerId != null">rescuer_id = #{rescuerId},</if>
            <if test="rescuerName != null and rescuerName != ''">rescuer_name = #{rescuerName},</if>
            <if test="rescuerPhone != null and rescuerPhone != ''">rescuer_phone = #{rescuerPhone},</if>
            <if test="rescuerIdCard != null and rescuerIdCard != ''">rescuer_id_card = #{rescuerIdCard},</if>
            <if test="score != null and score != ''">score = #{score},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRescuersByCaseId" parameterType="Long">
        delete from rescue_case_rescuer where case_id = #{caseId}
    </delete>

    <delete id="deleteRescueCaseRescuerByIds" parameterType="Long">
        delete from rescue_case_rescuer where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into rescue_case_rescuer (case_id, rescuer_id, rescuer_name, rescuer_phone, rescuer_id_card, create_by, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.caseId}, #{item.rescuerId}, #{item.rescuerName}, #{item.rescuerPhone}, #{item.rescuerIdCard}, #{item.createBy}, #{item.createTime})
        </foreach>
    </insert>

</mapper>
