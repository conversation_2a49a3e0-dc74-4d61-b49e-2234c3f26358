<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.wechat.mapper.WxUserApplyMapper">

    <resultMap type="com.drxin.wechat.domain.WxUserApply" id="WxUserApplyResult">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="applyStatus" column="apply_status"/>
        <result property="userType" column="user_type"/>
        <result property="realName" column="real_name"/>
        <result property="cardType" column="card_type"/>
        <result property="idCard" column="id_card"/>
        <result property="sex" column="sex"/>
        <result property="phoneNumber" column="phone_number"/>
        <result property="inviterId" column="inviter_id"/>
        <result property="inviterName" column="inviter_name"/>
        <result property="photoId" column="photo_id"/>
        <result property="photoUrl" column="photo_url"/>
        <result property="rejectReason" column="reject_reason"/>
    </resultMap>

    <sql id="selectWxUserApplyVo">
        select id, user_id, apply_status, user_type, real_name, card_type, id_card, sex, phone_number, inviter_id, inviter_name, photo_id, photo_url, reject_reason
        from user_apply
    </sql>

    <select id="selectUserApplyByUserId" parameterType="Long" resultMap="WxUserApplyResult">
        <include refid="selectWxUserApplyVo"/>
        where user_id = #{userId}
        limit 1
    </select>

    <select id="selectUserApplyByUserIdAndType" resultMap="WxUserApplyResult">
        <include refid="selectWxUserApplyVo"/>
        where user_id = #{userId} and user_type = #{userType}
        limit 1
    </select>

    <select id="selectUserApplyByUserIdAndStatus" resultMap="WxUserApplyResult">
        <include refid="selectWxUserApplyVo"/>
        where user_id = #{userId} and apply_status = #{applyStatus}
        limit 1
    </select>

    <insert id="insertUserApply" parameterType="com.drxin.wechat.domain.WxUserApply" useGeneratedKeys="true" keyProperty="id">
        insert into user_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="applyStatus != null and applyStatus != ''">apply_status,</if>
            <if test="userType != null and userType != ''">user_type,</if>
            <if test="realName != null and realName != ''">real_name,</if>
            <if test="cardType != null and cardType != ''">card_type,</if>
            <if test="idCard != null and idCard != ''">id_card,</if>
            <if test="sex != null and sex != ''">sex,</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number,</if>
            <if test="inviterId != null">inviter_id,</if>
            <if test="inviterName != null and inviterName != ''">inviter_name,</if>
            <if test="photoId != null">photo_id,</if>
            <if test="photoUrl != null and photoUrl != ''">photo_url,</if>
            <if test="rejectReason != null and rejectReason != ''">reject_reason,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="applyStatus != null and applyStatus != ''">#{applyStatus},</if>
            <if test="userType != null and userType != ''">#{userType},</if>
            <if test="realName != null and realName != ''">#{realName},</if>
            <if test="cardType != null and cardType != ''">#{cardType},</if>
            <if test="idCard != null and idCard != ''">#{idCard},</if>
            <if test="sex != null and sex != ''">#{sex},</if>
            <if test="phoneNumber != null and phoneNumber != ''">#{phoneNumber},</if>
            <if test="inviterId != null">#{inviterId},</if>
            <if test="inviterName != null and inviterName != ''">#{inviterName},</if>
            <if test="photoId != null">#{photoId},</if>
            <if test="photoUrl != null and photoUrl != ''">#{photoUrl},</if>
            <if test="rejectReason != null and rejectReason != ''">#{rejectReason},</if>
        </trim>
    </insert>

    <update id="updateUserApply" parameterType="com.drxin.wechat.domain.WxUserApply">
        update user_apply
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="applyStatus != null and applyStatus != ''">apply_status = #{applyStatus},</if>
            <if test="userType != null and userType != ''">user_type = #{userType},</if>
            <if test="realName != null and realName != ''">real_name = #{realName},</if>
            <if test="cardType != null and cardType != ''">card_type = #{cardType},</if>
            <if test="idCard != null and idCard != ''">id_card = #{idCard},</if>
            <if test="sex != null and sex != ''">sex = #{sex},</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number = #{phoneNumber},</if>
            <if test="inviterId != null">inviter_id = #{inviterId},</if>
            <if test="inviterName != null and inviterName != ''">inviter_name = #{inviterName},</if>
            <if test="photoId != null">photo_id = #{photoId},</if>
            <if test="photoUrl != null and photoUrl != ''">photo_url = #{photoUrl},</if>
        </trim>
        where id = #{id}
    </update>

</mapper>
