<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.wechat.mapper.WxCourseMapper">

    <resultMap id="BaseResultMap" type="com.drxin.wechat.domain.WxCourse">
        <result property="id"    column="id"    />
        <result property="courseName"    column="course_name"    />
        <result property="description"    column="description"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="location"    column="location"    />
        <result property="maxQuota"    column="max_quota"    />
        <result property="currentQuota"    column="current_quota"    />
        <result property="enrollDeadline"    column="enroll_deadline"    />
        <result property="courseStatus"    column="course_status"    />
        <result property="coursePrice"    column="course_price"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWxCourseVo">
        select id, course_name, description, start_time, end_time, location, max_quota, current_quota, enroll_deadline,
               course_status, course_price, create_by, create_time, update_by, update_time from course
    </sql>
    <select id="selectWxCourseList" parameterType="com.drxin.wechat.domain.WxCourse" resultMap="BaseResultMap">
        <include refid="selectWxCourseVo"/>
        <where>
            course_status = '1'
            <if test="courseName != null and courseName != ''"> and course_name like concat('%', #{courseName}, '%')</if>
        </where>
        order by course_status desc, start_time asc
    </select>

    <select id="selectWxCourseById" parameterType="Long" resultMap="BaseResultMap">
        <include refid="selectWxCourseVo"/>
        where id = #{id}
    </select>


</mapper>