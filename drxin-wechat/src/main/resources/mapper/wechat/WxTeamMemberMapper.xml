<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.wechat.mapper.WxTeamMemberMapper">
    
    <resultMap type="com.drxin.wechat.domain.WxTeamMember" id="WxTeamMemberResult">
        <result property="memberId"    column="member_id"    />
        <result property="teamId"    column="team_id"    />
        <result property="memberName"    column="member_name"    />
        <result property="memberType"    column="member_type"    />
        <result property="upgradedTime"    column="upgraded_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWxTeamMemberVo">
        select member_id, team_id, member_name, member_type, upgraded_time, create_by, create_time, update_by, update_time from team_member
    </sql>

    <select id="selectMembersByUserId" parameterType="String" resultMap="WxTeamMemberResult">
        <include refid="selectWxTeamMemberVo"/>
        where team_id = (
            select team_id from team_member where member_id = #{userId} limit 1
        )
        order by create_time desc
    </select>
    
    <select id="selectMembersByTeamId" parameterType="String" resultMap="WxTeamMemberResult">
        <include refid="selectWxTeamMemberVo"/>
        where team_id = #{teamId}
        order by create_time desc
    </select>

</mapper>
