<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.wechat.mapper.WxActivityReviewUserMapper">
    
    <resultMap type="com.drxin.wechat.domain.WxActivityReviewUser" id="WxActivityReviewUserResult">
        <result property="id"           column="id" />
        <result property="activityId"   column="activity_id" />
        <result property="userId"       column="user_id" />
        <result property="roleType"     column="role_type" />
        <result property="userName"     column="user_name" />
        <result property="createBy"     column="create_by" />
        <result property="createTime"   column="create_time" />
        <result property="updateBy"     column="update_by" />
        <result property="updateTime"   column="update_time" />
    </resultMap>

    <sql id="selectWxActivityReviewUserVo">
        select aru.id, aru.activity_id, aru.user_id, aru.role_type,
               COALESCE(u.real_name, u.user_name) as user_name,
               aru.create_by, aru.create_time, aru.update_by, aru.update_time
        from activity_review_user aru
        left join sys_user u on aru.user_id = u.user_id
    </sql>

    <select id="selectUsersByActivityId" parameterType="Long" resultMap="WxActivityReviewUserResult">
        <include refid="selectWxActivityReviewUserVo"/>
        where aru.activity_id = #{activityId}
        order by aru.role_type, aru.create_time
    </select>

    <select id="selectUsersByActivityIdAndRoleType" resultMap="WxActivityReviewUserResult">
        <include refid="selectWxActivityReviewUserVo"/>
        where aru.activity_id = #{activityId} and aru.role_type = #{roleType}
        order by aru.create_time
    </select>
        
    <insert id="insertActivityReviewUser" parameterType="com.drxin.wechat.domain.WxActivityReviewUser" useGeneratedKeys="true" keyProperty="id">
        insert into activity_review_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="activityId != null">activity_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="roleType != null">role_type,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="activityId != null">#{activityId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="roleType != null">#{roleType},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into activity_review_user (activity_id, user_id, role_type, create_by, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.activityId}, #{item.userId}, #{item.roleType}, #{item.createBy}, #{item.createTime})
        </foreach>
    </insert>

    <delete id="deleteUsersByActivityId" parameterType="Long">
        delete from activity_review_user where activity_id = #{activityId}
    </delete>
</mapper>
