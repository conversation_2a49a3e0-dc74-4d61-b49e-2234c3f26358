<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.wechat.mapper.WxActivityReviewAttachmentMapper">
    
    <resultMap type="com.drxin.wechat.domain.WxActivityReviewAttachment" id="WxActivityReviewAttachmentResult">
        <result property="id"               column="id" />
        <result property="activityId"       column="activity_id" />
        <result property="attachmentId"     column="attachment_id" />
        <result property="attachmentUrl"    column="attachment_url" />
        <result property="attachmentType"   column="attachment_type" />
        <result property="createBy"         column="create_by" />
        <result property="createTime"       column="create_time" />
        <result property="updateBy"         column="update_by" />
        <result property="updateTime"       column="update_time" />
    </resultMap>

    <sql id="selectWxActivityReviewAttachmentVo">
        select id, activity_id, attachment_id, attachment_url, attachment_type, create_by, create_time, update_by, update_time
        from activity_review_attachments
    </sql>

    <select id="selectAttachmentsByActivityId" parameterType="Long" resultMap="WxActivityReviewAttachmentResult">
        <include refid="selectWxActivityReviewAttachmentVo"/>
        where activity_id = #{activityId}
        order by create_time desc
    </select>
        
    <insert id="insertActivityReviewAttachment" parameterType="com.drxin.wechat.domain.WxActivityReviewAttachment" useGeneratedKeys="true" keyProperty="id">
        insert into activity_review_attachments
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="activityId != null">activity_id,</if>
            <if test="attachmentId != null and attachmentId != ''">attachment_id,</if>
            <if test="attachmentUrl != null and attachmentUrl != ''">attachment_url,</if>
            <if test="attachmentType != null and attachmentType != ''">attachment_type,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="activityId != null">#{activityId},</if>
            <if test="attachmentId != null and attachmentId != ''">#{attachmentId},</if>
            <if test="attachmentUrl != null and attachmentUrl != ''">#{attachmentUrl},</if>
            <if test="attachmentType != null and attachmentType != ''">#{attachmentType},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into activity_review_attachments (activity_id, attachment_id, attachment_url, attachment_type, create_by, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.activityId}, #{item.attachmentId}, #{item.attachmentUrl}, #{item.attachmentType}, #{item.createBy}, #{item.createTime})
        </foreach>
    </insert>

    <delete id="deleteAttachmentsByActivityId" parameterType="Long">
        delete from activity_review_attachments where activity_id = #{activityId}
    </delete>
</mapper>
