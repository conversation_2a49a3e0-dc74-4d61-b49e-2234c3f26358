<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.wechat.mapper.WxUserAddressMapper">
    
    <resultMap type="com.drxin.wechat.domain.WxUserAddress" id="WxUserAddressResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="name"    column="name"    />
        <result property="phone"    column="phone"    />
        <result property="sex"    column="sex"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="district"    column="district"    />
        <result property="detail"    column="detail"    />
        <result property="address"    column="address"    />
        <result property="defaultFlag"    column="default_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUserAddressVo">
        select id, user_id, name, phone, sex, province, city, district, detail, address, default_flag,
               create_by, update_by, create_time, update_time from user_address
    </sql>

    <select id="selectUserAddressList" parameterType="com.drxin.wechat.domain.WxUserAddress" resultMap="WxUserAddressResult">
        <include refid="selectUserAddressVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="sex != null  and sex != ''"> and sex = #{sex}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="district != null  and district != ''"> and district = #{district}</if>
            <if test="detail != null  and detail != ''"> and detail = #{detail}</if>
            <if test="userId!= null  and userId!= ''"> and user_id = #{userId}</if>
            <if test="searchValue!= null  and searchValue!= ''"> and (name like concat('%', #{searchValue}, '%') or phone like concat('%', #{searchValue}, '%'))</if>
        </where>
    </select>
    
    <select id="selectUserAddressById" parameterType="String" resultMap="WxUserAddressResult">
        <include refid="selectUserAddressVo"/>
        where id = #{id}
    </select>

    <insert id="insertUserAddress" parameterType="com.drxin.wechat.domain.WxUserAddress" useGeneratedKeys="true" keyProperty="id">
        insert into user_address
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">user_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="sex != null">sex,</if>
            <if test="province != null and province != ''">province,</if>
            <if test="city != null and city != ''">city,</if>
            <if test="district != null and district != ''">district,</if>
            <if test="detail != null and detail != ''">detail,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="defaultFlag!= null and defaultFlag!= ''">default_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="sex != null">#{sex},</if>
            <if test="province != null and province != ''">#{province},</if>
            <if test="city != null and city != ''">#{city},</if>
            <if test="district != null and district != ''">#{district},</if>
            <if test="detail != null and detail != ''">#{detail},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="defaultFlag!= null and defaultFlag!= ''">#{defaultFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateUserAddress" parameterType="com.drxin.wechat.domain.WxUserAddress">
        update user_address
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="province != null and province != ''">province = #{province},</if>
            <if test="city != null and city != ''">city = #{city},</if>
            <if test="district != null and district != ''">district = #{district},</if>
            <if test="detail != null and detail != ''">detail = #{detail},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="defaultFlag!= null and defaultFlag!= ''">default_flag = #{defaultFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserAddressById" parameterType="String">
        delete from user_address where id = #{id}
    </delete>

    <delete id="deleteUserAddressByIds" parameterType="String">
        delete from user_address where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
