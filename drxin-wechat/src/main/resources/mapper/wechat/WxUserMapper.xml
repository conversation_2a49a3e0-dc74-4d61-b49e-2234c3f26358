<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.wechat.mapper.WxUserMapper">
    <resultMap type="com.drxin.wechat.domain.WxUser" id="WechatUserResult">
        <id property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="email" column="email"/>
        <result property="phonenumber" column="phonenumber"/>
        <result property="avatar" column="avatar"/>
        <result property="realName" column="real_name"/>
        <result property="idCard" column="id_card"/>
        <result property="education" column="education"/>
        <result property="companyName" column="company_name"/>
        <result property="photoUrl" column="photo_url"/>
    </resultMap>
    <update id="updateUserProfile" parameterType="com.drxin.wechat.domain.WxUser">
        update sys_user set
        nick_name = #{nickName},
        email = #{email},
        avatar = #{avatar},
        phonenumber = #{phonenumber}
        where user_id = #{userId}
    </update>

</mapper>