<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.wechat.mapper.WxTeamMapper">
    
    <resultMap type="com.drxin.wechat.domain.WxTeam" id="WxTeamResult">
        <result property="teamId"    column="team_id"    />
        <result property="teamName"    column="team_name"    />
        <result property="leaderId"    column="leader_id"    />
        <result property="leaderName"    column="leader_name"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectWxTeamVo">
        select team_id, team_name, leader_id, leader_name, create_time from team
    </sql>

    <select id="selectTeamByUserId" parameterType="String" resultMap="WxTeamResult">
        <include refid="selectWxTeamVo"/>
        where leader_id = #{userId}
    </select>
    
    <select id="selectTeamByTeamId" parameterType="String" resultMap="WxTeamResult">
        <include refid="selectWxTeamVo"/>
        where team_id = #{teamId}
    </select>

    <select id="checkTeamNameUnique" parameterType="String" resultMap="WxTeamResult">
        <include refid="selectWxTeamVo"/>
        where team_name = #{teamName}
        limit 1
    </select>

</mapper>
