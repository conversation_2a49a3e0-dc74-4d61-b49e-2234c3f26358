<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.wechat.mapper.WxCourseEnrollmentMapper">
    <resultMap id="BaseRusltMap" type="com.drxin.wechat.domain.WxCourseEnrollment">
        <result property="id" column="id"/>
        <result property="courseId" column="course_id"/>
        <result property="userId" column="user_id"/>
        <result property="enrollTime" column="enroll_time"/>
        <result property="cancelTime" column="cancel_time"/>
        <result property="enrollStatus" column="enroll_status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <resultMap id="enrollCourseVo" type="com.drxin.wechat.vo.WxEnrollCourseVo">
        <result property="courseId" column="id"/>
        <result property="courseName" column="course_name"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="location" column="location"/>
        <result property="enrollTime" column="enroll_time"/>
    </resultMap>

    <sql id="selectWxCourseEnrollmentVo">
        select id, course_id, user_id, enroll_time, cancel_time, enroll_status, create_by, create_time, update_by, update_time from course_enrollment
    </sql>
    <insert id="insertCourseEnrollmentRecord" parameterType="com.drxin.wechat.domain.WxCourseEnrollment" useGeneratedKeys="true" keyProperty="id">
        insert into course_enrollment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="courseId != null">course_id,</if>
            <if test="userId != null">user_id,</if>
            enroll_time,
            <if test="cancelTime != null">cancel_time,</if>
            <if test="enrollStatus != null and enrollStatus != ''">enroll_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="courseId != null">#{courseId},</if>
            <if test="userId != null">#{userId},</if>
            now(),
            <if test="cancelTime != null">#{cancelTime},</if>
            <if test="enrollStatus != null and enrollStatus != ''">#{enrollStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>
    <select id="enrollCourseList" parameterType="long" resultMap="enrollCourseVo">
        select c.id, c.course_name,c.start_time,c.end_time,c.location,ce.enroll_time
        from course_enrollment ce
            left join course c on ce.course_id = c.id
        where ce.user_id = #{userId} and ce.enroll_status = '1'
        order by ce.enroll_time desc
    </select>


</mapper>