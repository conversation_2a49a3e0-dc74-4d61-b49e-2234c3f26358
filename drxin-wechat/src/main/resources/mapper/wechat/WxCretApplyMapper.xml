<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.wechat.mapper.WxCretApplyMapper">
    <resultMap type="com.drxin.wechat.domain.WxCretApply" id="CretApplyResult">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="cardType" column="card_type"/>
        <result property="phone" column="phone"/>
        <result property="sex" column="sex"/>
        <result property="education" column="education"/>
        <result property="company" column="company"/>
        <result property="photoUrl" column="photo_url"/>
        <result property="photoId" column="photo_id"/>
        <result property="heimlichUrl" column="heimlich_url"/>
        <result property="heimlichId" column="heimlich_id"/>
        <result property="paymentUrl" column="payment_url"/>
        <result property="paymentId" column="payment_id"/>
        <result property="examUrl" column="exam_url"/>
        <result property="examId" column="exam_id"/>
        <result property="applyType" column="apply_type"/>
        <result property="applyStatus" column="apply_status"/>
        <result property="receiveType" column="receive_type"/>
        <result property="addressId" column="address_id"/>
        <result property="receiveAddress" column="receive_address"/>
        <result property="practiceFlag" column="practice_flag"/>
        <result property="rejectReason" column="reject_reason"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectCretApplyVo">
            select id, name, id_card, card_type, sex, phone, education, company, photo_url,photo_id,heimlich_url,heimlich_id, payment_url,payment_id, exam_url,exam_id, apply_type, apply_status,
                receive_type, address_id, receive_address, practice_flag, reject_reason,
                create_by, create_time
            from cret_apply
    </sql>
    <delete id="deleteCretApplyByIds">
        delete from cret_apply
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectCretApplyList" resultMap="CretApplyResult">
        select ca.id, ca.name, ca.id_card, ca.card_type, ca.sex, ca.phone, ca.education, company, ca.photo_url, apply_type, apply_status,
        receive_type, address_id, receive_address, ca.reject_reason, su.real_name create_by, ca.create_time
        from cret_apply ca
        left join sys_user su on su.user_name = ca.create_by
        <where>
            <if test="name != null  and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="idCard != null  and idCard != ''">
                and id_card = #{idCard}
            </if>
            <if test="applyType != null  and applyType != ''">
                and apply_type = #{applyType}
            </if>
            <if test="applyStatus != null  and applyStatus != ''">
                and apply_status = #{applyStatus}
            </if>
            <if test="beginTime != null  and beginTime != ''">
                and date_format(ca.create_time,'%Y%m%d') &gt;= date_format(#{beginTime},'%Y%m%d')
            </if>
            <if test="endTime != null  and endTime != ''">
                and date_format(ca.create_time,'%Y%m%d') &lt;= date_format(#{endTime},'%Y%m%d')
            </if>
            <if test="createBy != null  and createBy != ''">
                and ca.create_by = #{createBy}
            </if>
            <if test="searchValue!= null  and searchValue!= ''">
                and (ca.name like concat('%', #{searchValue}, '%') or ca.id_card like concat('%', #{searchValue}, '%'))
            </if>
        </where>
        order by
            case apply_status
                when 1 then 0
                when 2 then 1
                when 3 then 2
                else 3
            end,
            id desc
    </select>
    <select id="getApplyInfo" resultMap="CretApplyResult">
        <include refid="selectCretApplyVo"/>
        where id = #{id}
    </select>

    <insert id="insertCretApply" parameterType="com.drxin.wechat.domain.WxCretApply" useGeneratedKeys="true" keyProperty="id">
        insert into cret_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="idCard != null and idCard != ''">id_card,</if>
            <if test="cardType != null and cardType != ''">card_type,</if>
            <if test="sex != null">sex,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="education != null and education != ''">education,</if>
            <if test="company != null and company != ''">company,</if>
            <if test="photoUrl != null and photoUrl != ''">photo_url,</if>
            <if test="photoId!= null and photoId!= ''">photo_id,</if>
            <if test="heimlichUrl != null and heimlichUrl != ''">heimlich_url,</if>
            <if test="heimlichId!= null and heimlichId!= ''">heimlich_id,</if>
            <if test="paymentUrl != null and paymentUrl != ''">payment_url,</if>
            <if test="paymentId!= null and paymentId!= ''">payment_id,</if>
            <if test="examUrl!= null and examUrl!= ''">exam_url,</if>
            <if test="examId!= null and examId!= ''">exam_id,</if>
            <if test="applyType != null">apply_type,</if>
            <if test="applyStatus != null">apply_status,</if>
            <if test="receiveType!= null">receive_type,</if>
            <if test="addressId!= null">address_id,</if>
            <if test="receiveAddress!= null">receive_address,</if>
            <if test="practiceFlag!= null">practice_flag,</if>
            <if test="rejectReason != null and rejectReason != ''">reject_reason,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="idCard != null and idCard != ''">#{idCard},</if>
            <if test="cardType != null and cardType != ''">#{cardType},</if>
            <if test="sex != null">#{sex},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="education != null and education != ''">#{education},</if>
            <if test="company != null and company != ''">#{company},</if>
            <if test="photoUrl != null and photoUrl != ''">#{photoUrl},</if>
            <if test="photoId!= null and photoId!= ''">#{photoId},</if>
            <if test="heimlichUrl != null and heimlichUrl != ''">#{heimlichUrl},</if>
            <if test="heimlichId!= null and heimlichId!= ''">#{heimlichId},</if>
            <if test="paymentUrl != null and paymentUrl != ''">#{paymentUrl},</if>
            <if test="paymentId!= null and paymentId!= ''">#{paymentId},</if>
            <if test="examUrl!= null and examUrl!= ''">#{examUrl},</if>
            <if test="examId!= null and examId!= ''">#{examId},</if>
            <if test="applyType != null">#{applyType},</if>
            <if test="applyStatus != null">#{applyStatus},</if>
            <if test="receiveType!= null">#{receiveType},</if>
            <if test="addressId!= null">#{addressId},</if>
            <if test="receiveAddress!= null">#{receiveAddress},</if>
            <if test="practiceFlag!= null">#{practiceFlag},</if>
            <if test="rejectReason != null and rejectReason != ''">#{rejectReason},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime}</if>
        </trim>
    </insert>

    <update id="updateCretApply" parameterType="com.drxin.wechat.domain.WxCretApply">
        update cret_apply
        <set>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="idCard != null and idCard != ''">id_card = #{idCard},</if>
            <if test="cardType != null and cardType != ''">card_type = #{cardType},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="education != null and education != ''">education = #{education},</if>
            <if test="company != null and company != ''">company = #{company},</if>
            <if test="photoUrl != null and photoUrl != ''">photo_url = #{photoUrl},</if>
            <if test="photoId!= null and photoId!= ''">photo_id = #{photoId},</if>
            <if test="heimlichUrl != null and heimlichUrl != ''">heimlich_url = #{heimlichUrl},</if>
            <if test="heimlichId!= null and heimlichId!= ''">heimlich_id = #{heimlichId},</if>
            <if test="paymentUrl != null and paymentUrl != ''">payment_url = #{paymentUrl},</if>
            <if test="paymentId!= null and paymentId!= ''">payment_id = #{paymentId},</if>
            <if test="examUrl!= null and examUrl!= ''">exam_url = #{examUrl},</if>
            <if test="examId!= null and examId!= ''">exam_id = #{examId},</if>
            <if test="applyType != null">apply_type = #{applyType},</if>
            <if test="applyStatus != null">apply_status = #{applyStatus},</if>
            <if test="receiveType!= null">receive_type = #{receiveType},</if>
            <if test="addressId!= null">address_id = #{addressId},</if>
            <if test="receiveAddress!= null">receive_address = #{receiveAddress},</if>
            <if test="practiceFlag!= null">practice_flag = #{practiceFlag},</if>
            <if test="rejectReason != null">reject_reason = #{rejectReason},</if>
            <if test="updateBy!= null">update_by = #{updateBy},</if>
            <if test="updateTime!= null">update_time = #{updateTime}</if>
        </set>
        where id = #{id}
    </update>
</mapper>