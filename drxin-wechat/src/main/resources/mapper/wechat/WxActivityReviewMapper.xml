<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.wechat.mapper.WxActivityReviewMapper">
    
    <resultMap type="com.drxin.wechat.domain.WxActivityReview" id="WxActivityReviewResult">
        <result property="id"                   column="id" />
        <result property="activityTitle"        column="activity_title" />
        <result property="startDate"            column="start_date" />
        <result property="endDate"              column="end_date" />
        <result property="lecturerName"         column="lecturer_name" />
        <result property="lecturerPhone"        column="lecturer_phone" />
        <result property="lecturer"             column="lecturer" />
        <result property="attendeeCount"        column="attendee_count" />
        <result property="activityDescription"  column="activity_description" />
        <result property="reviewStatus"         column="review_status" />
        <result property="createBy"             column="create_by" />
        <result property="createTime"           column="create_time" />
        <result property="updateBy"             column="update_by" />
        <result property="updateTime"           column="update_time" />
    </resultMap>

    <sql id="selectWxActivityReviewVo">
        select id, activity_title, start_date, end_date, lecturer_name, lecturer_phone, lecturer,
               attendee_count, activity_description, review_status, create_by, create_time,
               update_by, update_time
        from activity_review
    </sql>

    <select id="selectActivityReviewList" parameterType="com.drxin.wechat.domain.WxActivityReview" resultMap="WxActivityReviewResult">
        <include refid="selectWxActivityReviewVo"/>
        <where>
            <if test="activityTitle != null and activityTitle != ''">
                and activity_title like concat('%', #{activityTitle}, '%')
            </if>
            <if test="startDate != null">
                and DATE_FORMAT(start_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{startDate}, '%Y-%m-%d')
            </if>
            <if test="endDate != null">
                and DATE_FORMAT(end_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{endDate}, '%Y-%m-%d')
            </if>
            <if test="lecturerName != null and lecturerName != ''">
                and lecturer_name like concat('%', #{lecturerName}, '%')
            </if>
            <if test="lecturer != null and lecturer != ''">
                and lecturer like concat('%', #{lecturer}, '%')
            </if>
            <if test="reviewStatus != null and reviewStatus != ''">
                and review_status = #{reviewStatus}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="getActivityReviewInfo" parameterType="Long" resultMap="WxActivityReviewResult">
        <include refid="selectWxActivityReviewVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertActivityReview" parameterType="com.drxin.wechat.domain.WxActivityReview" useGeneratedKeys="true" keyProperty="id">
        insert into activity_review
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="activityTitle != null and activityTitle != ''">activity_title,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="lecturerName != null and lecturerName != ''">lecturer_name,</if>
            <if test="lecturerPhone != null and lecturerPhone != ''">lecturer_phone,</if>
            <if test="lecturer != null and lecturer != ''">lecturer,</if>
            <if test="attendeeCount != null">attendee_count,</if>
            <if test="activityDescription != null and activityDescription != ''">activity_description,</if>
            <if test="reviewStatus != null and reviewStatus != ''">review_status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="activityTitle != null and activityTitle != ''">#{activityTitle},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="lecturerName != null and lecturerName != ''">#{lecturerName},</if>
            <if test="lecturerPhone != null and lecturerPhone != ''">#{lecturerPhone},</if>
            <if test="attendeeCount != null">#{attendeeCount},</if>
            <if test="activityDescription != null and activityDescription != ''">#{activityDescription},</if>
            <if test="reviewStatus != null and reviewStatus != ''">#{reviewStatus},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateActivityReview" parameterType="com.drxin.wechat.domain.WxActivityReview">
        update activity_review
        <trim prefix="SET" suffixOverrides=",">
            <if test="activityTitle != null and activityTitle != ''">activity_title = #{activityTitle},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="lecturerName != null and lecturerName != ''">lecturer_name = #{lecturerName},</if>
            <if test="lecturerPhone != null and lecturerPhone != ''">lecturer_phone = #{lecturerPhone},</if>
            <if test="attendeeCount != null">attendee_count = #{attendeeCount},</if>
            <if test="activityDescription != null and activityDescription != ''">activity_description = #{activityDescription},</if>
            <if test="reviewStatus != null and reviewStatus != ''">review_status = #{reviewStatus},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteActivityReviewById" parameterType="Long">
        delete from activity_review where id = #{id}
    </delete>

    <delete id="deleteActivityReviewByIds" parameterType="String">
        delete from activity_review where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
