package com.drxin.web.controller.system;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.drxin.common.constant.SysUserTypeEnum;
import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.common.core.domain.entity.SysRole;
import com.drxin.common.core.domain.entity.SysUser;
import com.drxin.common.core.domain.model.LoginUser;
import com.drxin.common.core.domain.model.TokenPair;
import com.drxin.common.utils.SecurityUtils;
import com.drxin.common.utils.StringUtils;
import com.drxin.common.utils.WeChatMiniProgramUtil;
import com.drxin.framework.manager.AsyncManager;
import com.drxin.framework.manager.factory.AsyncFactory;
import com.drxin.framework.web.domain.server.Sys;
import com.drxin.framework.web.service.TokenService;
import com.drxin.system.service.ISysUserService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

@RestController
@RequestMapping("/wechat")
public class WechatLoginController extends BaseController {

    @Resource
    private WeChatMiniProgramUtil weChatMiniProgramUtil;

    @Resource
    private TokenService tokenService;

    @Resource
    private ISysUserService userService;

    @PostMapping("/login")
    public AjaxResult login(@RequestBody Map<String, String> body) {
        String inviterId = body.get("inviterId") == null ? "" : body.get("inviterId");
        String nickName = body.get("nickName");
        String openid = body.get("openid") == null ? weChatMiniProgramUtil.getOpenId(body.get("code")): body.get("openid");

        if (StringUtils.isEmpty(openid)) {
            return AjaxResult.error("登录失败，无法获取openid");
        }

        // 2. 根据 openid 查询用户，若无则自动注册
        SysUser user = userService.selectUserByOpenid(openid);
        if (user == null) {
            SysRole role = new SysRole();
            role.setRoleName("普通用户");
            role.setRoleKey("general");
            role.setRoleId(2L);
            user = new SysUser();
            user.setUserName("wx_" + openid); // 用户名生成策略
            user.setNickName(nickName);
            user.setOpenid(openid); // 若依需要你在 SysUser 表添加 openid 字段
            user.setPassword(SecurityUtils.encryptPassword("123456")); // 可生成随机密码
            user.setRoleIds(new Long[]{2L}); // 默认角色（根据你系统定义）
            user.setRoles(Lists.newArrayList(role));
            user.setDeptId(200L);
            user.setUserType(SysUserTypeEnum.GENERAL.getValue());
            user.setInviterId(inviterId);
            userService.insertUser(user);
            AsyncManager.me().execute(AsyncFactory.generateInviterCode(user.getUserId().toString(), openid), 1000);
        }

        // 3. 登录并生成 token（使用若依已有的 TokenService）
        LoginUser loginUser = new LoginUser(user, new HashSet<>());
        TokenPair tokenPair = tokenService.createTokenPair(loginUser);
        return AjaxResult.success(tokenPair);
    }

    @PostMapping("/checkCodeBind")
    public AjaxResult checkCodeBind(@RequestBody Map<String, String> body){
        String openid = weChatMiniProgramUtil.getOpenId(body.get("code"));
        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("openid", openid);
        boolean exists = userService.getBaseMapper().exists(queryWrapper);
        Map<String, Object> result = new HashMap<>();
        result.put("openid", openid);
        result.put("isExists", exists);
        return success(result);
    }
}
