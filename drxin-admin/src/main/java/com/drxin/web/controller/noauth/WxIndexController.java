package com.drxin.web.controller.noauth;

import com.drxin.bizz.domain.Course;
import com.drxin.bizz.service.ICourseService;
import com.drxin.common.annotation.Anonymous;
import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Anonymous
@RestController
@RequestMapping("/wx/public/index")
public class WxIndexController extends BaseController {
    @Resource
    private ICourseService courseService;

    // 获取最新三个课程时间
    @GetMapping("/latestCourse")
    public R<List<Course>> getLatestCourseTime() {
        return R.ok(courseService.getLatestCourse());
    }

    // 获取当月课程
    @GetMapping("/currentMonthCourse")
    public R<?> getCurrentMonthCourse() {
        return R.ok(courseService.getCurrentMonthCourse());
    }


}
