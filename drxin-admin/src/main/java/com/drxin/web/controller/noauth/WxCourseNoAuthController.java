package com.drxin.web.controller.noauth;

import com.drxin.common.annotation.Anonymous;
import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.R;
import com.drxin.common.core.page.TableDataInfo;
import com.drxin.wechat.domain.WxCourse;
import com.drxin.wechat.service.IWxCourseService;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/wx/public/course/")
@Anonymous
public class WxCourseNoAuthController extends BaseController {
    @Resource
    private IWxCourseService wxCourseService;
    /**
     * 课程信息管理列表
     */
    @RequestMapping("/list")
    public TableDataInfo list(WxCourse course) {
        startPage();
        // 调用服务层方法获取课程列表
        List<WxCourse> list = wxCourseService.selectWxCourseList(course);
        return getDataTable(list); // 这里需要替换为实际的业务逻辑
    }

    /**
     * 获取课程详细信息
     */
    @RequestMapping("/getCourseById/{id}")
    public R<WxCourse> getCourseById(@PathVariable("id") Long id) {
        WxCourse course = wxCourseService.getWxCourseById(id);
        if (course != null) {
            return R.ok(course);
        } else {
            return R.fail("课程信息不存在");
        }
    }
}
