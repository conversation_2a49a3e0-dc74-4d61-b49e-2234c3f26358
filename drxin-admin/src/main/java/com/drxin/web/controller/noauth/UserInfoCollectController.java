package com.drxin.web.controller.noauth;

import com.drxin.bizz.domain.UserInfoCollection;
import com.drxin.bizz.service.IUserInfoCollectionService;
import com.drxin.common.annotation.Anonymous;
import com.drxin.common.core.domain.AjaxResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/public")
@Anonymous
public class UserInfoCollectController {

    private static final Logger logger = LoggerFactory.getLogger(UserInfoCollectController.class);

    @Autowired
    private IUserInfoCollectionService userInfoCollectionService;

    @PostMapping("/submitUserInfo")
    public AjaxResult submitUserInfo(@RequestBody UserInfoCollection userInfoCollection) {
        try {

            userInfoCollectionService.insertUserInfoCollection(userInfoCollection);
            return AjaxResult.success("信息提交成功");

        } catch (Exception e) {
            logger.error("提交失败: ", e);
            return AjaxResult.error("提交失败: " + e.getMessage());
        }
    }
}
