package com.drxin.web.controller.system;

import java.util.List;
import java.util.Set;

import com.drxin.common.core.domain.model.LoginUser;
import com.drxin.common.core.domain.model.TokenPair;
import com.drxin.common.core.domain.vo.SysUserVo;
import com.drxin.common.utils.StringUtils;
import com.drxin.common.utils.bean.BeanUtils;
import com.drxin.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.drxin.common.constant.Constants;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.common.core.domain.entity.SysMenu;
import com.drxin.common.core.domain.entity.SysUser;
import com.drxin.common.core.domain.model.LoginBody;
import com.drxin.common.utils.SecurityUtils;
import com.drxin.framework.web.service.SysLoginService;
import com.drxin.framework.web.service.SysPermissionService;
import com.drxin.framework.web.service.TokenService;
import com.drxin.system.service.ISysMenuService;

import javax.servlet.http.HttpServletRequest;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@RestController
public class SysLoginController {
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private ISysUserService userService;

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody) {
        // 生成双token
        TokenPair tokenPair = loginService.loginWithTokenPair(loginBody.getUsername(), loginBody.getPassword(),
                loginBody.getCode(), loginBody.getUuid());
        return AjaxResult.success(tokenPair);
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = userService.selectUserById(loginUser.getUser().getUserId());
        SysUserVo sysUserVo = new SysUserVo();
        BeanUtils.copyBeanProp(sysUserVo, user);
        sysUserVo.setRoles(user.getRoles());
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        if (!loginUser.getPermissions().equals(permissions)) {
            loginUser.setPermissions(permissions);
            tokenService.refreshToken(loginUser);
        }
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", sysUserVo);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }



    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters() {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }

    /**
     * 获取验证码
     *
     * @return 验证码
     */
    /**
     * 刷新token
     */
    @PostMapping("/refresh")
    public AjaxResult refresh(HttpServletRequest request) {
        String refreshToken = request.getHeader("refresh-token");

        if (StringUtils.isEmpty(refreshToken)) {
            return AjaxResult.error("刷新token不能为空");
        }

        // 移除token前缀
        if (refreshToken.startsWith("Bearer ")) {
            refreshToken = refreshToken.replace("Bearer ", "");
        }

        TokenPair newTokenPair = tokenService.refreshAccessToken(refreshToken);

        if (newTokenPair == null) {
            return AjaxResult.error("刷新token失败，请重新登录");
        }

        return AjaxResult.success("刷新成功", newTokenPair);
    }

    /**
     * 获取用户贡献值
     *
     * @return 用户贡献值
     */
    @GetMapping("/getContribution")
    public AjaxResult getUserContribution() {
        Long userId = SecurityUtils.getUserId();
        String contribution = userService.selectUserContributionById(userId);

        // 如果贡献值为空，返回0
        if (StringUtils.isEmpty(contribution)) {
            contribution = "0";
        }
        return AjaxResult.success("获取成功", contribution);
    }
}
